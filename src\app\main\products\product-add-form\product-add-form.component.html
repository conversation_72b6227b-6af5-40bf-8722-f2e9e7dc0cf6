<div class="modal-header">
  <h4
    class="modal-title"
    id="myModalLabel1">
    <div *ngIf="isEditModal == 'Add'" >
      <span *ngIf="addData.deploymentMode==1">{{ 'PRODUCTS.ADDRADIOACCESSNETWORK' | translate }}</span>
      <span *ngIf="addData.deploymentMode==2">{{ 'PRODUCTS.ADDWIFIAPNETWORK' | translate }}</span>
      <span *ngIf="addData.deploymentMode==3">{{ 'PRODUCTS.ADDWIFIMESHNETWORK' | translate }}</span>
      <span *ngIf="!addData.deploymentMode">{{ 'PRODUCTS.ADD' | translate }}</span>
    </div>
    <div *ngIf="isEditModal == 'Edit'">
      <span *ngIf="addData.deploymentMode==1" class="modal-title-name" >{{ 'PRODUCTS.EDITRAN' | translate }}</span>
      <span *ngIf="addData.deploymentMode==2" class="modal-title-name" >{{ 'PRODUCTS.EDITAPN' | translate }}</span>
      <span *ngIf="addData.deploymentMode==3" class="modal-title-name" >{{ 'PRODUCTS.EDITMESH' | translate }}</span>
      <span *ngIf="!addData.deploymentMode" class="modal-title-name" >{{ 'PRODUCTS.EDITPRODUCT' | translate }}</span>
      <span *ngIf="addData.name" class="ml-75 badge badge-light-info">{{ addData.name }}</span>
    </div>
  </h4>
  <button
    type="button"
    class="close"
    (click)="modalConfig.dismiss('Cross click')"
    aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div
  class="modal-body"
  tabindex="0"
  ngbAutofocus>
  <section class="modern-horizontal-wizard">
    <div
      id="AddProductFrom"
      class="bs-stepper wizard-modern modern-wizard-example">
      <div
        class="bs-stepper-header justify-content-left"
        [class]="isEditModal == 'Add' ? 'd-none' : 'd-flex'">
        <div
          class="step"
          [hidden]="isEditModal == 'Add'"
          data-target="#product-details"
          class="step"
          [class]="isEditModal == 'Add' ? '' : 'cursor-pointer'"
          (click)="isEditModal == 'Add' ? null : stepNavigate()">
          <button class="step-trigger">
            <span
              class="bs-stepper-box"
              [hidden]="isEditModal == 'Add'">
              1
            </span>
            <span class="bs-stepper-label">
              <span class="bs-stepper-title">
                <!-- {{ 'PRODUCTS.PRODUCTDETAILS' | translate }} -->
                {{ 'PRODUCTS.DETAILS' | translate }}
              </span>
            </span>
          </button>
        </div>
        <div
          class="line"
          [hidden]="isEditModal == 'Add'">
          <i
            data-feather="chevron-right"
            class="font-medium-2"></i>
        </div>
        <div
          class="step cursor-pointer"
          (click)="stepNavigate(ADDPRODUCTValidationForm)"
          data-target="#summary-Report"
          [hidden]="isEditModal == 'Add'">
          <button class="step-trigger">
            <span class="bs-stepper-box">2</span>
            <span class="bs-stepper-label">
              <span class="bs-stepper-title">
                {{ 'PROVISIONING.SUMMARYREPORT' | translate }}
              </span>
            </span>
          </button>
        </div>
      </div>
      <div class="bs-stepper-content">
        <form
          class="form form-vertical"
          (ngSubmit)="(ADDPRODUCTValidationForm.form.valid)"
          #ADDPRODUCTValidationForm="ngForm">
          <div
            id="product-details"
            class="content">
            <div class="row">
              <div class="col-md-12">
                <div class="media">
                  <div *ngIf="previewImg && !previewImg.startsWith('database'); else customAvatar">
                    <img
                      class="user-avatar users-avatar-shadow rounded mr-2"
                      style="object-fit: contain;"
                      [src]="previewImg"
                      height="90"
                      width="90"
                      alt="Product avatar">
                  </div>
                  <ng-template #customAvatar>
                    <div class="mr-1 ml-0 bg-light-secondary">
                      <div class="rounded p-3"></div>
                    </div>
                  </ng-template>
                  <div class="media-body mt-50">
                    <!-- <h4>
                      {{ 'PRODUCTS.PRODUCTPICTURE' | translate }}
                    </h4> -->
                    <h4>
                      {{ 'PRODUCTS.PICTURE' | translate }}
                    </h4>
                    <div class="col-12 d-flex mt-1 px-0">
                      <div
                        ngbDropdown
                        container="body">
                        <button
                          ngbDropdownToggle
                          [disabled]="noPermission"
                          type="button"
                          class="hide-arrow btn btn-primary mr-75 mb-0"
                          rippleEffect>
                          <span class="d-none d-sm-block">{{ 'USERS.CHANGE' | translate }}</span>
                        </button>
                        <div
                          ngbDropdownMenu
                          aria-labelledby="dropdownMenuButton">
                          <a
                            ngbDropdownItem
                            (click)="openSelectImg(modalImg)">
                            <span
                              [data-feather]="'image'"
                              [size]="16"
                              [class]="'mr-50'"></span>
                            {{ 'PRODUCTS.BYDEFAULT' | translate }}
                          </a>
                          <label
                            ngbDropdownItem
                            style="font-size: 1rem;"
                            for="change-picture">
                            <input
                              #selectedImage
                              class="form-control"
                              type="file"
                              id="change-picture"
                              hidden
                              [accept]="allowedFileTypes"
                              (change)="productImgChange($event)">
                            <span
                              [data-feather]="'upload-cloud'"
                              [size]="16"
                              [class]="'mr-50'"></span>
                            {{ 'PRODUCTS.BYUPLOAD' | translate }}
                          </label>
                        </div>
                      </div>
                      <button
                        [disabled]="noPermission"
                        class="btn btn-outline-secondary d-none d-sm-block"
                        (click)="resetPic()"
                        rippleEffect>
                        {{ 'ALARMS.REMOVE' | translate }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <!-- <label for="product-name-vertical">
                    {{ 'PRODUCTS.PRODUCTNAME' | translate }}
                  </label> -->
                  <label  for="product-name-vertical">
                    {{ 'PRODUCTS.NAME' | translate }}
                  </label>
                  <span class="text-warning ml-50">*</span>
                  <app-custom-input
                    [content]="addData.name"
                    [trimAllowed]="true"
                    [required]="true"
                    [errMsgType]="'outside'"
                    [validatorItem]="'name'"
                    [customName]="'COMMON.NAME' | translate"
                    [placeholder]="addData.deploymentMode? ('PRODUCTS.NETWORKNAME' | translate):('PRODUCTS.NAMETOOLTIP' | translate)"
                    [disabled]="isEditModal == 'Edit'"
                    (checkValidEvt)="inputingChange($event,'name')"></app-custom-input>
                  <!-- <input
                    type="text"
                    id="product-name-vertical"
                    class="form-control"
                    name="{{ 'COMMON.NAME' | translate }}"
                    [class.error]="nameRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted"
                    [(ngModel)]="addData.name"
                    #nameRequiredFieldRef="ngModel"
                    required
                    [disabled]="isEditModal == 'Edit'"
                    trim
                    placeholder="{{ 'PRODUCTS.PRODUCTNAME' | translate }}">
                  <small
                    class="form-text text-danger"
                    *ngIf="nameRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted">
                    {{ 'PRODUCTS.ISPRODUCTNAME' | translate }}
                  </small> -->
                </div>
              </div>
              <div class="col-md-6">
                <label>{{ 'DEVICES.PROTOCOL' | translate }}</label>
                <!-- <span class="text-warning ml-50">*</span> -->
                <ng-select
                  class="column-select-filter"
                  [clearable]="false"
                  [searchable]="false"
                  [closeOnSelect]="true"
                  [disabled]="isEditModal == 'Edit'"
                  (change)="protocolChange($event)"
                  [class.error]="modernTypeRef.invalid && ADDPRODUCTValidationForm.submitted"
                  [items]="protocolOption"
                  [(ngModel)]="addData.protocol"
                  bindLabel="name"
                  bindValue="value"
                  labelForId="modernType"
                  #modernTypeRef="ngModel"
                  required
                  name="modernType"
                  placeholder="{{ 'DEVICES.SELECTPROTOCOL' | translate }}">
                  <ng-template
                    ng-label-tmp
                    let-item="item"
                    let-clear="clear">
                    <span class="ng-value-label">{{ item.name }}</span>
                    <span
                      class="ng-value-icon right"
                      (click)="clear(item)"
                      inert>
                      ×
                    </span>
                  </ng-template>
                </ng-select>
                <small
                  class="form-text text-danger"
                  *ngIf="modernTypeRef.invalid && ADDPRODUCTValidationForm.submitted">
                  The Protocol is required!
                </small>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="oui-vertical">OUI</label>
                  <span class="text-warning ml-50">*</span>
                  <app-more-info [tooltip]="ouiDescription"></app-more-info>
                  <app-custom-input
                    [content]="addData.oui"
                    [required]="true"
                    [errMsgType]="'outside'"
                    [validatorItem]="'OUI'"
                    [customName]="'oui'"
                    [placeholder]="'PRODUCTS.OUI'| translate"
                    [disabled]="isEditModal == 'Edit'"
                    (checkValidEvt)="inputingChange($event,'oui')"></app-custom-input>
                  <!-- <input
                    type="text"
                    id="oui-vertical"
                    class="form-control"
                    name="oui"
                    [class.error]="ouiRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted"
                    [(ngModel)]="addData.oui"
                    #ouiRequiredFieldRef="ngModel"
                    required
                    [disabled]="isEditModal == 'Edit'"
                    trim
                    placeholder="OUI">
                  <small
                    class="form-text text-danger"
                    *ngIf="ouiRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted">
                    This OUI is required!
                  </small> -->
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="product-class-vertical">
                    {{ 'COMMON.PRODUCT_CLASS' | translate }}
                  </label>
                  <span class="text-warning ml-50">*</span>
                  <app-more-info 
                   [tooltip]="productClassDescription"></app-more-info>
                  <app-custom-input
                    [content]="addData.model"
                    [trimAllowed]="true"
                    [required]="true"
                    [errMsgType]="'outside'"
                    [validatorItem]="'productClass'"
                    [customName]="'productClass'"
                    [placeholder]="'PRODUCTS.PRODUCTCLASS' | translate"
                    [disabled]="isEditModal == 'Edit'"
                    (checkValidEvt)="inputingChange($event,'model')"></app-custom-input>
                  <!-- <input
                    type="text"
                    id="productClass-vertical"
                    class="form-control"
                    name="productClass"
                    [class.error]="productClassRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted"
                    [(ngModel)]="addData.model"
                    #productClassRequiredFieldRef="ngModel"
                    required
                    [disabled]="isEditModal == 'Edit'"
                    trim
                    placeholder="{{ 'COMMON.PRODUCT_CLASS' | translate }}">
                  <small
                    class="form-text text-danger"
                    *ngIf="productClassRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted">
                    {{ 'PRODUCTS.ISPRODUCTCLASS' | translate }}
                  </small> -->
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="data-model-vertical">
                    {{ 'DEVICES.SELECTDATAMODEL' | translate }}
                  </label>
                  <!-- <span class="text-warning ml-50">*</span> -->
                  <ng-select
                    name="selectDataModel"
                    [clearable]="false"
                    [searchable]="false"
                    [class.error]="dataModelRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted"
                    [(ngModel)]="addData.rootObject"
                    [disabled]="isEditModal == 'Edit'"
                    [closeOnSelect]="true"
                    #dataModelRequiredFieldRef="ngModel"
                    placeholder="{{ 'DEVICES.SELECTDATAMODEL' | translate }}"
                    required>
                    <ng-option
                      *ngFor="let item of selectedDataModel"
                      [value]="item.value">
                      {{item.name}}
                    </ng-option>
                  </ng-select>
                  <small
                    class="form-text text-danger"
                    *ngIf="dataModelRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted">
                    {{ 'PROVISIONING.ISDATAMODEL' | translate }}
                  </small>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="cpe-limit-vertical">
                    {{ 'PRODUCTS.DEVICELIMITS' | translate }}
                  </label>
                  <span class="text-warning ml-50">*</span>
                  <app-more-info *ngIf="addData.deploymentMode"
                   [tooltip]="netDeviceLimitDescription"></app-more-info>
                   <app-more-info *ngIf="!addData.deploymentMode"
                   [tooltip]="proDeviceLimitDescription"></app-more-info>
                  <!-- <input
                    type="number"
                    id="cpe-limit-vertical"
                    class="form-control"
                    name="cpeLimit"
                    [class.error]="cpeRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted"
                    [(ngModel)]="addData.cpeLimit"
                    #cpeRequiredFieldRef="ngModel"
                    required
                    trim
                    placeholder="{{ 'PROVISIONING.CPELIMIT' | translate }}">
                  <small
                    class="form-text text-danger"
                    *ngIf="cpeRequiredFieldRef.invalid && ADDPRODUCTValidationForm.submitted">
                    {{ 'PROVISIONING.ISCPELIMIT' | translate }}
                  </small> -->
                  <app-custom-input
                    [content]="addData.cpeLimit"
                    [required]="true"
                    [errMsgType]="'outside'"
                    [customName]="'cpeLimit'"
                    [validatorItem]="'cpeLimit'"
                    [disabled]="noPermission"
                    [placeholder]="addData.deploymentMode? ('PRODUCTS.NETCPELIMIT' | translate):('PRODUCTS.PROCPELIMIT' | translate)"
                    (checkValidEvt)="inputingChange($event,'cpeLimit')"></app-custom-input>
                </div>
              </div>
              <div
                class="col-md-6"
                *ngIf="isEditModal == 'Add' && addData.protocol == 'usp'">
                <div class="form-group">
                  <label for="scope-vertical">
                    {{ 'PROVISIONING.PUBLICTOPIC' | translate }}
                  </label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span
                        class="input-group-text"
                        id="devices">
                        devices/
                      </span>
                    </div>
                    <ng-select
                      name="publishTopic"
                      (change)="publishTopicChange($event)"
                      [(ngModel)]="addData.publishTopic"
                      required
                      style="flex: 1;"
                      bindLabel="name"
                      bindValue="name"
                      [multiple]="true"
                      [closeOnSelect]="false"
                      [searchable]="false"
                      placeholder="{{ 'PROVISIONING.SELECTPUBLICTOPIC' | translate }}">
                      <ng-option
                        *ngFor="let item of topicList"
                        [value]="item">
                        {{item}}
                      </ng-option>
                    </ng-select>
                  </div>
                </div>
              </div>
              <div
                class="col-md-6"
                *ngIf="isEditModal == 'Add' && addData.protocol == 'usp'">
                <div class="form-group">
                  <label for="scope-vertical">
                    {{ 'PROVISIONING.SUBSCRIBETOPIC' | translate }}
                  </label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span
                        class="input-group-text"
                        id="usp">
                        usp/
                      </span>
                    </div>
                    <ng-select
                      name="subscribeTopic"
                      [(ngModel)]="addData.subscribeTopic"
                      (change)="subscribeTopicChange($event)"
                      bindLabel="name"
                      bindValue="name"
                      required
                      style="flex: 1;"
                      [multiple]="true"
                      [closeOnSelect]="false"
                      [searchable]="false"
                      placeholder="{{ 'PROVISIONING.SELECTTOPIC' | translate }}">
                      <ng-option
                        *ngFor="let item of topicList"
                        [value]="item">
                        {{item}}
                      </ng-option>
                    </ng-select>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="cpe-limit-vertical">
                    {{ 'PRODUCTS.PERMITTEDTYPE' | translate }}
                  </label>
                  <!-- <span class="text-warning ml-50">*</span> -->
                  <app-more-info *ngIf="addData.deploymentMode"
                   [tooltip]="netAccessControlDescription"></app-more-info>
                   <app-more-info *ngIf="!addData.deploymentMode"
                   [tooltip]="proAccessControlDescription"></app-more-info>
                  <ng-select
                    name="allowType"
                    [(ngModel)]="addData.type"
                    [disabled]="noPermission||addData.deploymentMode"
                    bindLabel="key"
                    bindValue="value"
                    required
                    style="flex: 1;"
                    [closeOnSelect]="true"
                    [clearable]="false"
                    [searchable]="false"
                    placeholder="{{ 'PRODUCTS.SELECTTYPE' | translate }}">
                    <ng-option
                      *ngFor="let item of typeList"
                      [value]="item.value">
                      {{item.key}}
                    </ng-option>
                  </ng-select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="cpe-limit-vertical">
                    {{ 'PRODUCTS.PROVISIONINGTYPE' | translate }}
                  </label>
                  <!-- <span class="text-warning ml-50">*</span> -->
                  <app-more-info 
                   [tooltip]="provisioningTypeDescription"></app-more-info>
                  <div class="input-group">
                    <ng-select
                      name="provisioningType"
                      [(ngModel)]="addData.provisioningType"
                      bindLabel="key"
                      bindValue="value"
                      required
                      style="flex: 1;"
                      [closeOnSelect]="true"
                      [clearable]="false"
                      [searchable]="false"
                      [disabled]="noPermission || addData.deploymentMode==2 || addData.deploymentMode==3"
                      (change)="provisioningTypeChange()"
                      placeholder="{{ 'PRODUCTS.SELECTTYPE' | translate }}">
                      <ng-option
                        *ngFor="let item of provisioningTypeList;"
                        [value]="item">
                        {{item}}
                      </ng-option>
                    </ng-select>
                    <div class="input-group-append" *ngIf="showInitDefault(addData.provisioningType)">
                      <button
                        class="btn btn-primary feather icon-edit"
                        placement="top-right"
                        ngbTooltip="{{ 'PRODUCTS.EDITPROVISIONINGDEFAULTVALUE' | translate }}"
                        (click)="openModal(modalInitDefault)"
                        type="button"
                        rippleEffect></button>
                    </div>
                  </div>
                </div>
              </div>
               <!-- Tag -->
            <div class="col-md-6 ">
              <label>{{'GROUPS.TAGS'|translate}}</label>
              <div class="form-group" >
                <div class=" input-group d-flex w-100">
                  <div class="flex-grow-1">
                    <app-custom-input
                    [content]="newTag"
                    [errMsgType]="'outside'"
                    [trimAllowed]="true"
                    [disabled]="noPermission"
                    [validatorItem]="'productTag'"
                    [customName]="'productTag'"
                    [placeholder]="'GROUPS.INPUTTAGNAME' | translate"
                    (checkValidEvt)="inputingChange($event,'newTag')"></app-custom-input>
                  </div>
                  <div class="input-group-append">
                    <button
                      class="btn btn-primary btn-sm "
                      type="button"
                      style="height: 38px;"
                      rippleEffect
                      [disabled]="newTag == '' || newTag == null || newTag == undefined || formError?.newTag"
                      (click)="addTag(newTag)">
                      <span [data-feather]="'plus'"></span>
                    </button>
                  </div>
                </div>
              </div>
              <div>
                <div
                  *ngFor="let item of tagArr"
                  class="badge badge-light-success"
                  style="margin: 0px 10px 10px 0px;">
                  <span class="mr-50">{{item.name}}</span>
                  <span
                    class="text-danger"
                    (click)="deleteTag(item)"
                    style="cursor: pointer;">
                    <i data-feather="trash-2"></i>
                  </span>
                </div>
              </div>
            </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="cpe-limit-vertical">
                    {{ 'PRODUCTS.DESCRIPTION' | translate }}
                  </label>
                  <!-- <fieldset class="form-group"> -->
                  <textarea
                    class="form-control"
                    name="{{ 'PRODUCTS.DESCRIPTION' | translate }}"
                    [maxlength]="100"
                    [disabled]="noPermission"
                    placeholder="{{addData.deploymentMode? ('PRODUCTS.NETWORKDESCRIPTION' | translate ):('PRODUCTS.PRODUCTDESCRIPTION' | translate )}}"
                    [(ngModel)]="addData.description"></textarea>
                  <small class="textarea-counter-value float-right">
                    <span
                      *ngIf="addData"
                      class="char-count">
                      {{ addData?.description?.length || 0}}
                    </span>
                    <span
                      *ngIf="!addData"
                      class="char-count">
                      0
                    </span>
                    / 100
                  </small>
                  <!-- </fieldset> -->
                </div>
              </div>
            </div>
          </div>
          <div
            id="summary-Report"
            [hidden]="isEditModal == 'Add'"
            class="content">
            <div class="row">
              <!-- <div class="form-group col-md-6">
                <button
                  type="button"
                  class="btn btn-icon btn-primary"
                  (click)="openAddSummaryModal(modalAddSummary)"
                  rippleEffect>
                  <i
                    data-feather="plus"
                    class="mr-25"></i>
                  <span class="align-middle">{{ 'COMMON.ADDPARAM' | translate }}</span>
                </button>
              </div> -->
              <div class="col-md-12 col-12">
                <ngx-datatable
                  [rows]="summaryReportList"
                  [rowHeight]="40"
                  class="bootstrap core-bootstrap footer-adjust"
                  [limit]="selectedOption"
                  [columnMode]="ColumnMode.force"
                  [headerHeight]="50"
                  [footerHeight]="50"
                  [scrollbarH]="true">
                  <!-- labelName -->
                  <ngx-datatable-column
                    [width]="240"
                    [sortable]="false"
                    name="{{ 'PRODUCTS.LABEL' | translate }}"
                    prop="labelName">
                    <ng-template
                      let-column="column"
                      ngx-datatable-header-template>
                      <input
                        type="text"
                        name="slabelName"
                        class="form-control form-control-sm"
                        [(ngModel)]="searchMap.slabelName"
                        placeholder="{{column.name | titlecase}}"
                        trim
                        (keyup)="filterByInput($event,'slabelName')">
                    </ng-template>
                    <ng-template
                      let-row="row"
                      let-labelName="value"
                      ngx-datatable-cell-template>
                      <div *ngIf="row.isServer">{{labelName}}</div>
                      <div
                        *ngIf="!row.isServer"
                        (click)="openAddSummaryModal(modalAddSummary,row)"
                        class="text-primary cursor-pointer">
                        {{labelName}}
                      </div>
                    </ng-template>
                  </ngx-datatable-column>
                  <!-- referNode -->
                  <ngx-datatable-column
                    [width]="240"
                    [sortable]="false"
                    name="{{ 'PROVISIONING.PARAPATH' | translate }}"
                    prop="referNode">
                    <ng-template
                      let-model="value"
                      ngx-datatable-cell-template>
                          <app-beautify-content
                            [content]="model"
                            [placement]="'top'"></app-beautify-content>
                    </ng-template>
                  </ngx-datatable-column>
                  <!-- source -->
                  <ngx-datatable-column
                    [sortable]="false"
                    name="source"
                    prop="source"
                    [width]="180">
                    <ng-template
                      let-column="column"
                      ngx-datatable-header-template>
                      <select
                        name="sSource"
                        class="form-control form-control-sm"
                        [(ngModel)]="searchMap.sSource"
                        placeholder="{{column.name | titlecase}}"
                        (change)="filterBySource($event)">
                        <option
                          *ngFor="let option of sourceList"
                          [value]="option.value">
                          {{option.name}}
                        </option>
                      </select>
                    </ng-template>
                    <ng-template
                      let-source="value"
                      ngx-datatable-cell-template>
                      <div>{{source}}</div>
                    </ng-template>
                  </ngx-datatable-column>
                  <!-- Active Toggle -->
                  <ngx-datatable-column
                    name="{{ 'PRODUCTS.DATACOLLECT' | translate }}"
                    *ngIf="datacollectionButtonAccess"
                    [sortable]="false"
                    prop="dataCollect"
                    [width]="100">
                    <ng-template
                      let-row="row"
                      let-rowIndex="rowIndex"
                      let-dataCollect="value"
                      ngx-datatable-cell-template>
                      <div
                        *ngIf="row.collection && datacollectionButtonAccess"
                        ngbTooltip="{{ 'PRODUCTS.DATACOLLECTDESCRIPTION' | translate }}"
                        container="body"
                        class="custom-control custom-control-primary custom-switch"
                        style="min-height: 1.7rem;">
                        <input
                          type="checkbox"
                          [checked]="dataCollect"
                          [disabled]="noPermission"
                          (change)="dataCollectChange($event,row)"
                          class="custom-control-input"
                          id="customSwitch{{rowIndex}}">
                        <label
                          class="custom-control-label"
                          for="customSwitch{{rowIndex}}"></label>
                      </div>
                    </ng-template>
                  </ngx-datatable-column>
                  <!-- PM Collect -->
                  <ngx-datatable-column
                    name="Metrics Export"
                    *ngIf="datacollectionButtonAccess"
                    [sortable]="false"
                    prop="pmCollect"
                    [width]="100">
                    <ng-template
                      let-row="row"
                      let-rowIndex="rowIndex"
                      let-pmCollect="value"
                      ngx-datatable-cell-template>
                      <div
                        *ngIf="row.collection && datacollectionButtonAccess"
                        ngbTooltip="{{ 'PRODUCTS.DATAEXPORTDESCRIPTION' | translate }}"
                        container="body"
                        class="custom-control custom-control-primary custom-switch"
                        style="min-height: 1.7rem;">
                        <input
                          type="checkbox"
                          [checked]="pmCollect"
                          [disabled]="noPermission"
                          (change)="pmCollectChange($event,row)"
                          class="custom-control-input"
                          id="pmCustomSwitch{{rowIndex}}">
                        <label
                          class="custom-control-label"
                          for="pmCustomSwitch{{rowIndex}}"></label>
                      </div>
                    </ng-template>
                  </ngx-datatable-column>
                  <!-- action -->
                  <ngx-datatable-column
                    name
                    [width]="80"
                    [sortable]="false">
                    <ng-template
                      ngx-datatable-header-template
                      let-value="value">
                      <button
                        type="button"
                        class="btn btn-icon btn-primary btn-sm ml-1"
                        style="padding: 4px;"
                        [disabled]="noPermission"
                        (click)="openAddSummaryModal(modalAddSummary)"
                        ngbTooltip="{{ 'COMMON.ADDPARAM' | translate }}"
                        container="body"
                        rippleEffect>
                        <i data-feather="plus"></i>
                      </button>
                    </ng-template>
                    <ng-template
                      let-row="row"
                      ngx-datatable-cell-template>
                      <div class="d-flex align-items-center">
                        <button
                          (click)="addAlarm(row, modalEdit)"
                          type="button"
                          class="btn icon-btn btn-sm hide-arrow tableActionButton"
                          ngbTooltip="Add Alarm Notification"
                          container="body"
                          [disabled]="noPermission"
                          *ngIf="notificationButtonAccess && addData.protocol === 'cwmp'"
                          rippleEffect>
                          <i data-feather="mail" class="feather-icon text-primary cursor-pointer" size="16"></i>
                        </button>
                        <button
                          (click)="delModel(row)"
                          [disabled]="row.isServer || noPermission"
                          type="button"
                          class="btn icon-btn btn-sm hide-arrow tableActionButton"
                          [ngClass]="row.isServer ?'text-secondary' : 'text-primary'"
                          ngbTooltip="Delete"
                          container="body"
                          rippleEffect>
                          <i data-feather="trash-2" class="feather-icon" size="16"></i>
                        </button>
                      </div>
                    </ng-template>
                  </ngx-datatable-column>
                </ngx-datatable>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </section>
</div>
<div class="modal-footer">
  <div class="w-100 d-flex justify-content-between">
    <div
      *ngIf="stepperIndex === 0"
      class="col-12 d-flex mt-1"
      [class]="isEditModal == 'Add' ? 'justify-content-end' : 'justify-content-between'">
      <button
        *ngIf="isEditModal != 'Add'"
        class="btn btn-outline-secondary btn-prev"
        disabled
        rippleEffect>
        <i
          data-feather="arrow-left"
          class="align-middle mr-sm-25 mr-0"></i>
        <span class="align-middle d-sm-inline-block d-none">{{ 'COMMON.PREVIOUS' | translate }}</span>
      </button>
      <button
        class="btn btn-primary btn-next"
        (click)="isEditModal == 'Add'? addProduct() : modernHorizontalNext()"
        [disabled]="loading && isEditModal == 'Add'"
        rippleEffect>
        <span class="align-middle d-sm-inline-block d-none">
          <span
            *ngIf="loading && isEditModal == 'Add'"
            class="spinner-border spinner-border-sm mr-1"></span>
          {{isEditModal == 'Add'? ('COMMON.SAVE'| translate) :('COMMON.NEXT'| translate)}}
        </span>
        <i
          *ngIf="isEditModal == 'Add'"
          class="align-middle ml-sm-25 ml-0"></i>
        <i
          *ngIf="isEditModal == 'Edit'"
          data-feather="arrow-right"
          class="align-middle ml-sm-25 ml-0"></i>
      </button>
    </div>
    <button
      *ngIf="stepperIndex === 1"
      class="btn btn-primary btn-prev"
      (click)="modernHorizontalPrevious()"
      rippleEffect>
      <i
        data-feather="arrow-left"
        class="align-middle mr-sm-25 mr-0"></i>
      <span class="align-middle d-sm-inline-block d-none">{{ 'COMMON.PREVIOUS' | translate }}</span>
    </button>
    <button
      *ngIf="stepperIndex === 1"
      class="btn btn-primary btn-next"
      (click)="saveUpdate()"
      [disabled]="loading || noPermission"
      rippleEffect>
      <span class="align-middle d-sm-inline-block">
        <span
          *ngIf="loading"
          class="spinner-border spinner-border-sm mr-1"></span>
        {{ 'COMMON.SAVE' | translate }}
      </span>
    </button>
  </div>
</div>
<!-- Model -->
<ng-template
  #modalAddSummary
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel3">
      {{modalAddSummaryStatus}} {{ 'PROVISIONING.SUMMARYREPORTSETTING' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-product-add-summary-form
      [addData]=addSummaryData
      [modalConfig]=modal
      [productInfo]="addData"
      [summaryReportList]="summaryReportList"
      (transFormDataEvt)="transFormDataCallback($event);"></app-product-add-summary-form>
  </div>
</ng-template>
<!-- Model -->
<ng-template
  #modalImg
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel3">
      {{ 'PRODUCTS.BYDEFAULT' | translate }}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-product-image-select
      [productImg]="previewImg"
      (selectImgObjectEvt)="selectImgObjectEvtCallback($event);"></app-product-image-select>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="modal.close('Close click')">
      {{ 'COMMON.OK' | translate }}
    </button>
  </div>
</ng-template>
<!-- Modal -->
<ng-template
  #modalEdit
  let-modal>
  <app-events-notification-edit-form
    class="modal-content"
    (dismissEvt)="dismiss($event, modal)"
    [productSummaryReport]="productSummaryReport"
    [editData]="editData"></app-events-notification-edit-form>
</ng-template>
<!-- / Modal -->


<!-- Modal -->
<ng-template
  #modalInitDefault
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{addData.provisioningType ? addData.provisioningType:''}}
    </h4>
    <div class="d-flex">
      <button
        type="button"
        class="close"
        (click)="refresh()"
        ngbTooltip="{{ 'PROVISIONING.RESET_TOOLTIP' | translate }}"
        placement="bottom"
        aria-label="Close">
        <span class="text-danger" data-feather="rotate-cw"></span>
      </button>
      <button
        style="margin-left: 1.8rem;"
        type="button"
        class="close"
        (click)="modal.dismiss('Cross click')"
        aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
  </div>
  <div
    id="initDefaultModal"
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-init-provisioning
      [protocol]="addData.protocol" type="product"></app-init-provisioning>
  </div>
  <div class="modal-footer d-flex" style="justify-content: space-between">
    <div>
      <label
        class="btn btn-primary mr-75 mb-0"
        placement="top"
        ngbTooltip="{{ 'PRODUCTS.EXPORTINITDEFAULTTOCSV' | translate }}"
        for="download-default">
        <span class="d-none d-sm-block">{{ 'COMMON.DOWNLOAD' | translate }}</span>
        <input
          class="form-control"
          type="button"
          id="download-default"
          hidden
          (click)="downloadDefault('xlsx')">
      </label>
      <label
        class="btn btn-primary mr-75 mb-0"
        for="upload-default">
        <span class="d-none d-sm-block">{{ 'COMMON.UPLOAD' | translate }}</span>
        <input
          class="form-control"
          type="file"
          id="upload-default"
          hidden
          accept=".csv,.xlsx,.xls"
          (change)="uploadDefault($event)">
      </label>
    </div>
    <div>
      <button
        type="button"
        class="btn btn-primary"
        [disabled]="noPermission"
        (click)="saveInitDefault(modal)">
        {{'COMMON.OK' | translate}}
      </button>
    </div>
  </div>
</ng-template>