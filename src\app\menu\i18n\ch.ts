export const locale = {
  lang: 'ch',
  data: {
    MENU: {
      HOME: 'Home',
      SAMPLE: 'Sample'
    },
    DASHBOARD: {
      TITLE: "仪表板",
      AVGSESSIONS: {
        TITLE: "平均会话",
        VIEWDETAILS: "查看详情",
        LAST1DAY: "过去24小时",
        LAST7DAYS: "最近7天",
        LAST15DAYS: "最近15天",
        LAST28DAYS: "最近28天",
        LAST30DAYS: "最近30天",
        LASTMONTH: "上个月",
        LASTYEAR: "去年"
      },
      MAP: "位置",
      SESSIONDURATION: "会话时长",
      SESSIONDURATIONDESCRIPTION: "所有设备在固定时间间隔的会话时长平均值历史图。会话时长：设备与AMP之间的会话中花费的总时间。",
      SESSIONRATE: "会话速率",
      SESSIONRATEDESCRIPTION: "在线设备的CWMP请求频率历史图。",
      LATENCY: "请求延迟",
      LATENCYDESCRIPTION: "所有设备在固定时间间隔的请求延迟平均值历史图。请求延迟：设备与AMP之间的请求花费的总时间。",
      REGISTERED_COUNT_DISTRIBUTION: "注册设备",
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: "每个产品的注册设备分布图。",
      PROVISIONINGTYPEDISTRIBUTION: "供应类型分配",
      PROVISIONINGTYPEDISTRIBUTION_DESCRIPTION: "每个注册产品选择的所有配置类型的分布。",
      ONLINE_COUNT_DISTRIBUTION: "在线设备",
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: "每个产品的在线设备数量分布图。",
      HISTORYONLINEDEVICE: "在线设备",
      ONLINEDEVICEDESCRIPTION: "每个产品的在线设备数量历史图。",
      SOFTWARE_VERSION_DISTRIBUTION: "软件版本",
      SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION: "在线设备的软件版本分布图。",
      PROVISIONING_CODE_DISTRIBUTION: "配置代码",
      PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION: "在线设备的配置代码分布图。",
      XMPP_STATUS_DISTRIBUTION: "XMPP状态",
      XMPP_STATUS_DISTRIBUTION_DESCRIPTION: "在线设备的XMPP状态分布图。",
      IMS_STATUS_DISTRIBUTION: "IMS状态分布",
      IMS_STATUS_DISTRIBUTION_DESCRIPTION: "在线设备的IMS注册状态分布图。",
      SIM_STATUS_DISTRIBUTION: "SIM状态",
      SIM_STATUS_DISTRIBUTION_DESCRIPTION: "在线设备的SIM连接状态分布图。",
      IPSEC_STATUS_DISTRIBUTION: "IPSec状态",
      IPSEC_STATUS_DISTRIBUTION_DESCRIPTION: "在线设备的IPSec隧道连接状态分布图。",
      TOTAL: "总数",
      ONLINE: "在线",
      ONLINE_DEVICE: "在线设备",
      ONLINE_DEVICE_DESCRIPTION: "在线设备的总数。",
      GROUPS_COUNT: "组",
      GROUPS_COUNT_DESCRIPTION: "组的总数。",
      ONLINE_USERS: "在线用户",
      ONLINE_USERS_DESCRIPTION: "在半小时内登录的用户总数。",
      UE_COUNT: "用户设备",
      UE_COUNT_DESCRIPTION: "连接到小基站的用户设备总数。",
      ALARMS_TOTAL: "告警总数",
      ALARMS_TOTAL_DESCRIPTION: "设备报告的告警总数。",
      ALARMS_SERVERITY: "告警",
      ALARMS_SERVERITY_DESCRIPTION: "不同严重级别的告警总数，如严重、主要、次要和警告。",
      GROUP_LIST: "组列表",
      GROUP_LIST_DESCRIPTION: "详细信息列表，包括设备数量、不同行级别的告警。",
      COVERMAP: "覆盖地图",
      COVERMAP_DESCRIPTION: "显示组内设备的位置和无线覆盖范围。",
      ALARM_LIST: "告警列表",
      ALARM_LIST_DESCRIPTION: "列出所有设备报告的告警，包括已清除和未清除的告警，显示严重级别、事件时间和可能的原因。",
      SYSTEM_EVENT_LIST: "系统事件",
      SYSTEM_EVENT_LIST_DESCRIPTION: "列出所有与通信和访问失败相关的日志事件。",
      STSTEM_INFORMATIONS: "系统信息",
      STSTEM_INFORMATIONS_DESCRIPTION: "AMP系统信息或服务器报告内容。",
      TOTAL_CLIENTS: "WiFi RSSI分布总数",
      TOTAL_CLIENTS_DESCRIPTION: "不同RSSI水平的WiFi客户端分布图。",
      TOTAL_CLIENTS_COUNT: "WiFi客户端",
      TOTAL_CLIENTS_COUNT_DESCRIPTION: "连接到WiFi AP的WiFi客户端总数。",
      EXCELLENT_CLIENTS: "优秀",
      GOOD_CLIENTS: "良好",
      POOR_CLIENTS: "较差",
      STATISTICSOFTOTALCLIENTS: "WiFi RSSI分布记录总数",
      STATISTICSOFTOTALCLIENTS_DESCRIPTION: "不同RSSI水平的WiFi客户端历史图。",
      GROUPNAME: "组名称",
      PRODUCTNAME: "产品名称",
      MANAGEMENTSCOPE: "管理范围",
      REGION: "区域",
      LOCATION: "位置",
      CONFIGURATION: "配置",
      APS: "AP总数",
      TOTALCLIENTS: "总客户端数",
      EXCELLENTCLIENTS: "优秀客户端",
      GOODCLIENTS: "良好客户端",
      POORCLIENTS: "较差客户端",
      EXCELLENT: "优秀",
      GOOD: "良好",
      POOR: "较差",
      EXCELLENT_DESCRIPTION: "RSSI > -65dBm",
      GOOD_DESCRIPTION: "-65dBm < RSSI < -80dBm",
      POOR_DESCRIPTION: "RSSI < -80dBm",
      TOTALCLIENTSTABLE: "客户端表格",
      CLIENTS: "WiFi客户端信息",
      ONLINEAPS: "在线AP",
      TAGS: "标签",
      GROUPSLOCATION: "组位置",
      GROUPSLOCATION_DESCRIPTION: "在地图上显示所有组的位置和基本信息。",
      DEVICESLOCATION: "设备位置",
      DEVICESLOCATION_DESCRIPTION: "在地图上显示所有设备的位置和基本信息。",
    },
    DEVICES: {
      WIFICLIENT: 'WiFi客户端',
      WIFIAPNAME: 'WiFi AP 名称',
      WIFIAPROLE: 'WiFi AP 角色',
      WIFIAPCONFVERSION: 'WiFi AP 配置版本',
      WIFIAPNCONFVERSION: 'WiFi APN 配置版本',
      TAGS: '标签',
      LIST: '设备列表',
      SERIAL_NUMBER: '序列号',
      MODEL_NAME: '型号名称',
      FIRMWARE: '固件',
      LABEL: 'Label',
      GROUP: '分组',
      PRODUCT: '产品',
      LAST_CONNECTED: '上次连接时间',
      LAST_EVENT: '上次事件时间',
      UPTIME: '运行时间',
      TIME_ZONE: '时区',
      ACTIVE: '活动状态',
      BAND: '频段',
      CHANNEL: '信道',
      BANDWIDTH: '带宽',
      UTILIZATION: '利用率',
      RECEIVED: '接收',
      SENT: '发送',
      DOWNLINK_RATE: '下行速率',
      UPLINK_RATE: '上行速率',
      MODE: '模式',
      CONNECTTIME: '连接时间',
      ERRORCODE: '错误代码',
      ERRORDESCRIPT: '错误描述',
      ERRORTIME: '错误时间',
      DAILYSENT: '每日发送量',
      DAILYRECEIVED: '每日接收量',
      ACCESSCOUNT: '访问次数',
      UNINSTALLEDTIME: '卸载时间',
      DATASIZE: '数据大小',
      CACHESIZE: '缓存大小',
      SERVICEDISCOVERYSERVER: '服务发现服务器',
      ACTIVE_BCG_SERVERS: '活跃的BCG服务器',
      POWERCONSUMPTION: '能耗',
      STATE: '状态',
      CLEAREDTIME: '清除时间',
      CONTAINERVERSION: '容器版本',
      APPLICATIONVERSION: '应用程序版本',
      ENABLE: '启用',
      CELL_RESERVED_FOR_OPERATOR_USE: '为运营商保留的小区',
      EUTRA_CARRIER_ARFCN: 'EUTRA载波ARFCN',
      BLACKLISTED: '黑名单',
      VENDORCLASSID: '供应商类别ID',
      EARFCNDOWNLOAD: 'EARFCN 下行载波频点',
      DOWNLOADBANDWIDTH: '下行带宽',
      UPLOADBANDWIDTH: '上行带宽',
      REFERENCESIGNALPOWER: '参考信号功率',
      SECURITY: '安全性',
      SEVERITY: '严重程度',
      ALARMID: '告警ID',
      EVENTTYPE: '事件类型',
      EVENTTIME: '事件时间',
      PROBABLECAUSE: '可能原因',
      SPECIFICPROBLEM: '具体问题',
      ACKUSER: '确认用户',
      ACKTIME: '确认时间',
      ADDITIONALTEXT: '附加文本',
      ADDITIONALINFORMATION: '附加信息',
      PEER: '对等方',
      DURATION: '持续时间',
      CONNECT: '连接',
      START: '开始',
      END: '结束',
      UPLOAD: '上传 ',
      DOWNLOAD: '下载',
      DOWNLOADDATAMODEL: '下载整个数据模型',
      DOWNLOADALL: '下载所有',
      DOWNLOADSELECT: '下载已选择',
      TIME: '时间',
      UPLOADRESULT: '上传测试结果',
      DOWNLOADRESULT: '下载测试结果',
      EVENT: '事件',
      LOGLEVEL: '日志级别',
      REQUEST: '请求',
      CREATED: '已创建',
      IMEI: 'IMEI',
      MAC: 'MAC',
      IP: 'IP',
      RSSI: 'RSSI',
      SSID: 'SSID',
      BSSID: 'BSSID',
      APSN: 'AP S/N',
      APNAME: 'AP Name',
      APMAC: 'AP MAC',
      APIP: 'AP IP',
      LISTDESCRIPTION: '列出所有允许的设备，并提供一般信息，如序号、MAP和IP地址。',
      SMALLCELL_LIST: '小型基站列表',
      SMALLCELL_LISTDESCRIPTION: '列出所有属于无线接入网的允许小型基站，并提供特定信息，如UE、PCI和GNB ID。',
      WIFI_AP_LIST: 'WiFi AP列表',
      WIFI_AP_LISTDESCRIPTION: '列出所有属于WiFi AP网络的允许WiFi AP，并提供特定信息，如客户端、频道和频道利用率。',
      WIFI_MESH_LIST: 'WiFi Mesh列表',
      WIFI_MESH_LISTDESCRIPTION: '列出所有属于WiFi Mesh网络的允许WiFi Mesh AP，并提供特定信息，如客户端、频道和频道利用率。',
      CURRENTNUMBERS: '当前警报数。',
      ALARMMGMTDESCRIPTION: "列出所有由设备报告的警报，包括已清除的、未清除的，并附上严重性、事件时间和可能原因。",
      REGISTERDEVICE: '注册设备',
      REGISTERSMALLCELLDEVICE: '注册小型基站设备',
      REGISTERAPDEVICE: '注册WiFi AP设备',
      REGISTERNESHDEVICE: '注册WiFi Mesh设备',
      LIVEUPDATE: '实时更新',
      SPEEDTEST: '速度测试',
      SPEEDTESTDESCRIPTION: "设备使用TR-143进行上传和下载速度测试，以衡量网络性能，确保数据传输速率最佳。",
      FIVECORE: '5G 核心网',
      FIVECORENETWORK: '5G核心网络',
      FIVECORENETWORK_DESCRIPTION: '提供连接至系统/设置中配置的5G核心的链接，并提供5G核心的URL。',
      CELL_THROUGHPUT: '小区吞吐量',
      CELL_THROUGHPUT_DESCRIPTION: "有关小型基站下载和上传吞吐量的信息。",
      UE_LIST: 'UE列表',
      UE_LIST_DESCRIPTION: '附加到5G核心网络的UE（用户设备）和脱离的UE，提供详细信息，如状态、IMSI、IMEI、GNB ID和IP地址。',
      UE_5QI_PACKET: 'UE 5QI封包',
      UE_5QI_PACKET_DESCRIPTION: '列出UE的5QI封包，包括上行和下行的各种流量指标和丢包率。',
      BULKDATAPROFILE_DESCRIPTION: "列出大容量数据档案，并提供详细信息，如别名、状态、URL、参数和编码类型。",
      SOFTWAREMODULES_DESCRIPTION: "列出软件模块，并提供详细信息，如UUID、别名、名称、URL和最后更新时间。",
      ONLINE_COUNT_DISTRIBUTION: '在线设备',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: '每个产品的在线设备计数分布图。',
      REGISTERED_COUNT_DISTRIBUTION: '注册设备',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: '每个产品的注册设备计数分布图。',
      CONNECTIVITYTEST: '连接测试',
      REBOOT: '重启',
      FACTORYRESET: '应用出厂重置',
      UPLOADLOG: '上传日志',
      UPGRADEFIRMWARE: '升级',
      GENERATEREPORT: '生成报告',
      ADDFILE: '添加文件指针',
      SETTING: '管理偏好设置',
      GENERAL: '一般',
      GENERALSTATUS: '一般状态',
      OPERATION: '分配操作',
      PROTOCOL: '协议',
      SELECTPROTOCOL: '选择协议',
      NETCONFAUTH: 'NETCONF支持密码和私钥验证，请输入密码或私钥。',
      REGISTER: '注册',
      CONNECTIONREQ: '连接请求',
      TELEMETRY: '遥测',
      INFO: '信息',
      MAP: '地图',
      FAPCONNECTEDCLIENTS: 'FAP已连接客户端',
      FAPCONNECTEDCLIENTSDESCRIPTION: '已连接的FAP客户端。',
      GENERALINFO: '一般信息',
      GENERALINFODESCRIPTION: "设备的一般信息，如序号、型号名称和软件版本。",
      CELLULARSTATUS: '移动状态',
      CELLULARSTATUSDESCRIPTION: '移动信息，如服务状态、接入技术、频段、RSRP、RSRQ和RSRI。',
      WORKFLOWLIST: '工作流程列表',
      WORKFLOWLISTDESCRIPTION: '目前对此设备应用的工作流程列表。',
      DATAUSAGE: '移动数据使用量',
      DATAUSAGEDESCRIPTION: '移动数据使用量的历史图表。',
      CLIENTS: '客户端',
      CLIENTDESCRIPTION: '客户端数量，如手机、笔记本电脑、平板电脑。',
      UE: 'UE',
      UEDESCRIPTION: '附加至小型基站的UE总数。',
      SIMCARDINFO: 'SIM卡信息',
      SIMCARDINFODESCRIPTION: 'SIM卡信息，如状态、ICCID、IMSI、IMPI和IMPU。',
      WIFISTATUS: 'WiFi无线状态',
      WIFISTATUSDESCRIPTION: "所有可用WiFi频段/接口的当前无线设置和状态。",
      WIFICHANNELUTLILIZATION: 'WiFi频道利用率',
      WIFICHANNELUTLILIZATIONDESCRIPTION: '所有可用频段的流量所产生的在空频道的负载。',
      CONNECTEDHOSTS: '已连接的主机',
      CONNECTEDHOSTSDESCRIPTION: '有关连接到设备的主机的信息。',
      REGSTATUS: '注册状态',
      REGSTATUSDESCRIPTION: '注册信息，如最后注册时间、最后断开时间和最后断开原因。',
      ERRORSTATUS: '错误状态',
      ERRORSTATUSDESCRIPTION: '设备报告的错误列表，如错误描述和错误时间。',
      SPECTRUMDESCRIPTION: "有关小型基站部署模式的资讯，具体频谱如NR频段、ARFCN和TDD时间槽。",
      APPLIST: '应用列表',
      APPLISTDESCRIPTION: '有关设备应用的资讯。',
      SERVICEPRO: '服务提供者',
      SERVICEPRODESCRIPTION: '设备的服务提供者列表。',
      SPECTRUM: '频谱',
      PLMNNEIGHBORLIST: '邻居/PLMN列表',
      NEIGHBORLIST: '邻居列表',
      NEIGHBORLISTDESCRIPTION: '有关ANR邻居列表、切换列表和PLMN列表的信息。',
      HANDOVERLIST: '已配置邻居列表',
      ANRNEIGHBORLIST: 'ANR邻居列表',
      UTRANEIGHBORLIST: 'Utra邻居列表',
      PLMNLIST: 'PLMN列表',
      APPSTATUS: '设备应用状态',
      APPSTATUSDESCRIPTION: '设备应用的功耗。',
      LXCSTATUS: 'LXC状态',
      LXCSTATUSDESCRIPTION: '设备的Linux容器状态。',
      SUBSCRIPTION: 'USP 订阅',
      SUBSCRIPTIONDESCRIPTION: '通过USP列出订阅项目，提供详细信息，如别名、状态、通知类型和接收者。',
      BULKDATAPROFILE: '大容量数据档案',
      SOFTWAREMODULES: '软件模块',
      CONTROLLERTRUSTROLE: '控制器信任角色',
      CONTROLLERTRUSTROLEDESCRIPTION: '列出包含别名、状态、权限、条目数等详细信息的控制器信任角色',
      FIRMWAREIMAGES: '固件映像',
      FIRMWAREIMAGESDESCRIPTION: '列出包含别名、状态、启动失败日志等详细信息的固件映像',
      BOOTFAILURELOG: "启动失败日志",
      AVAILABLE: "可用",
      DEVICEACTION: '设备操作',
      CURRENTALARMLIST: '当前警报列表',
      ALARMLIST: '警报列表',
      ACKALARM: '确认警报',
      ALARMMGMT: '警报管理',
      ALARMMGMDESCRIPTION: '列出所有由设备报告的警报，包括已清除的、未清除的，并附上严重性、事件时间和可能原因。',
      HISTORYALARM: '历史警报管理',
      CURRENTYALARM: '当前警报列表',
      DATAMODEL: '数据模型',
      SELECTDATAMODEL: '选择数据模型',
      DATANODE: '数据节点',
      DATANODEDESCRIPTION: "显示设备向AMP报告的所有参数节点的树状结构。",
      PARAMETERDATADESCRIPTION: "所选参数节点的详细信息，如子节点、参数名称、属性、路径和值。",
      PARAMETERDATA: '参数数据',
      SELECTDATANODE: '请选择参数从数据节点',
      LOGS: '日志',
      LOG: '日志',
      SESSIONLOG: '会话日志列表',
      SESSIONLOGDESCRIPTION: 'AMP与设备之间的会话日志列表。',
      SESSIONLOGRATE: '报告率',
      SESSIONLOGRATEDESCRIPTION: "过去24小时内，设备向AMP的定期报告率（计数）的统计。",
      SESSLOG: '会话日志',
      PENDINGLOG: '待处理操作日志列表',
      PENDINGLOGPENDINGLOGS: '待设备接收的待处理操作任务的列表。',
      OPERATIONLOGS: '操作日志列表',
      OPERATIONLOGSDESCRIPTION: '设备执行的AMP分配的操作任务列表，以及报告的结果。',
      CALLLOG: '通话日志列表',
      CALLLOGDESCRIPTION: '通话记录的列表，如对等方、类型和持续时间。',
      SPEEDTESTHISTORY: '速度测试历史',
      CONNECTIVITYTESTHISTORY: '连接测试历史',
      CONNECTIVITYTESTHISTORYDESCRIPTION: '连接测试历史。',
      DEVICEREPORTLIST: '设备报告列表',
      DEVICEREPORTLISTDESCRIPTION: '列出生成的摘要报告，并提供设备的关键参数。',
      PMLOG: 'PM KPI日志',
      PMLOGDESCRIPTION: '列出由小型基站报告的KPI数据记录。',
      ADVANCE: '高级',
      CBSDSTATUS: 'CBSD状态',
      CBSDSTATUSDESCRIPTION: '有关公民宽带服务设备的信息，如SAS提供者、CBSD ID、GPS状态和授权状态。',
      CBSDCONDIGS: 'CBSD配置',
      CBSDCONDIGSDESCRIPTION: '公民宽带服务设备的配置，如CBSD序列号、型号和软件版本。',
      TERMINAL: '终端',
      TERMINALDESCRIPTION: '提供支持通过终端进行远程实时操作的设备。',
      COMMANDXML: '命令XML',
      COMMANDXMLDESCRIPTION: '设备命令XML。',
      ACSSTATUS: 'ACS状态',
      DEVICESTATUS: '设备状态',
      SASTATUS: '小区状态',
      RFCONTROL: '小区RF控制',
      RFCONTROLDESCRIPTION: '提供RF相关的设置和开关。',
      ANTENNABEAM: '天线波束',
      BEAMMENU: '波束菜单',
      ANTENNABEAMDESCRIPTION: "提供更改小型基站的天线波束角度。",
      BEAMID: '波束ID',
      GPSANTENNA: 'GPS天线',
      GPSANTENNAPATH: 'GPS天线路径',
      ANTENNAPATH: '天线路径',
      GPSANTENNADESCRIPTION: '提供选择GPS天线路径作为外部或内部。',
      DEPLOYMENTMODE: '部署模式',
      DEPLOYMENTMODEDESCRIPTION: '设备的部署模式。',
      HWMONITOR: '机器诊断',
      HWMONITORDESCRIPTION: "启用小型基站的诊断，如CPU、温度和功耗。",
      CELLULARSTATUS2: 'Cellular状态表',
      RECONNECT: '重连',
      DISCONNECT: '断开连接',
      DOWNLOADLOG: '下载日志',
      REPEATLASTCMD: '重复最新命令',
      CLEARSCROLLBACK: '清除',
      CELLULARDIAGNOSTIC: '请求Cellular接口诊断',
      WIFIDIAGNOSTIC: '请求WIFI接口诊断',
      SYSHEALTHREBOOT: '请求系统运行状态重启',
      DIFFRENTPROTOCOLNOTALLOWED: '不允许批量注册两个或多个不同的协议设备',
      DEVICEERRCODE1: '不允许将设备添加到不存在的产品中',
      DEVICEERRCODE2: '设备已经被注册',
      DEVICEERRCODE3: '无效序列号',
      INVALIDPRODUCT: '无效产品',
      DOFURBISHMENT: '将被翻新，所有设备数据都将被删除。是否仍要继续？',
      CONFURBISHMENT: '翻新过程',
      MAXUES: '最大同时连接用户数:',
      OSMERROR: "地图加载错误。请检查网络连接或稍后重试。",
      GOOGLEMAPERROR: "无法加载 Google Maps API。请检查 Google Maps API 密钥或网络连接。",
      PERMISSIONNUMBEROFENTRIES: "条目数",
      ACTION: {
        ASSIGN_OPERATION: '操作已分配',
        ASSIGN_OPERATION_SUCC: '操作已成功分配。',
        ASSIGN_OPERATION_FAIL: '操作分配失败。',
        ASSIGN_OPERATION_SUCCESS_MESSAGE: '操作成功。',
        ASSIGN_OPERATION_WARNING_MESSAGE1: '等待',
        ASSIGN_OPERATION_WARNING_MESSAGE2: '获取操作。',
        ASSIGN_OPERATION_FAIL_MESSAGE: '操作分配失败。',
        CONFIRM_LIVE_UPDATE: '确认即时更新吗?',
        DO_LIVE: '是否确认要即时更新设备:',
        DO_LIVE_UPDATE: '确定要即时更新选定的设备吗?',
        LIVESUCCESS: '即时更新成功!',
        LIVEFAIL: '即时更新失败!',
        CONFIRM_REBOOT: '重启',
        DOREBOOT: '在重启期间，设备提供的所有服务（包括远程管理）将暂时中断几分钟。',
        SELECTREBOOT: '在重启期间，选定设备提供的所有服务（包括远程管理）将暂时中断几分钟。',
        ABOUT_TO_REBOOT: '即将重新启动...',
        REBOOT_SUCCESS: '重启成功!',
        WAIT_REBOOT: '等待重启...',
        REBOOTFAIL: '重启失败!',
        REBOOT_TIMED_OUT: '重启超时！',
        SHUTDOWN_SUCCESS: '关机成功!',
        CONFIRMFACTORYRESET: '恢复出厂设置',
        DOFACTORYRESET: '设备将恢复到原始制造状态，所有用户设置将被清除。',
        SELECTFACTORYRESET: '选定设备将恢复到原始制造状态，所有用户设置将被清除。',
        ACTION_CONFIRM: '您是否仍然要继续?',
        ABOUT_TO_FACTORYRESET: '即将恢复出厂设置...',
        FACTORYRESETSUCC: '恢复出厂设置成功!',
        FACTORYRESETFAIL: '恢复出厂设置失败!',
        FACTORYRESET_TIMED_OUT: '恢复出厂设置超时！',
        CONFIRMUPLOADLOG: '上传日志',
        DOUPLOADLOG: '请求设备将其系统日志上传到远程URL以进行进一步故障排除。上传URL可以在产品和系统页面的设置文件部分进行配置。',
        SELECTUPLOADLOG: '请求选定设备将其系统日志上传到远程URL以进行进一步故障排除。上传URL可以在产品和系统页面的设置文件部分进行配置。',
        UPLOADLOG: "上传日志",
        UPLOADLOGSUCC: "上传日志成功！",
        UPLOADLOGSUCCMESSAGE: "设备操作已成功创建！",
        UPLOADLOGFAIL: '上传日志失败!',
        ABOUT_TO_UPLOADLOG: '即将上传日志...',
        UPLOADLOG_TIMED_OUT: '上传日志已超时!',
        CONFIRMFIRMWARE: '升级',
        SUCCESSFIRMWARE: '升级成功！',
        UPGRADE_BEING_DOWNLOADED: '正在下载中...',
        ASSIGN_UPGRADE_OPERATION: '分配升级操作',
        UPGRADE_STATUS_INSTALLING: '正在安装...',
        UPGRADE_BEING_ACTIVATED: '正在激活...',
        UPGRADE_DEVICE_BEING_RESTARTED: '设备正在重启...',
        WAIT_UPGRADE: '等待通知...',
        UPGRADING: '正在升级中...',
        UPGRADE_COMPLETE_REBOOTING: '升级完成，正在重启',
        UPGRADE_TIMED_OUT: '升级已超时!',
        UPGRADEFIRMWARE: "升级",
        UPGRADEFIRMWARESUCCMESSAGE: "固件升级的设备操作已成功创建！",
        FAILFIRMWARE: '升级失败！',
        FAILFIRMWAREPENDING: '升级待处理操作日志。',
        REBOOTPENDING: '重新启动在待处理操作日志中。',
        FACTORYRESETPENDING: '恢复出厂设置在待处理操作日志中。',
        UPLOADLOGPENDING: '上传日志在待处理操作日志中。',
        DOFIRMWARE: '在固件升级过程中，设备提供的所有服务（包括远程管理）将暂时中断几分钟。',
        SELECTFIRMWARE: '在固件升级过程中，选定设备提供的所有服务（包括远程管理）将暂时中断几分钟。',
        CONFIRM_GENERATE: '生成',
        CONFIRM_REPORT: '报告',
        CONFIRM_GENERATE_REPORT: '生成报告',
        DO_GENERATE_REPORT: '生成设备关键参数的汇总报告。该报告可以在日志页面的设备报告列表Widget中找到。',
        SELECT_GENERATE_REPORT: '生成选定设备关键参数的汇总报告。该报告可以在日志页面的设备报告列表Widget中找到。',
        SUCCESSGENE: '生成报告成功!',
        FAILGENE: '生成报告失败!',
        CONFIRM_DELETE: '确认删除',
        DO_DELETE: '确认要删除选定的设备吗？',
        CONFIRM_APPLY: '确认应用',
        DO_APPLY_CONFIGRUATION: '您想要应用这个配置吗？',
        IMPORT_JSON_FILE_MESSAGE: '请导入配置 JSON 文件。',
        APPLY_SUCC: '应用成功！',
        APPLY_FAIL: '应用失败！',
        PLESESELECT: '请选择设备',
        CONFIRMReset: '确认重置',
        SUREReset: '确定要重置吗?',
        RESETPERSONALTHEME: '是否确定要重置所有页面的个人主题?',
        RESETCURRENTPERSONALTHEME: '是否确定要重置当前页面的个人主题?',
        RESETALL: '重置所有页面',
        RESETCURRENTPAGE: '重置当前页面',
        RESETSuccess: '重置Widget布局成功',
        CONFIRMSave: '确认保存吗?',
        SURESave: '确定要保存当前的版本吗?',
        SAVESuccess: '保存Widget布局成功',
        DODELETE: '是否要删除: ',
        DELETESUCCESS: '删除成功!',
        DELETEFAIL: '删除失败!',
        CONFIRMBAN: '确认禁止吗?',
        BANSUCCESS: '禁止成功!',
        BANFAIL: '禁止失败!',
        DOBAN: '是否要禁止 ',
        BANSELECT: '是否要禁止选定的设备吗?',
        CONFIRMRegister: '确认注册吗?',
        SUCCESSRegister: '注册设备成功!',
        FAILRegister: '注册设备失败!',
        ONLYVALID: '勾选会自动保留合法的设备',
        DORegister: '是否要注册设备:',
        FAIL: '失败!',
        CONFIRMOPER: '确认删除操作日志',
        OPERSELECT: '是否要删除选定设备的操作日志?',
        CONNECTIVITYTESTSELECT: '您是否要删除所选的连通性测试历史记录？',
        CONNECTIVITYTEAllCONFIRM: '确认清除连接测试历史记录吗？',
        CONNECTIVITYTEAll: '您是否要清除连接测试历史记录？',
        SPEEDTESTSTSELECT: '您是否要删除选定的速度测试历史记录？',
        SPEEDTESTAllCONFIRM: '确认清除速度测试历史记录？',
        SPEEDTESTAll: '您是否要清除速度测试历史记录？',
        PLESEOPER: '请选定完成的操作日志',
        DOTAG: '是否要删除标签',
        TAGSUCC: '删除标签成功!',
        TAGFAIL: '删除标签失败!',
        ADDTAGSUCC: '新增标签成功!',
        ADDTAGFAIL: '新增标签失败!',
        UPDAGEDEVICE: '更新设备!',
        CONFIRMCANCEL: '确认取消操作？',
        DOCANCEL: '是否要取消: ',
        CANCELSUCC: '取消成功!',
        CANCELFAIL: '取消失败!',
        SELECTCANCEL: '是否取消选中的待處理操作日志?',
        PLEASECANCEL: '请选定待處理操作日志',
        CONFIRMREPORT: '确认删除报告',
        SELECTREPORT: '是否要删除所选报表日志?',
        PLEASEREPORT: '请选择报表日志',
        CONFIRMSESSION: '确认删除会话日志',
        SELECTSESSION: '是否要删除选定的会话日志?',
        PLEASESESSION: '请选择会话日志',
        CONFIRMACK: '确认告警?',
        SELECTACK: '是否要确认所有选定的告警?',
        DOACK: '是否要确认当前告警: ',
        CONFIRMSAVELOCA: '确认保存位置',
        DOSAVELOCA: '是否要保存位置?',
        CONFIRMRESETLOCA: '确认重置位置吗？',
        DORESETLOCA: '是否要重置位置吗?',
        SAVESUCC: '保存成功!',
        SAVEFAIL: '保存失败!',
        RESETSUCC: "重置成功！",
        RESETFAIL: "重置失败！",
        ACTIVESUCC: '启用设备成功!',
        ACTIVEFAIL: '启用设备失败!',
        BANDEVSUCC: '禁止设备成功!',
        BANDEVFAIL: '禁止设备失败!',
        WAITOTHER: '等待另一方加入会话...',
        ACSTROUBL: 'ACS加入troubleshooting会话',
        WAITCPE: '等待CPE进入troubleshooting会话',
        ACSCONNECT: 'ACS正在连接troubleshooting会话',
        ACSSETTING: '等待ACS设置troubleshooting会话',
        CPEJOIN: 'CPE加入troubleshooting会话',
        SESSESTABLISH: '会话已创建',
        CONFIRMTROUB: '确认断开troubleshooting',
        DODISCONNECT: '确认断开会话 ',
        CONFIRMUSER: '确认删除用户?',
        DOUSER: '确认删除该用户',
        SUCCESS: '成功!',
        ERROR: '错误!',
        CONFLOGENTRY: '确认删除日志列表',
        UPLOAD_CONNECTIVITY: '上传连通性测试,请稍候...',
        DOWNLOAD_CONNECTIVITYTEST: '下载连通性测试,请稍候...',
        COMPLETE_CONNECTIVITYTEST: '连通性测试完成！',
        CONNECTIVITYTEST_URL: '测试文件网址',
        UPLOAD_SPEEDTEST: '上传速度测试中，请稍候...',
        DOWNLOAD_SPEEDTEST: '下载速度测试中，请稍候...',
        COMPLETE_SPEEDTEST: '速度测试完成！',
        SPEEDTEST_URL: '测试文件URL',
        REQUESTUPDATEFAIL: '请求更新失败',
        CONFVERSION_NOTCHANGED: "配置版本未更改。",
      },
      OPERATION_ACTION: {
        SELECT_OPERATION: '选择配置文件',
        SELECT_WORKFLOW: '选择工作流程',
        SELECT_Action_OR_WORKFLOW: "选择配置文件或工作流程",
        ADD_OPERATION: '添加到配置文件',
        OPERATION_COUNT: '操作数',
        EDIT_OPERATION: '编辑操作',
        EDIT_CONFIGURATION: '编辑配置',
        OPERATION_DETAILS: '操作详情',
        SETUP_OPERATION: '设置配置文件详情',
        ADD_OPERATION_TO: '将操作添加到配置文件',
        OPERATION_ACTIONS: '操作配置文件',
        ENTER_OPERATION_ACTIONS: '输入操作配置文件',
        OPERATION_TYPE: '操作类型',
        SELECT_OPERATIONTYPE: '选择操作类型',
        EDIT_ACTIONS: '编辑操作配置文件',
        MANUAL_OPERATION: '手动配置文件',
        MANUAL_WORKFLOW: '手动工作流程',
        COMPLETION_RATE: '完成率'
      },
      AMP: 'AMP',
      GO: 'Go',
      COLLAPSE: '折叠',
      COLLAPSEALL: '折叠所有',
      EXPAND: '展开',
      EXPANDALL: '展开所有',
      DISCOVER: '发现',
      DISCOVERDATAMODEL: '发现：请求设备更新所选参数',
      PATH: '路径',
      REQUIRED: '必须',
      OPTIONALPARAM: '沒有可选的参数',
      SENDRESP: 'Send Resp',
      ACCESSLIST: '访问列表',
      SELECTACCESSLIST: '选择访问列表',
      REPORTEDVALUE: '值',
      INPUTLIST: '输入列表',
      ADDEDITTAG: '新增/编辑标签',
      ADDTAGS: '新增标签',
      ADDTAG: '添加标签',
      INPUTTAGNAME: '输入标签名',
      TAGSLIST: '标签列表',
      EXISTTAG: '该标签已存在!',
      ADDEDITLABEL: '新增/编辑标牌',
      INPUTLABELNAME: '输入标牌名称',
      LOCATION: '地图',
      HEALTHYSTATUS: '在线率',
      INFORMHISTORY: 'Inform 历史状态',
      SESSIONLOGINTERVAL: '会话日志间隔',
      SESSIONLOGINTERVALDESCRIPTION: "过去24小时内设备向AMP的定期报告间隔的历史图表。",
      TIMEINTERVAL_BETWEEN_CONSECUTIVE_INFORMS: "连续通知之间的时间间隔",
      LOCATIONDESCRIPTION: "在地图上显示设备的位置。",
      ONLINERATE: '在线率',
      ONLINERATEDESCRIPTION: '五角形雷达图显示设备的健康状态。',
      RESET: '重置',
      EDITCOMPONENT: '添加小工具',
      BULKLOGLIST: '批量日志列表',
      EDITFILE: '编辑文件',
      FILTERNODE: '筛选节点',
      SELECTACTION: '对选定设备的操作',
      PRODUCTINFO: '产品信息',
      CONNHISTORY: '连接历史',
      LAST24: '(过去24小时)',
      WIFIUSAGE: 'WiFi频道使用',
      WIFIANALYZER: 'WiFi分析仪',
      WIFIANALYZERDESCRIPTION: '此设备上可用频道/频段的邻近AP的WiFi RSSI图表。',
      CELLSTATUS: '小型基站状态',
      CELLSTATUSDESCRIPTION: "小型基站的状态，例如无线服务状态、PC、gNB ID、MCC、MNC和TAC。",
      CELLHANDOVERSTATUS: '小型基站切换状态',
      CELLHANDOVERSTATUSDESCRIPTION: '小型基站切换状态。',
      COVERMAP: '覆盖地图',
      COVERMAPDESCRIPTION: '显示组内设备的位置和无线覆盖范围。',
      TOTALALARMSDESCRIPTION: "设备报告的警报总数。",
      ALARMSDESCRIPTION: "具有不同严重性的警报总数，例如关键、主要、次要和警告。",
      CREATEMAP: '创建',
      SAVEMAP: '保存',
      SELECTIMAGE: '选择图片',
      IMAGEPRE: '图片预览',
      MAPX: '地图尺寸（X）',
      MAPY: '地图尺寸（Y）',
      EDITMAP: '编辑',
      M: '米',
      ENGMODE: '工程模式',
      CLIMODE: 'CLI模式',
      CONFIG: '配置',
      PROVISIONING: '供给',
      DEVICEALARM24HRS: '24小时内的警报',
      PMALARM24HRS: '在过去24小时内检测到PM数据中的异常。',
      PMSTATUS_GOOD: '过去24小时内没有异常的PM数据。',
      PMSTATUS_BAD: '过去24小时内的PM数据存在异常。',
      PMSTATUS_NORMAL: '过去24小时内没有可用的PM数据。',
      PMSTATUS_NOPERMISSION: '无法访问 PM 数据。',
      ADDTOGROUP: '加入组',
      ADDDEVTOGROUP: '将设备添加到组',
      SELECTDEVICE: '选定设备',
      RESTARTNOW: '现在重启以使更改生效。',
      TAGSDESCRIPTION: '设备上的自定义标签，以便更轻松地管理设备。',
      BATCHSAVE: '批量保存',
      CPUUSAGEDESCRIPTION: "设备的CPU使用百分比。",
      MEMORYUSAGEDESCRIPTION: "设备的内存使用百分比。",
      CPUUSAGECHART_DESCRIPTION: "设备的CPU使用百分比的历史图表。",
      MEMORYUSAGECHART_DESCRIPTION: "设备的内存使用情况的历史图表。",
      KPIDESCRIPTION: "小型基站的KPI的历史图表，如RRC、UE上下文和吞吐量。",
      PMPARAMDESCRIPTION: "小型基站的KPI，如RRC、UE上下文和吞吐量。",
      ALARM_CURRENTNUMBERS: '设备报告的警报总数。',
      PRODUCTMODEL: '产品型号',
      PRODUCTMODELDESCRIPTION: '指示当前设备属于哪个产品。',
      DEVICEOFFLINETIPS: '设备目前离线，AMP保留设备最后报告的信息。',
      DEVICENOTREGISTERTIPS: '该设备是新添加的设备，从未在线/供给过。',
      ONLINESTATUS: '在线状态',
      ONLINESTATUSDESCRIPTION: "设备在线状态的历史图表。",
      REGISTERSINGLEDEVICE: "注册单个设备",
      REGISTERBATCHDEVICE: "注册批处理设备",
      DOWNLOADSESSIONGCSV: '下载 CSV (全部字段)',
      DOWNLOADSESSIONGJSON: '下载 JSON (完整内容)',
      DOWNLOADLATESTSESSIONGJSON: '下载最新会话日志',
      TOTALCLIENTHISTORY: "已连接的WiFi客户端",
      TOTALCLIENTHISTORY_DESCRIPTION: "此设备上关于客户端连接数量的历史图表。",
      CLIENTSPERSSID: '每个 SSID 的 WiFi 客户',
      CLIENTSPERSSID_DESCRIPTION: '此设备上按 SSID 分类的 WiFi 客户连接数量概述的历史图表。',
      CLIENTSPERRADIO: '每个无线电的 WiFi 客户',
      CLIENTSPERRADIO_DESCRIPTION: '此设备上按无线电频率分类的 WiFi 客户连接数量概述的历史图表。',
      TRAFFICPERSSID: '每个 SSID 的 WiFi 流量',
      TRAFFICPERSSID_DESCRIPTION: '此设备上按 SSID 分类的 WiFi 流量概述的历史图表。',
      TRAFFICPERRADIO: '每个无线电的 WiFi 流量',
      TRAFFICPERRADIO_DESCRIPTION: '此设备上按无线电频率分类的 WiFi 流量概述的历史图表。',
      VIEWCHART: '查看图表',
      LOGDETAIL: '日志详细信息',
      TXPOWER: '发射功率',
      BEACONPERIOD: '信标周期',
      SSIDSWITHCLIENTS: 'WiFi SSID分布',
      SSIDSWITHCLIENTS_DESCRIPTION: '此设备上与 WiFi 客户端相关的 SSID 分布网络。',
      RSSIDISTRIBUTION: 'WiFi RSSI 分布',
      RSSIDISTRIBUTION_DESCRIPTION: '此设备上与 WiFi 客户端相关的 RSSI 分布范围。',
      WIFIRADIOTHROUGHPUT: 'WiFi 无线电数据使用情况',
      WIFIRADIOTHROUGHPUT_DESCRIPTION: '显示所有可用 WiFi 频段/接口的当前无线电协议和数据使用情况。',
      OPERATINGCHANNELBANDWIDTH: '带宽',
      BYTESSENT: '已发送',
      BYTESRECEIVED: '已接收',
      PACKETSSENT: '已发送数据包',
      PACKETSRECEIVED: '已接收数据包',
      ERRORSSENT: '发送错误数据包',
      ERRORSRECEIVED: '接收错误数据包',
      SSIDLIST: 'WiFi SSID 清单',
      SSIDLIST_DESCRIPTION: "关于此设备预设SSID的当前WiFi状态，包括启用、WMM、带宽和数据使用情况。",
      MAXCLIENTS: '最大客户端数量',
      WMM: 'WMM',
      UTILIZATION_GREEN: '绿色',
      UTILIZATION_YELLOW: '黄色',
      UTILIZATION_RED: '红色',
      UTILIZATION_GREEN_DESCRIPTION: '利用率<=30%',
      UTILIZATION_YELLOW_DESCRIPTION: '30%<利用率<=60%',
      UTILIZATION_RED_DESCRIPTION: '利用率>60%',
      MCS: 'MCS',
      STATUS: '状态',
      INACTIVE: '未激活',
      DISABLE: '禁用',
      WHITELISTED: '白名单',
      REFURBISHMENT: '翻新',
      NETWORKTOPOLOGY: "网络拓扑",
      NETWORKTOPOLOGY_DESCRIPTION: "以树状视图显示所有相关节点信息。",
      core_5g: {
        ue_info: 'UE信息',
        ue_info_DESCRIPTION: 'UE信息提供了连接到5G核心网络的用户设备（UE）的详细信息。',
      },
      healthyStatus: {
        alarm_description: '在24小时内每发生一次严重警报将扣10分，而每发生一次重大警报将扣5分，以此类推。',
        session_log_rate_description: '设备正常运行周期与过去24小时内提交给AMP的报告之比。',
        online_rate_description: '设备在过去24小时内的在线率，表示设备运行并连接的时间百分比。',
        service_quality_description: "WiFi设备会对每个当前连接的差客户端扣除5分，而小基站通过测量24小时平均RRC速率来评估服务质量。",
        service_active_description: '在过去24小时内每次设备Reboot(Bootstrap)，将扣20分。'
      },
      cableMedemInfo: {
        cable_medem_info: 'DOCSIS 状态',
        cable_medem_info_description: '显示有线接入网络的电缆调制解调器信息'
      },
      ONTMediaInfo: {
        ont_media_info: 'ONT媒体状态',
        ont_media_info_description: '显示ONT设备的媒体信息'
      },
      batteryStatus: {
        battery_information: '电池状态',
        battery_description: '电池信息，如状态、温度和电量'
      },
      WIFIAPINFO: 'WiFi AP 信息',
      WIFIAPINFO_DESCRIPTION: '',
      WIFIAPINFO_APCONFIGVERSION: 'AP 配置',
      APCONFIG: 'AP 配置',
      APNCONFIG: 'APN 配置',
      CONFIGVER: "配置版本",
      WIFIAPINFO_APNAME: 'AP 名称',
      WIFIAPINFO_APROLE: 'AP 角色',
      WIFIAPINFO_APNCONFIGVERSION: 'APN 配置',
      EDITAPNAME: '编辑 AP 名称',
      WIFIAPINFODESCRIPTION: 'WiFi AP 信息，包括 AP 角色、AP 名称、AP 配置版本和 APN 配置版本。',
      FIVEGLICENSE: '单元软件许可证',
      FIVEGLICENSEDESCRIPTION: '小型基站的许可证信息，如到期时间和支持的项目。',
      OPERATION_LOCATION: '配置文件位置',
      OPERATION_SETUPLOCATION: '配置文件设置位置',
      SEARCH_KEYWORD: "搜索关键字",
      SERVICE_STATUS: "服务状态",
      NCI: 'NCI',
      GNB_ID: 'GNB ID',
      GNB_ID_LENGTH: 'GNB ID Length',
      MCC: 'MCC',
      MNC: 'MNC',
      AMF_IP_CONTROL_PLANE: 'AMF IP-Control-Plane',
      CELL_ID: 'Cell ID',
      TAC: 'TAC',
      NSSAI: 'NSSAI',
      PCI: 'PCI',
      PARAMETER: '参数',
      REPORTINTERVAL: '报告间隔',
      TIMEREFERENCE: '时间参考',
      NUMBEROFRETAINEDFAILEDREPROTS: '保留失败报告的数量',
      ENCODINGTYPE: "编码类型",
      REQUESTURIPARAMETER: "请求URI参数",
      NOTIFTYPE: '通知类型',
      REFERENCELIST: '参考列表',
      RECIPIENT: '容器',
      COMPLETED_DESCRIPTION: "设备完成了AMP分配的操作。",
      FAIL_DESCRIPTION: "设备执行的所有操作都失败了。",
      PARTIAL_DESCRIPTION: "设备执行的部分操作失败了。",
      CANCEL_DESCRIPTION: "AMP分配的操作在执行前被取消。",
      PENDING_DESCRIPTION: "等待设备接收AMP分配的操作。",
      INPROCESS_DESCRIPTION: "等待设备向AMP报告执行操作的结果。",
      LOADMAPFAIL: '地图瓦片载入失败，请稍后重试。',
      MISMATCHPROFILE: '参数 "conGlobalProfile" 和 "conDeviceSpecificProfile" 与配置不匹配。',
      ENERGYSAVING: '能源管理',
      STARTTIME_MUST_BE_EARLIER: "开始时间必须早于结束时间",
      STARTDATE_MUST_BE_EARLIER: "开始日期不能晚于结束日期。",
    },
    GROUPS: {
      WIFICLIENT: 'WiFi客户端',
      TITLE: '设备群组',
      TOTAL: '群组总数',
      LIST: '设备群组列表',
      LISTDESCRIPTION: 'AMP 中包含设备的独立单元，负责批量操作、网络状态监控等特定功能。',
      ADDGROUP: '新增群组',
      EDITGROUP: '编辑群组',
      NAME: '群组名',
      ACCEPT: '接受',
      MEMBERSTITLE: '成员',
      MEMBERS: '成员',
      PENDINGLOGS: '待处理日志列表',
      PENDINGLOGSDESCRIPTION: '等待设备接收的待处理操作任务列表。',
      OPERATIONLOGS: '操作日志列表',
      OPERATIONLOGSDESCRIPTION: '由 AMP 分配给群组内设备的操作任务列表及回报的结果。',
      IMPORTTYPE: '选择导入类型',
      SELECTGROUP: '选择设备群组',
      CREATE: '创建',
      ADDDEVICE: '添加设备',
      INPUTTYPE: '选择输入的类型',
      INPUTVALUE: '输入值',
      GROUPUPDATE: '群组更新',
      FILTERING: '清除非法设备',
      ADDTOGROUP: '添加到群组',
      SELECTGROUPTYPE: '选择群组类型',
      SEARCH: '搜索',
      PENDINGOPER: '待办操作',
      OPERLOG: '操作日志',
      PARAMNOTIFICATION: '通知参数',
      SELECTPARAMNOTIFICATION: '选择通知参数',
      NONEXISTINGMEN: '自动过滤不存在的成员',
      BYINPUT: '通过输入',
      BYIMPORTFILE: '通过导入文件',
      ADDMEMBER: '添加成员',
      FILTERMEMBERIN: '产品中的过滤条件',
      ENTERKEYWORDS: '输入关键字',
      BYPRODUCT: '通过产品型号',
      CONFIRM: '确认',
      DEVICECOUNT: '设备',
      ONLINERATE: '在线率',
      SERVICEAVAILABILITY: '服务可用性',
      CREATEBY: '创建者',
      CREATETIME: '创建时间',
      ALARMCOUNT: '告警数量',
      ALARMCOUNTDESCRIPTION: '不同严重程度（如严重、主要、次要和警告）的警报总数。',
      DRAGANDDROP: '拖放所选操作以安排执行操作的顺序',
      GROUPACT: '群组动作',
      ADDMEMBY: '通过导入新增成员',
      ADDMEMINPUT: '通过输入新增成员',
      NETWORKTOPOLOGY: '网络拓扑结构',
      CANTDOOPERATION: '当前组不存在设备',
      ASSIGNOPERATION: '分配操作',
      REASSIGNOPERATION: '重新分配操作',
      DIFFRENTPROTOCOLNOTALLOWED: '不允许将2个以上不同的协议设备加至1个组',
      CONFIRMADDTOGROUPMEMBER: '确认添加到组',
      ONLYVALID: '将只添加合法设备',
      DEVICEERRCODE1: '产品不包含此设备',
      DEVICEERRCODE2: '该设备已存在于组中',
      DEVICEERRCODE3: '该设备是非法的',
      UE: 'UE',
      UEDESCRIPTION: '此群组内附属于小型基站的 UE 总数。',
      ONLINEDEVICESTITLE: '在线设备',
      ONLINEDEVICESDESCRIPTION: '此群组内的在线设备总数。',
      WIFICLIENTDESCRIPTION: '此群组内的 WiFi 客户端总数。',
      ALARMSDESCRIPTION: '此群组内设备报告的警报总数。',
      KPIDESCRIPTION: '此群组内小型基站 KPI（例如 RRC、UE Context 和吞吐量）的历史图表。',
      DEVICESLISTDESCRIPTION: '此群组内的所有设备列表。',
      GROUPSDELETED: '所选组已成功删除。',
      RFMAP_MAX_WARNING: '覆盖图仅支持最多50台设备。',
      SSIDLIST: 'WiFi SSID 列表',
      SSIDLIST_DESCRIPTION: '此群组内的所有 SSID 列表。',
      SSID: 'SSID',
      SECURITY: '安全性',
      CAPTIVEPORTAL: 'Captive Portal',
      MACACL: 'MAC ACL',
      ATF: 'ATF 启用',
      ATFPERCENTAGE: 'ATF',
      BEAMFORMING: '波束成形',
      MAXCLIENTS: '最大客户端数',
      WMM: 'WMM',
      BAND: '频段',
      BANDWIDTHCONTROL: '带宽控制',
      DOWNLOAD: '发送',
      UPLOAD: '接收',
      FOREIGNAPSLIST: '外部 AP 列表',
      FOREIGNAPSLIST_DESCRIPTION: '不包含在此群组中的 WiFi AP 列表。',
      VLAN: 'VLAN',
      BSSID: 'BSSID',
      CHANNEL: '频道',
      RSSI: 'RSSI',
      NEARBYAPS: '附近的 AP',
      TIME: '时间',
      SSIDSWITHCLIENTS: '拥有最多客户端的前 6 名 WiFi SSID',
      SSIDSWITHCLIENTS_DESCRIPTION: '此组中拥有最多客户端连接的前 6 名 WiFi SSID',
      APSWITHCLIENTS: '拥有最多客户端的前 6 名 WiFi AP',
      APSWITHCLIENTS_DESCRIPTION: '按客户端数量排列的前 6 名最受欢迎的 WiFi AP。',
      APSWITHTRAFFIC: '流量最高的前 6 名 WiFi AP',
      APSWITHTRAFFIC_DESCRIPTION: '发送和接收数据最多的六个 WiFi AP 的总数值。',
      CLIENTSPERSSID: "客户端连接的WiFi SSID",
      CLIENTSPERSSID_DESCRIPTION: "针对包含在组中的设备，客户端连接数量按 WiFi SSID分类的历史图表。",
      CLIENTSPERRADIO: "WiFi 客户端连接的頻寬",
      CLIENTSPERRADIO_DESCRIPTION: "针对包含在组中的设备，客户端连接数量按頻寬分类的历史图表。",
      TRAFFICPERSSID: "WiFi SSID的流量",
      TRAFFICPERSSID_DESCRIPTION: "针对包含在组中的设备，流量按SSID分类的历史图表。",
      TRAFFICPERRADIO: "頻寬的流量",
      TRAFFICPERRADIO_DESCRIPTION: "针对包含在组中的设备，流量按无线电频率分类的历史图表。",
      BYTESRECEIVED: '接收',
      BYTESSENT: '发送',
      ENABLE: '启用',
      DISABLE: '禁用',
      TAGSGROUPDESCRIPTION: '针对群组的自定义标签，方便设备管理。',
      COUNTRY: '国家',
      SELECTCOUNTRY: '选择国家',
      STREETADDRESS: '街道地址',
      CITY: '城市',
      STATE: '州',
      ZIPCODE: '邮政编码',
      TAGS: '标签',
      INPUTTAGNAME: '输入标签名',
      ROAD: '道路/街道',
      HOUSE: '建筑号码',
      GROUPDETAILS: '组详细信息',
      GROUPLOCATION: '组位置',
      GROUPLOCATIONDESCRIPTION: '在地图上显示群组位置。',
      EDITLOCATION: '编辑位置',
      INPUTLAT: '输入纬度',
      INPUTLNG: '输入经度',
      RESET: '重置',
      LATLNG: '纬度和经度',
      LAT: '纬度',
      LNG: '经度',
      LOCATION: '位置',
      VIEWLOCATION: '查看群组位置',
      VIEWCOVERAGEMAP: '查看覆盖范围地图',
      VIEWLOCATIONDESCRIB: '当群组位置小工具开启时，点击栏中的图标允许用户查看该群组的位置和信息。',
      VIEWCOVERAGEMAPDESCRIB: '当覆盖范围地图小工具开启时，点击栏中的图标允许用户查看群组中包含的设备的分布和信息。',
      WIDGETNAME: '小工具名称、类别或子类别',
      COVERMAP: '覆盖地图',
      COVERMAPDESCRIPTION: '显示群组内所有设备的位置和无线覆盖范围。',
      TOTALALARMSDESCRIPTION: '此群组内设备报告的警报总数。',
      ALARMMGMT: '警报管理',
      ALARMMGMTDESCRIPTION: '列出此群组内设备报告的所有警报，包括已清除、未清除的警报，以及其严重程度、事件时间和可能原因。',
      NETWORK: '网络'
    },
    PRODUCTS: {
      LIST: '产品列表',
      LISTDESCRIPTION: '包含设备的单位，具有特定功能，如访问列表控制、默认配置参数和用户账户的管理范围。设备注册和上线时必须提供此单位。',
      NETWORKRADIOACCESSLIST: '无线接入网络列表',
      NETWORKRADIOACCESSLISTDESCRIPTION: '用于管理特定设备（如小型基站）的单位。出于网络管理目的，还会创建同名的组。',
      NETWORKWIFIAPLIST: 'WiFi AP 网络列表',
      NETWORKWIFIAPLISTDESCRIPTION: '用于管理特定设备（如 WiFi AP）的单位。出于网络管理目的，还会创建同名的组。',
      NETWORKWIFIMESHLIST: 'WiFi Mesh 网络列表',
      NETWORKWIFIMESHLISTDESCRIPTION: '用于管理特定设备（如 WiFi Mesh AP）的单位。出于网络管理目的，还会创建同名的组。',
      ACTION: '型号动作',
      ADD: '新增型号',
      ADDRADIOACCESSNETWORK: '添加无线接入网络',
      ADDWIFIAPNETWORK: '添加 WiFi AP 网络',
      ADDWIFIMESHNETWORK: '添加 WiFi Mesh 网络',
      PERMISSION: '许可',
      DEVICEPERMISSION: '许可的设备',
      ADDPERMISSION: '允许列表',
      PERMITTEDTYPE: '访问控制 ',
      PROVISIONINGTYPE: '设置类型',
      SELECTTYPE: '选择访问控制 ',
      ALLOWALL: '允许所有设备',
      WHITELIST: '允许列表',
      CREATEWHITELIST: '创建允许列表',
      LABEL: '标签名',
      LABELREQUIRED: '标签名必填!',
      NAMEREQUIRED: '名称必填!',
      PRODUCTTYPE: '型号类型',
      SELECTPRODUCTTYPE: '选择型号类型',
      PRODUCTPICTURE: '型号图片',
      PRODUCTNAME: '型号名',
      ISPRODUCTNAME: '型号名必填!',
      ISPRODUCTCLASS: '型号必填!',
      TARGETPRODUCT: '目标型号名',
      OBJECTNAME: '目标名',
      ENTEROBJECTNAME: '输入目标名',
      PRODUCTDETAILS: '型号详情',
      DETAILS: '详细信息',
      CHOOSEFILE: '选择文件',
      PERMITEDDEVICE: '允许接入设备',
      DEVICELIMITS: '设备数上限',
      UPDATEDTIME: '更新时间',
      EDITPRODUCT: '编辑型号',
      EDITRAN: '编辑无线接入网络',
      EDITAPN: '编辑 WiFi AP 网络',
      EDITMESH: '编辑 WiFi Mesh 网络',
      PRODUCTMODEL: '产品模型',
      PRODUCTMODELDESCRIPTION: '产品模型的图片和说明',
      BYDEFAULT: '选择图片',
      BYUPLOAD: '上传图片',
      DATACOLLECT: '数据收集',
      DATACOLLECTDESCRIPTION: "切换开启以开始收集数据,相关设备的KPI图表Widget可在设备信息页面中创建。",
      DATAEXPORTDESCRIPTION: "打开此选项以将性能指标导出到第三方遥测系统。",
      PRODUCTNOTEXIST: '当前产品不存在',
      DEVICETEXIST: '设备已存在',
      DEVICEERRCODE1: '项目包含非法字符串',
      DEVICEERRCODE2: 'DelItem不存在',
      DEVICEERRCODE3: "该设备已存在于产品：",
      LABELORPATHEXISTS: "标签名称或参数路径已存在",
      PARSINGFAILED: "解析文件失败",
      FILETYPEERROR: "只能上传CSV文件",
      EXPORTINITDEFAULTTOJSON: "下载JSON (配置默认值）",
      EXPORTINITDEFAULTTOCSV: "下载CSV (批量注册示例）",
      EDITPROVISIONINGDEFAULTVALUE: "编辑设置默认值",
      TAGS: '标签',
      LOCATION: '位置',
      REGISTERDEVICE: '注册设备',
      NAME: '名称',
      NAMETOOLTIP: '输入用户定义的产品名称',
      NETWORKNAME: '输入用户定义的网络名称',
      PICTURE: '图片',
      UEFORPRODUCT: '各产品的 UE 数量',
      OUI: '输入正确的OUI以符合设备要求',
      DESCRIPTION: '描述',
      NETWORKDESCRIPTION: '输入网络中设备的特点或专有属性',
      PRODUCTDESCRIPTION: '输入产品中设备的特点或专有属性',
      PRODUCTCLASS: '输入符合设备要求的产品类别',
      PROCPELIMIT: '输入产品的容量',
      NETCPELIMIT: '输入网络的容量',
      OUIDESCRIPTION: 'OUI必须正确以便AMP验证访问的设备',
      ALLOWALLDESCRIPTION: '设备的OUI、产品类别需被验证',
      PROALLOWLISTDESCRIPTION: '设备的OUI、产品类别和序列号需被验证，创建产品后需通过序列号注册设备',
      NETALLOWLISTDESCRIPTION: '设备的OUI、产品类别和序列号需被验证，创建网络后需通过序列号注册设备',
      PRODUCTCLASSDESCRIPTION: '产品类别必须正确，以便AMP验证访问的设备。例如：Femtocell_5G_SA、EAP等',
      PRODEVICELIMITDESCRIPTION: '产品中设备的数量容量',
      NETDEVICELIMITDESCRIPTION: '网络中设备的数量容量',
      PROVISIONINGTYPEDESCRIPTION: '配置类型必须正确，以便AMP验证访问的设备'
    },
    ALARMS: {
      TOTAL: '告警总数',
      ALARMCOUNT: '严重告警/主要告警/次要告警/警告告警 数量',
      CRITICAL: '严重告警',
      MAJOR: '主要告警',
      WARNING: '警告告警',
      INDETERMINATE: '不确定',
      MINOR: '次要告警',
      CURRENTNUMBERS: '当前告警数量',
      SYSTEM: '系统事件',
      LIST: '系统事件列表',
      DEVICE: '设备告警',
      DEVICELIST: '设备告警列表',
      ACKALARM: '确认告警',
      ERRORLIST: '设备错误列表',
      DEVICEEVENTTRACKING: '设备事件跟踪',
      DEVICEEVENTTRACKINGDESCRIPTION: '列出由 AMP 跟踪的与设备相关的重大事件，如重启、离线和重置。',
      NOTIFICATION: '通知',
      NOTIFICATIONLIST: '通知列表',
      NOTIFICATIONLISTDESCRIPTION: '用户创建的规则列表，用于在特定条件下通过 SMTP 或 SNMP Trap 接收通知，基于需求。',
      FORK: '分叉',
      FORKNOTIFICATION: '分叉通知',
      CLONE: '复制',
      CLONENOTIFICATION: '复制通知',
      EDITNOTIFICATION: '编辑通知',
      SETUPDETAILS: '详情',
      ENTERDETAILS: '输入详情信息',
      GENERIC_TRAP_TYPE: "通用陷阱类型",
      SPECIFIC_TRAP_OID: "特定陷阱OID",
      VARIABLE_BINDINGS: '变量绑定（可选）',
      ALIASVALUE: '别名值',
      OlDNAME: '旧名称/名称',
      VARIABLE_BINDINGS_DESCRIPTION: 'Varbinds的值与阶段中配置的别名列表相关联。',
      TARGET: '目标',
      EDITTARGET: '编辑目标',
      SELECTTARGET: '选择目标',
      ENTERTARGET: '输入目标',
      TARGETREQUIRED: '必须输入目标文件名!',
      TARGETTYPE: '目标类型',
      TARGETDEVICESN: '目标设备序列号',
      ISTARGETDEVICESN: '目标设备序列号必填!',
      SCHEDULE: '计划',
      EDITSCHEDULE: '编辑计划',
      ENTERSCHEDULE: '输入计划',
      SCHEDULEDOWNLOAD: '计划下载详情',
      STARTDATE: '开始日期',
      ENDDATE: '结束日期',
      STARTTIME: '开始时间',
      ENDTIME: '结束时间',
      ENTEROPERATION: '输入操作名',
      TRIGGER: '触发类型',
      SELECTTRIGGER: '选择触发类型',
      INFORMPARAM: '告知参数',
      CONDITION: '执行列表',
      SELECTCONDITION: '选择列表',
      BUILDCONDITION: '编辑条件',
      PARAMCONDITION: '参数列表',
      SELECTPARAMCONDITION: '选择参数列表',
      ATTACHED: '附件信息',
      ADDITIONALPARAMINFO: '附加参数信息',
      ADDITIONALPARAMINFO_DESCRIPTION: '更详细的信息，包含通知消息中特定参数值。',
      NODE: '节点',
      ENTERNODE: '输入节点',
      SELECTNODE: '选择节点',
      VIEWNODE: 'NetConf节点视图',
      REFERNODE: '参照节点',
      REFERNODEREQUIRED: '参照节点必填!',
      PARENTNODE: '父节点',
      SELECTPARENTNODE: '选择父节点',
      CHILDNODE: '子节点',
      ADDCHILDNODE: '新增子节点',
      SELECTCHILDNODE: '选择子节点',
      CHILDCONTENT: '子节点内容',
      CONTENT: '内容',
      ENTERCONTENT: '输入内容',
      CONFIG: '配置',
      NAMESPACE: '命名空间',
      ENTERNAMESPACE: '输入命名空间',
      ALIAS: '別名',
      ENTERALIAS: '输入別名',
      ADDATTACHED: '新增附加信息',
      ADDDEVICEFALUT: '新增设备故障参数',
      SELECTDEVICEFAULTNAME: '选择设备故障名称',
      BUILD: '建立操作(可选)',
      BUILDREQUIRED: '操作',
      PROGRESS: '前进',
      ACTIONS: '动作',
      REPEAT: '重演',
      REPEATTYPE: '重复类型',
      UPLOADNOTIFI: '上传告警通知',
      DROPFILE: '在这里放置文件或点击',
      UPLOADFORMATSARESUPPORTED: '仅支持.tar .tar.gz .tgz .zip .gzip 格式上传。',
      UPLOADALL: '上传所有',
      UPLOAURL: '上传URL',
      BANDWIDTH: '上传带宽',
      QUEUEPROGRESS: '队列进展',
      PARAMLIST: '参数列表',
      SELECT: '选择',
      ENTERSELECT: '输入选择(XPath expression)',
      SOURCE: '源',
      SELECTSOURCE: '选择源',
      FILTERSTATE: '过滤状态',
      SELECTFILTERSTATE: '选择过滤状态',
      FILTERTYPE: '过滤类型',
      SELECTFILTERTYPE: '选择过滤类型',
      REMOVEALL: '移除所有',
      REMOVE: '移除',
      UPDATEUSER: '更新用户',
      UPDATEDBY: '更新者',
      LASTACTIVE: '最后活动',
      UPDATELOG: '更新日志',
      NOTIF: '通知',
      ONLYONCE: '仅一次',
      ALLSTATE: '全部状态',
      ALLSEVERITY: '全部严重程度',
      ALLGROUPS: '所有群组',
      SEVERITY: '严重性',
      STATE: '状态',
      CLEAREDTIME: '清除时间',
      CLEARED: '已清除',
      NOTCLEARED: '未清除',
      CLEAREDALARM: '已清除警报',
      UNCLEAREDALARM: '未清除警报',
      CHANGEDALARM: '已更改警报',
      NEWALARM: '新警报',
      NOTIFICATIONTYPE: '通知类型',
      PROBABLECAUSE: '可能原因',
      SPECIFICPROBLEM: '具体问题',
      ADDITIONALTEXT: '附加文本',
      ADDITIONALINFORMATION: '附加信息',
      ALARMID: '报警ID',
      EVENTTYPE: '事件类型',
      EVENTTIME: '活动时间',
      ACKUSER: '确认用户',
      ACKTIME: '确认时间',
      ERRORCODE: '错误',
      DEVICEFAULTPARAM: '设备故障参数',
      BYTAG: '按标签',
      RECEIVERLIST: '接收者列表',
      RECEIVERCCLIST: '抄送者列表',
      RECEIVETAGSTOOLTIP: '添加带有特定标签的接收者',
      RECEIVERCCTAGSTOOLTIP: '添加带有特定标签的抄送者',
      EMAILSUBJECT: '电子邮件主题',
      EMAILCONTENT: '电子邮件内容',
      WITHACTIVEHOURS: '有活跃时间',
      PRIPHONENUM: '主电话号码',
      SECPHONENUM: '辅助电话号码',
      TEXTMESSAGE: '短信',
      ACK: '确认',
      EDIT_STAGE_NOTIFICATIONS: '使用阶段/通知构建通知',
      TOTALALARMSDESCRIPTION: '系统报告的警报总数。',
      ALARMMGMT: '警报管理',
      ALARMMGMTDESCRIPTION: '列出系统报告的所有警报，包括已清除、未清除的警报，并显示严重性、事件时间和可能原因。',
      ALARMCOUNTDESCRIPTION: '不同严重程度（如严重、主要、次要和警告）的警报总数。',
      TARGETDEVICETAG: '目标设备标签',
    },
    PROVISIONING: {
      COLLAPSIBLE: ' 配置供应',
      WORKSFLOW: '流程规划',
      WORKSFLOWLIST: '流程列表',
      WORKSFLOWLISTDESCRIPTION: '用户创建的规则列表，用于在特定条件下对特定设备执行操作，基于需求。',
      CONFIGURATIONS: '配置',
      CONFIGURATIONLIST: '配置列表',
      CONFIGURATIONLISTDESCRIPTION: '自动或用户创建的规则列表，用于在特定条件下对设备执行配置，以满足需求。',
      POLICYLIST: '能源策略',
      POLICYLISTDESCRIPTION: '列出自动创建的节能规则。这些规则在启用节能模式时对设备执行配置设置。',
      CLONEPOLICY: '克隆策略',
      POLICYS: '策略',
      FROMWORKSFLOW: '起始流程',
      FROM: '起始',
      VALIDFROM: '有效起始日期',
      CLONEWORKSFLOW: '复制流程',
      CLONECONFIGURATION: '复制配置',
      EDITWORKSFLOW: '编辑流程',
      FORKWORKSFLOW: '分叉流程',
      UPLOADWORKSFLOW: '上传流程',
      UPLOADQUEUE: '上传队列',
      OPERATIONS: '操作',
      PROFILES: '配置文件',
      ACTIONS: '动作',
      CLONEOPERATIONS: '克隆配置文件',
      FORKOPERATIONS: '分叉配置文件',
      EDITOPERATIONS: '编辑操作',
      OPERATIONLIST: '配置文件列表',
      OPERATIONLISTDESCRIPTION: '用户创建的规则列表，用于在特定条件下对特定设备执行操作，基于需求。',
      DUOPERATION: '新增DU操作',
      PARAPATH: '参数路径',
      ENTERPARAPATH: '输入参数路径',
      ISPARAPATH: '必须输入参数路径.',
      NEXTLEVEL: '跳过',
      PRODUCT: '产品型号',
      SCRIPTS: '脚本',
      SCRIPTLIST: '脚本列表',
      EDITSCRIPT: '编辑脚本',
      SCRIPTNAME: '脚本名',
      FILES: '文件',
      FILELIST: '文件列表',
      FILELISTDESCRIPTION: '用户创建的列表，包含设备上传或下载文件时所需的信息，例如文件类型、URL 和身份验证。',
      FILETYPE: '文件类型',
      SELECTFILETYPE: '选择文件类型',
      ENTERFILETYPE: '输入文件类型',
      ISFILETYPE: '文件类型必填!',
      ISURL: 'URL必填!',
      DELAYSECONDS: '延迟秒数',
      TARGETNAME: '目标文件名称',
      ENTERTARGETNAME: '输入目标文件名称',
      FILESIZE: '文件大小',
      DESCRIPTION: '详情描述',
      SUBSCRIBE: '订阅',
      SUBSCRIBETOPIC: '订阅主题',
      SELECTTOPIC: '选择订阅主题',
      VENDORFILE: '供应商专用文件',
      VENDORFILEDESCRIPTION: '用户创建的列表，包含设备上传或下载文件时所需的厂商特定信息，例如文件类型、URL 和身份验证。',
      ADDVENDORFILE: '新增供应商专用文件',
      EDITVENDORFILE: '编辑供应商专用文件',
      LATESTFIRMWARE: '最新的固件',
      AVAILABLEFILES: '可用文件',
      SETUPDETAILS: '设置详情',
      CODEDISTRIBUTION: '配置代码分发',
      OPERATENAME: '操作名',
      ENTEROPERATENAME: '输入操作名',
      ADDINFORM: '添加Inform参数',
      PUBLICTOPIC: '发布主题',
      SELECTPUBLICTOPIC: '选择发布主题',
      ISDATAMODEL: '数据模型必填!',
      CPELIMIT: 'CPE数目上限',
      ISCPELIMIT: 'CPE数目上限必填!',
      SUMMARYACTION: '汇总活动',
      ADDSUMMARYREPORT: '新增汇总报告',
      SUMMARYREPORT: '汇总报告',
      SUMMARYREPORTSETTING: '汇总报告设置',
      PLEASESELECTTEMPLATE: '请先选择一个模板，然后再继续',
      PLEASEFILLPARAMS: '请先请填写标签名称和参数路径，然后再继续',
      INFORMLIST: 'Inform参数列表',
      SELECTTRIGGERREQ: '选择触发 (必填)',
      DEVICEPARAMLIST: '设备参数列表',
      ADDSTAGE: '阶段',
      ENTERSTAGENAME: '输入阶段名',
      SELECTFILE: '选择文件',
      SUCCESSURL: '成功URL',
      FAILURL: '失败URL',
      NOTIFYTYPE: '通报类型',
      SELECTNOTIFYTYPE: '选择通报类型',
      NOTIFYPARAMS: '通报参数',
      SHOWDETAILS: '显示详情',
      NOTIFYTYPEREQU: '选择通报类型(必选)',
      EDITNOTIFYPARA: '编辑通报参数',
      OBJECTPATH: '目标路径',
      ENTEROBJECTPATH: '输入目标路径',
      ALLOWPAR: '部分允许',
      ADDCREATEOBJ: '添加创建对象',
      ISOBJECTNAME: '目标名必填!',
      FILETARGET: '目标文件',
      SELECTSN: '选择序列号',
      GENERSUMMARYREPORT: '生成汇总报告',
      SCRIPT: '脚本',
      SELECTSCRIPT: '选择脚本',
      ACTIONSLIST: '动作列表',
      PARAMTYPE: '参数类型',
      ADDITIONALCON: '触发条件',
      ADDCONDITION: '添加附加条件',
      EDITCONDITION: '编辑附加条件',
      DEVICEPARAMTRIGGER: '设备参数触发器',
      INFORM: '通知',
      DURATION: '持续时间',
      FIELD: '领域',
      TRIGGEREVENTS: '触发事件',
      TRIGGERTYPE: '触发类型',
      INFORMEVENT: '通知事件',
      SELECTINFORMEVENT: '选择通知事件',
      EVENTNAME: '事件名称',
      ENTEREVENTNAME: '输入活动名称',
      PARAMETERSKEY: '参数键',
      ENTERPARAMETERSKEY: '输入参数键',
      PARAMETERSVALUE: '参数值',
      ENTERPARAMETERSVALUE: '输入参数值',
      PROTOCOLVER: '协议版本',
      SOURCEURL: '来源网址',
      TARGETURL: '目标网址',
      SESSIONID: '会话ID',
      OPERATEMODE: '操作模式',
      SELECTOPERATEMODE: '选择操作模式',
      INPUTURLFORMATE: '支持的格式: http / https / ftp / ftps / sftp',
      HISTORY: '历史',
      WORKFLOWHISTORY: '流程历史记录',
      CONFIGURATIONHISTORY: '配置历史记录',
      POLICYHISTORY: '策略历史',
      TRIGGERTIME: "触发时间",
      TARGETPRODUCT: '目标产品',
      TARGETGROUP: '目标组',
      TARGETSOFTWAREVERSION: '目标软件版本',
      TARGETSN: '目标设备序列号',
      TARGETSV: '目标设备软件版本',
      SUPPORTEDPRODUCT: '受支持的產品',
      MANAGEDEDPRODUCT: '受管理的產品',
      ALWAYSACTIVE: "始终活跃",
      SELECTACTIVEDATERANGE: "选择活动日期和时间范围",
      DAYOFWEEK: '星期几',
      WITHONLYONCE: '只用一次',
      ACTIVEDATERANGE: '活动日期范围',
      ACTIVETIMERANGE: '一天中的活跃时间范围',
      EVERYDAY: '每天',
      SUNDAY: '周日',
      MONDAY: '周一',
      TUESDAY: '周二',
      WEDNESDAY: '周三',
      THURSDAY: '周四',
      FRIDAY: '周五',
      SATURDAY: '周六',
      EXECUTIONSTATUS: '执行状态',
      EXECUTIONTIME: '执行时间',
      EDITACTIONS: '编辑操作',
      DOWNLOADASMULTI: '下载为多文件',
      DOWNLOADASONE: '下载为一个文件',
      LASTEXECUTIONTIME: '最后执行时间',
      SEARCHSN: '搜索序列号',
      ACTIVATE: '激活',
      DEACTIVATE: '停用',
      LOADING: '加载中',
      STATETYPE: '状态类型',
      STAGE: '阶段',
      EDIT_STAGE_OPERATIONS_DESCRIPTION: '使用阶段/操作构建工作流程',
      EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION: '使用阶段/操作构建配置',
      EDIT_POLICY_STAGE_OPERATIONS_DESCRIPTION: '通过阶段/操作构建策略',
      TRIGGERCONDITIONS: '触发条件',
      BUILD: '构建',
      SETUP_SCHEDULE_DESCRIPTION: '设置活动时间范围',
      INVALID_VALUE_MESSAGE: '存在无效值，请检查表单。',
      RESET_TOOLTIP: '重置为默认值',
      RESET_CONFIRM: '您是否要重置所有初始配置值？',
      COUNT: '执行次数',
      COMPLETEDCOUNT: '已完成',
      PARTIALFAILEDCOUNT: '部分取消',
      CANCELEDCOUNT: '已取消',
      INPROGRESS: '处理中',
      FAILCOUNT: '已失败',
      ADDTAGFAIL: '添加标签失败!',
      ADDTAGSUCC: '添加标签成功!',
      DELTAGFAIL: '删除标签失败!',
      DELTAGSUCC: '删除标签成功!',
      STEPAFTERSUCCESS: "如果失败，停止后续任务",
      WORKFLOWOPERATIONLOG: '工作流程操作日志',
      OPERATIONLOGS: '操作日志'
    },
    USERS: {
      ACCOUNT: '使用者列表',
      ONLINEUSERS: '在线使用者',
      ONLINEUSERSDESCRIPTION: "正在使用的或者半小时内登陆过的使用者数量",
      PROFILE: '头像',
      PROFILEDESCRIPTION: '当前登录用户的详细信息。',
      STATUS: '状态',
      ALLSTATUS: '所有状态',
      ALLTYPE: '所有类型',
      ROLE: '角色',
      ROLELIST: '角色列表',
      ROLELISTDESCRIPTION: '用于用户权限控制的角色列表。',
      CANNOTFINDROLE: "找不到角色权限列表",
      CHANGE: '更改',
      ACCOUNTLIST: '账户列表',
      ACCOUNTLISTDESCRIPTION: '当前登录用户可以管理的账户列表。',
      EDITUSER: '编辑用户',
      EXPIRATION: '期限',
      DEPLOYEXPIRATION: '部署过期',
      CONTROLLIST: '权限控制表',
      ALLEDITABLE: '全部可编辑',
      EDITABLE: '可编辑',
      ALLREADONLY: '全部只读',
      READONLY: '只读 ',
      ALLDISABLED: '全部停用',
      DISABLED: '停用',
      SUBMIT: '提交',
      AMPNODE: 'AMP节点',
      SUPERADMINPASSWORD: '超级管理员密码',
      ACTIVITIES: '使用者日志',
      ACTIVITIESLIST: '用户日志列表',
      ACTIVITIESLISTDESCRIPTION: '记录当前登录用户可以管理的账户的所有请求事件。',
      DATERANGE: '选择时间范围',
      BEGINTIMEDATERANGE: '选择开始日期',
      ENDTIMEDATERANGE: '选择结束日期',
      DASHPERMISSION: '仪表板许可',
      CONFIRMPASSWORD: '确认密码',
      NOTMATCH: '密码和确认密码不同.',
      NOTMATCH2: '新密码和确认密码不同.',
      ISPASSWORD: '需要输入密码。',
      ISUSERNAME: '必须填写用户名。',
      ADDUSER: '新增用户',
      USERROLE: '用户角色',
      CONFIRMPSW: '密码必须包含8-128个字符, 并至少包括以下内容：字母、数字和符号。',
      SPECIALSYMBOLS: '只允许由字母、数字和特殊符号(@ !# ? $ / \ _ - .)组成的1~32个字符.',
      SPECIALSYMBOLS_NO_DASH: '只允许由字母、数字和特殊符号(@ !# ? $ / \ _ .)组成的1~128个字符.',
      ADDNEWUSER: '新增用户',
      USERACTION: '用户动作',
      LANGUAGE: '语言',
      AUTHORITYLIST: 'Widget类别权限列表',
      CHANGEPASSWORD: '更改密码',
      APIDOCUMENT: 'API文档',
      USERMANUAL: '用户手册',
      OLDPASSWORD: '旧密码',
      NEWPASSWORD: '新密码',
      PREVIOUSTIME: '上次登录时间',
      PREVIOUSLOCATION: '上次登录位置',
      LASTLOGINLOCATION: '上次登录位置',
      CURRENTTIME: '最新登录时间',
      CURRENTLOCATION: '最新登录位置',
      EDITROLE: '编辑角色',
      ADDNEWROLE: '添加新角色',
      AUTHORITY: '角色权限',
      EMAIL: '电子邮箱',
      ACTIONTYPE: '动作类型',
      EMAILERROR: '电子邮箱的格式不正确',
      ISMAIL: '必须填写电子邮箱。',
      TAGHASCREATED: '此标签已由其他用户创建',
      DEVICEADMIN_TITLE: '设备管理',
      DEVICEADMIN_DESCRIPTION: '具有编辑/读取设备类/设备管理员子类中设备相关Widget的权限。包括Widget：设备列表、实时更新、重启、升级固件',
      PRODUCTADMIN_TITLE: '产品管理',
      PRODUCTADMIN_DESCRIPTION: '具有编辑/读取设备类/产品管理员子类中产品相关Widget的权限。包括Widget：已注册设备、产品列表',
      GROUPADMIN_TITLE: '群组管理',
      GROUPADMIN_DESCRIPTION: '具有编辑/读取设备类/群组管理员子类中群组相关Widget的权限。包括Widget：群组列表、添加设备至群组、添加操作',
      GENERALDATA_TITLE: '一般',
      GENERALDATA_DESCRIPTION: '具有编辑/读取设备类/通用数据子类中设备通用数据相关Widget的权限。包括Widget：在线设备、群组、产品型号、通用信息、关键绩效指标',
      ALARMMANAGEMENT_TITLE: '告警管理',
      ALARMMANAGEMENT_DESCRIPTION: '具有编辑/读取设备类/告警管理子类中特定设备的告警管理Widget的权限。包括Widget：告警管理',
      REMOTETROUBLESHOOTING_TITLE: '高级',
      REMOTETROUBLESHOOTING_DESCRIPTION: '具有编辑/读取设备类/远程故障排除子类中高级远程相关Widget的权限。包括Widget：终端、命令 XML',
      DATAMODEL_TITLE: '数据模型',
      DATAMODEL_DESCRIPTION: '具有编辑/读取设备类/数据模型子类中数据模型相关Widget的权限。包括Widget：数据节点、参数数据',
      NETWORKLOCATION_TITLE: '网络位置',
      NETWORKLOCATION_DESCRIPTION: '具有编辑/读取设备类/网络位置子类中网络位置相关Widget的权限。包括Widget：位置、网络拓扑、覆盖地图',
      LOGCOLLECTION_TITLE: '日志收集',
      LOGCOLLECTION_DESCRIPTION: '具有编辑/读取设备类/日志收集子类中日志收集相关Widget的权限。包括Widget：会话日志列表、生成报告、操作日志列表',
      STATISTICALANALYSIS_TITLE: '统计分析',
      STATISTICALANALYSIS_DESCRIPTION: '具有编辑/读取设备类/统计分析子类中统计分析Widget的权限',
      WIFISPECIFIC_TITLE: 'WiFi特定',
      WIFISPECIFIC_DESCRIPTION: '具有编辑/读取设备类/WiFi特定子类中WiFi特定相关Widget的权限。包括Widget：WiFi客户端、WiFi无线状态、WiFi分析器、WiFi邻居列表',
      CELLULARSPECIFIC_TITLE: '蜂窝特定',
      CELLULARSPECIFIC_DESCRIPTION: '具有编辑/读取设备类/蜂窝特定子类中蜂窝特定相关Widget的权限。包括Widget：UE、蜂窝状态、天线波束、邻居/PLMN列表',
      PMKPICOUNTER_TITLE: 'PM KPI计数器',
      PMKPICOUNTER_DESCRIPTION: '具有编辑/读取设备类/PM KPI计数器特定子类中PM KPI计数器特定相关Widget的权限。包括Widget：PM 图,PM 参数.',
      FAPSPECIFIC_TITLE: 'FAP特定',
      FAPSPECIFIC_DESCRIPTION: '编辑/阅读FAP特定Widget的权限',
      APPSPECIFIC_TITLE: '应用特定',
      APPSPECIFIC_DESCRIPTION: '具有编辑/读取设备类/应用特定子类中应用特定相关Widget的权限。包括Widget：应用列表、服务提供商、设备应用状态',
      YANGMODULE_TITLE: 'Yang模块',
      YANGMODULE_DESCRIPTION: '具有编辑/读取设备类/YANG模块子类中YANG模块相关Widget的权限',
      POWERSAVING_TITLE: '省电',
      POWERSAVING_DESCRIPTION: '具有编辑/读取设备类/省电特定子类中省电特定相关Widget的权限。包括Widget：能源管理.',
      DOCSIS_TITLE: 'Docsis特定',
      DOCSIS_DESCRIPTION: '具有编辑/读取设备类/Docsis特定子类中Docsis特定相关Widget的权限。包括Widget：DOCSIS 状态.',
      DEVICEALARM_TITLE: '设备告警',
      DEVICEALARM_DESCRIPTION: '具有编辑/读取告警类/设备告警子类中设备告警相关Widget的权限。包括Widget：总告警、告警管理（下载/确认）、设备事件跟踪',
      NOTIFICATIONMANAGMENT_TITLE: '通知管理',
      NOTIFICATIONMANAGMENT_DESCRIPTION: '具有编辑/读取告警类/通知管理子类中通知管理相关Widget的权限。包括Widget：通知列表',
      WORKFLOWSETUP_TITLE: '工作流/配置设置',
      WORKFLOWSETUP_DESCRIPTION: '具有编辑/读取配置类/工作流设置子类中工作流/配置设置相关Widget的权限。包括Widget：工作流/配置列表、工作流/配置历史',
      OPERATIONSETUP_TITLE: '操作设置',
      OPERATIONSETUP_DESCRIPTION: '具有编辑/读取配置类/操作设置子类中操作设置相关Widget的权限。包括Widget：动作列表',
      POLICYSETUP_TITLE: '策略设置',
      POLICYSETUP_DESCRIPTION: '具有编辑/读取配置类/策略设置子类中策略设置相关Widget的权限。包括Widget：能源策略',
      SCRIPTSETUP_TITLE: '脚本设置',
      SCRIPTSETUP_DESCRIPTION: '具有编辑/读取脚本设置相关Widget的权限',
      FILESETUP_TITLE: '文件设置',
      FILESETUP_DESCRIPTION: '具有编辑/读取配置类/文件设置子类中文件设置相关Widget的权限。包括Widget：文件列表、特定供应商文件',
      ACCOUNTADMIN_TITLE: '账户管理',
      ACCOUNTADMIN_DESCRIPTION: '具有编辑/读取用户类/帐户管理员子类中帐户管理相关Widget的权限。包括Widget：个人资料、帐户列表',
      ACCOUNTLOG_TITLE: '账户日志',
      ACCOUNTLOG_DESCRIPTION: '具有编辑/读取用户类/帐户日志子类中帐户日志相关Widget的权限。包括Widget：活动列表',
      ACCOUNTROLE_TITLE: '账户角色',
      ACCOUNTROLE_DESCRIPTION: '具有编辑/读取用户类/帐户角色子类中帐户角色相关Widget的权限。包括小Widget：角色列表',
      DEVICESTATISTICS_TITLE: '设备统计',
      DEVICESTATISTICS_DESCRIPTION: '具有编辑/读取分析类/设备统计子类中设备统计相关Widget的权限。包括Widget：在线设备、新设备、事件代码',
      SYSTEMSTATISTICS_TITLE: '系统统计',
      SYSTEMSTATISTICS_DESCRIPTION: '具有编辑/读取分析类/系统统计子类中系统统计相关Widget的权限。包括Widget：数据库状态、设备会话持续时间、设备会话速率、空闲内存',
      PROVISIONINGSTATISTICS_TITLE: '配置统计',
      PROVISIONINGSTATISTICS_DESCRIPTION: '具有编辑/读取分析类/配置统计子类中配置统计相关Widget的权限。包括Widget：配置代码、软件版本、SIM卡状态',
      PMSTATISTICS_TITLE: 'PM统计',
      PMSTATISTICS_DESCRIPTION: '具有编辑/读取分析类/性能管理统计子类中性能管理统计相关Widget的权限。包括Widget：性能管理、性能管理状态、性能报告',
      SERVERSETTING_TITLE: '服务器设置',
      SERVERSETTING_DESCRIPTION: '具有编辑/读取系统类/服务器设置子类中服务器设置相关Widget的权限。包括Widget：通用设置、实时更新、CWMP、Netconf、性能服务',
      SERVERPREFERENCE_TITLE: '服务器偏好',
      SERVERPREFERENCE_DESCRIPTION: '具有编辑/读取系统类/服务器偏好设置子类中服务器偏好设置相关Widget的权限。包括Widget：SMTP通知、SNMP Trap通知、报告、统计、日志',
      SERVERLICENSE_TITLE: '服务器许可',
      SERVERLICENSE_DESCRIPTION: '具有编辑/读取系统类/服务器许可证子类中服务器许可证相关Widget的权限。包括Widget：许可证',
      SERVERREPORT_TITLE: '服务器报告',
      SERVERREPORT_DESCRIPTION: '具有生成/读取附加类/报告导出子类中服务器报告相关Widget的权限。包括Widget：生成摘要报告',
      SYSTEMEVENTS_TITLE: '系统事件',
      SYSTEMEVENTS_DESCRIPTION: '具有编辑/读取系统类/系统事件子类中系统事件相关Widget的权限。包括Widget：系统事件、注册日志',
      SYSTEMNODES_TITLE: '系统节点',
      SYSTEMNODES_DESCRIPTION: '具有编辑/读取系统类/系统节点子类中系统节点相关Widget的权限。包括Widget：节点',
      PERSONALTHEME_TITLE: '个人主题',
      PERSONALTHEME_DESCRIPTION: '具有编辑/读取附加类/个人主题子类中个人主题相关Widget的权限',
      REPORTEXPORT_TITLE: '报告导出',
      REPORTEXPORT_DESCRIPTION: '具有编辑/读取附加类/报告导出子类中报告导出相关Widget的权限。包括Widget：生成摘要报告',
      SYSTEMINFORMATION_TITLE: '系统信息',
      SYSTEMINFORMATION_DESCRIPTION: '具有编辑/读取系统类/系统信息子类中AMP系统信息相关Widget的权限。包括Widget：系统信息',
      FURBISHMENTSTATISTICS_TITLE: '翻新统计',
      GENERAL_TITLE: '一般',
      GENERAL_DESCRIPTION: '在 5G 核心类/一般子类中编辑/读取一般小部件的权限。',
      UE_TITLE: '用户设备',
      UE_DESCRIPTION: '在 5G 核心类/用户设备子类中编辑/读取用户设备小部件的权限。',
      CELL_TITLE: '小区',
      CELL_DESCRIPTION: '在 5G 核心类/小区子类中编辑/读取小区小部件的权限。',
      ALARM_TITLE: '告警',
      ALARM_DESCRIPTION: '在 5G 核心类/告警子类中编辑/读取告警小部件的权限。',
      SYSTEM_TITLE: "系统",
      SYSTEM_DESCRIPTION: "有权编辑/读取与 5GC 系统相关的操作和组件。",
      TOTALREQUESTS_TITLE: '总请求',
      TOTALREQUESTS_DESCRIPTION: '过去24小时内的总请求次数',
      EXTERNAL_DEVICE_TITLE: '设备',
      EXTERNAL_DEVICE_DESCRIPTION: '具有编辑/读取外部类/设备子类中设备相关NBI的权限。',
      CBSD_DEVICE_TITLE: '设备',
      CBSD_DEVICE_DESCRIPTION: '具有编辑/读取CBSD类/设备子类中设备相关NBI的权限。',
      REQUESTSHISTORY_TITLE: "请求历史",
      REQUESTSHISTORY_DESCRIPTION: "过去24小时总请求量的历史图表",
      IPREQUESTDISTRIBUTION: "IP请求分布图",
      IPREQUESTDISTRIBUTION_DESCRIPTION: "过去 24 小时内，每个 IP 的请求数量前五名",
    },
    ANALYSIS: {
      COLLAPSIBLE: '分析',
      SYSTEM: '系统统计',
      SESSIONDURATION: '设备会话时长',
      SESSIONRATE: '设备会话频率',
      LATENCY: '设备请求延迟',
      REQUESTRATE: '设备请求频率',
      PARSING: '设备请求解析',
      MEMORY: '内存使用',
      SPACEUSAGE: '空间使用',
      CPUUTILIZE: 'CPU 使用率',
      MEMORYUSAGECHART: '内存使用图表',
      FREEMEMORY: '空闲内存',
      CPUUSAGE: 'CPU 使用率',
      CPUUSAGECHART: 'CPU 使用率图表',
      FREEDISK: '空闲磁盘',
      DEVICE: '设备统计',
      PM: 'PM 统计',
      TOTALDEVICE: '总设备数',
      NEWDEVICE: '新设备',
      SESSIONS: '会话',
      EVENTCODE: '事件代码',
      MEMORYUTILIZATION: '内存利用率',
      DISKUTILIZATION: '磁盘利用率',
      MEMORYUTILIZATIONDESCRIPTION: '内存利用率历史图表。',
      DISKUTILIZATIONDESCRIPTION: '磁盘利用率历史图表。',
      SESSIONDURATIONDESCRIPTION: '所有设备的平均会话时长的历史图表。会话时长：设备与 AMP 之间会话中花费的总时间。',
      SESSIONRATEDESCRIPTION: '所有设备的平均会话频率的历史图表。会话频率：设备每秒向 AMP 发起的会话次数。',
      LATENCYDESCRIPTION: '所有设备的平均请求延迟的历史图表。请求延迟：设备与 AMP 之间的请求中花费的总时间。',
      REQUESTRATEDESCRIPTION: '所有设备的平均请求频率的历史图表。请求频率：设备每秒向 AMP 发起的请求次数。',
      PARSINGDESCRIPTION: '所有设备的平均请求解析时间的历史图表。请求解析：AMP 解析设备发起的请求所花费的总时间。',
      MEMORYDESCRIPTION: '空闲内存历史图表。',
      CPUUSAGEDESCRIPTION: 'CPU 使用率历史图表。',
      FREEDISKDESCRIPTION: '空闲磁盘历史图表。',
      TOTALDEVICEDESCRIPTION: '定期统计的设备总数的历史图表。',
      ONLINEDEVICEDESCRIPTION: '定期统计的在线设备总数的历史图表。',
      NEWDEVICEDESCRIPTION: '定期统计的新注册设备总数的历史图表。',
      SESSIONSDESCRIPTION: '定期统计的每个产品的总会话数的历史图表。',
      EVENTCODEDESCRIPTION: '定期统计的每个产品的事件代码总数的历史图表。',
      PROVISIONING: '设备配置统计',
      PMSTATISTICS: 'PM 统计',
      STATUSFORDEVICES: '在线设备状态',
      RATE: '频率',
      NUMBER: '数量',
      VERSIONDISTRIBUTION: '软件版本分布',
      CODEDISTRIBUTION: '配置代码分布',
      XMPPSTATUS: 'XMPP 状态',
      XMPPSTATUS_DESCRIPTION: 'XMPP 服务状态。',
      IMSSTATUS: 'IMS 注册状态',
      SIMSTATUS: 'SIM 卡状态',
      IPSECSTATUS: 'IPSec 隧道状态',
      STATUSFORDEVICE: '设备状态总览',
      DBSTATUS: '数据库状态',
      DBSTATUS_DESCRIPTION: '数据库服务状态。',
      SELECTDURATION: '选择时长',
      PMSTATUS: 'PM 服务状态',
      PMSTATUS_DESCRIPTION: 'PM 服务状态。',
      REFURBISHMENETHISTORY: '重置历史'
    },
    SYSTEM: {
      COLLAPSIBLE: "系统",
      EXTERNALSERVICE: "外部服务",
      PERFORMANCESERVICE: "性能服务",
      PERFORMANCESERVICEDESCRIPTION: "与性能管理相关的设置，例如设备上传KPI文件的默认URL。",
      PREFERENCE: "偏好",
      DBSERVER: "数据库服务器",
      SOURCES: "数据源",
      SNMPTRAP: "SNMP陷阱通知",
      SNMPTRAP_DESCRIPTION: "有关通过SNMP进行通知的配置。",
      SMTP: "SMTP通知",
      SMTP_DESCRIPTION: "有关通过SMTP进行通知的配置。",
      SMS: "短信通知",
      STATISTICSPRE: "统计",
      STATISTICSPRE_DESCRIPTION: "有关AMP中数据收集旋转的偏好设置。",
      LOGPRE: "日志",
      LOGPRE_DESCRIPTION: "设备会话和操作的日志旋转设置。",
      FAULT: "告警",
      FAULT_DESCRIPTION: "关于告警的偏好设置，例如自动确认和告警旋转。",
      REPORTS: "报告",
      REPORTS_DESCRIPTION: "设备摘要报告的报告旋转设置。",
      SYSREPORT: "服务器报告列表",
      GENERATESYSREPORT: "生成服务器报告",
      CONFIRMDELETE: "确认删除所有服务器报告",
      DOCONFIRMDELETE: "您要删除所有服务器报告吗？",
      CONFIRMDELETESELECT: "确认删除所选服务器报告",
      DOCONFIRMDELETESELECT: "您要删除所选服务器报告吗？",
      DOWNLOADCSV: "下载CSV",
      DOWNLOADTXT: "下载TXT",
      DOWNLOADXLSX: "下载XLSX",
      LICENSE: "许可证",
      LICENSE_DESCRIPTION: "有关AMP许可证的信息，例如状态、类型、支持的协议和到期日。允许用户更新许可证以延长到期或启用AMP中的更多功能。",
      LICENSETYPE: "版本",
      LICENSESTATE: "许可证状态",
      KEY_DESCRIPTION: "提供在输入密钥后激活AMP的功能，以及与密钥相关的信息。",
      CPENUM: "设备容量",
      SESSIONNUM: "会话编号",
      REMAININGTIME: "剩余时间",
      VALIDTO: "有效至",
      DISPLAY: "显示",
      GENERAL: "常规",
      GENERAL_DESCRIPTION: "系统常规设置，例如会话日志级别、注销超时和服务器报告。",
      CWMP: "CWMP",
      CWMP_DESCRIPTION: "与CWMP协议相关的设置，例如会话超时和在线标准。",
      NETCONF: "Netconf",
      NETCONF_DESCRIPTION: "与NETCONF协议相关的设置，例如重试间隔和保持活动间隔。",
      USP: "USP",
      USP_DESCRIPTION: "与USP协议相关的设置，例如通过Websocket或MQTT。",
      LIVEUPDATE: "实时更新",
      LIVEUPDATE_DESCRIPTION: "用于立即在AMP和特定设备之间启动通信的功能。",
      MQTT: "系统MQTT",
      FILES: "文件",
      FILES_DESCRIPTION: "系统默认文件设置，例如FOTA和设备日志URL及认证。",
      TELEMETRY: "系统遥测",
      SUMMARYREPORT: "摘要报告",
      SUMMARYREPORT_DESCRIPTION: "与地图相关的设置，例如选择地图数据提供者和源。",
      EVENTS: "事件",
      SYSEVENTS: "系统事件",
      SYSEVENTS_DESCRIPTION: "列出所有与AMP相关的事件及详细信息，例如严重性、特定问题、可能原因和事件时间。",
      REGISTRATIONLOG: '注册日志',
      REGISTRATIONLOG_DESCRIPTION: 'Usp mqtt 注册日志',
      NODES: "节点",
      DEL_TITLE_NODES: '是否要删除节点',
      NODES_DESCRIPTION: "列出AMP节点信息，例如节点名称、IP地址、AMP版本和运行时间。",
      ENTERLICENSEKEY: "输入许可证密钥",
      LICENSEKEY: "许可证密钥",
      KEY: '密钥',
      UNIQUEID: '唯一标识',
      KEYVALIDITY: '密钥有效性',
      EDITURL: "编辑URL",
      VERIFYSMTP: "测试发送邮件",
      VERIFYSNMP: "测试发送SNMP",
      SERVICESTATUSTRACKING: "服务状态跟踪",
      KPIFACTORS: "KPI因素",
      KPIFACTORSTRACKING: "KPI因素跟踪",
      VERIFYXMPP: "测试XMPP",
      MAP_DESCRIPTION: "地图",
      PROCESS: "进程",
      NODEID: "节点ID",
      SEVERITY: "严重性",
      LOCATIONMAP: "位置地图",
      LOCATIONMAP_DESCRIPTION: "与地图相关的设置，例如选择地图数据提供者和源。",
      FIVECORESERVICE: "5G核心服务",
      FIVECORESERVICE_DESCRIPTION: "与5G核心服务相关的设置，例如5G核心供应商和服务器URL。",
      ENERGYMANAGEMENT: "能量管理",
      ENERGY_DESCRIPTION: "与能量管理相关的设置，例如功率限制和休眠间隔。",
      NIDS: "NIDS",
      NIDS_DESCRIPTION: "网络入侵检测系统。",
      PROMETHEUS: "指标收集 - Prometheus",
      PROMETHEUS_DESCRIPTION: "与使用Prometheus进行数据收集相关的设置。",
      PROMETHEUS_PARAMETER_DESCRIPTION: {
        PULL_PATH: '用于抓取指标的URL。',
        USERNAME: '每个Prometheus指标拉取请求中Authorization头中的用户名。',
        PASSWORD: '每个Prometheus指标拉取请求中Authorization头中的密码。',
      },
      KAFKA: "指标收集 - Kafka",
      KAFKA_DESCRIPTION: "与使用Kafka进行数据收集相关的设置。",
      KAFKA_PARAMETER_DESCRIPTION: {
        BROKERS: 'kafka代理的URL。',
        TOPIC: '生产者消息的Kafka主题。',
        ROUTING_KEY: '消息路由机制。',
        ACCESS_TOKEN: '用于身份验证的令牌。'
      },
      NODE_DESCRIPTION: {
        UPGRADE: '升级',
        ADDRESS: '地址',
        HASHKEY: '哈希键',
        TYPE: '类型',
        USERNAME: '用户名',
        PASSWORD: '密码',
        TARGEVERSION: '圆盾版本',
        COMPOENT: '组件',
        RUNTOOL: '运行工具',
        UPGRADESUCC: '升级成功',
        UPGRADEFAIL: '升级失败',
        WAITFORUPDATE: '下载升级文件',
        STARTUPDATING: '开始升级...',
        BEINGUPDATED: '升级完成，等待重启',
        SERVERRESTART: '服务器正在重启...',
        TIMEOUT: '升级超时！',
        ACSNODEREBOOT: '重启AMP节点可能导致数据丢失。',
        ACSNODESHUTDOWN: '关闭AMP节点可能导致数据丢失。',
      },
      SETTING_DESCRIPTION: {
        GENERAL: {
          SESSIONTIMEOUT: '该设置用于配置CWMP会话的超时时间。',
          TRANSACTIONTIMEOUT: '事务超时是请求和响应的超时时间。',
          REFRESHINTERVAL: 'Netconf重试间隔。',
          KEEPALIVEINTERVAL: 'Netconf保持活动间隔。',
          SESSIONLOGLEVEL: '该设置影响设备会话日志视图的详细程度:如果是原始日志,会话将呈现为SOAP封包;如果是RPC日志,则呈现为解析后的SOAP消息。',
          DEVICE: "设置切换打开/关闭发送 'Device.',以下所有参数均用于设备的初始上线时 BootStrap 方法。",
          DEVICE_XMPP_CONNECTION_1: '该设置用于在启动时开启/关闭发送Device.XMPP.Connection.1的方法。',
          DEVICE_MQTT_CLIENT_1: '该设置用于在启动时开启/关闭发送Device.MQTT.Client.1的方法。',
          DEIVCE_DEVICEINFO_XVENDOR_HOLD: '该设置用于在启动时开启/关闭发送Devcie.DeviceInfo.X_VENDOR.HOID的方法。',
          DEVICE_MANAGEMENTSERVER_PERIODICINFORMTIME: '该设置用于在启动时开启/关闭发送Device.ManagementServer.PeriodicInformTime的方法。',
          TYPEOFINFORMRECEIVEDWITHIN: '该设置用于判断设备是否在线。',
          INTERVALOFINFORMRECEIVEDWITHIN: '该设置配置掉线条件的时间周期。',
          SERVERREPORTENABLED: '该设置用于开启/关闭服务器报告的生成:开启时,ACS将生成服务器报告;关闭时,ACS将不生成服务器报告。',
          SERVERREPORTEMAILNOTIFICATION: '启用或禁用系统报告电子邮件通知功能。',
          SERVERREPORTPERIOD: '该设置配置服务器报告生成的频率(以天为单位)。',
          SERVERREPORTCONTENT: '系统报告可选内容，AMP支持MONGODB、XMPP、MQTT和PM服务器的信息收集。',
          PRIORITY: '定义CPE IP检测来源的优先级:RemoteAddress - IP来自指定的数据节点Eq.Device.DeviceInfo.X_Vendor_GlobalIPAddress;X-Forwarded-For - IP来自X-Forwarded-For HTTP头;Custom - IP来自自定义HTTP头,该头的名称在[Custom Header]设置中指定。',
          CUSTOMHEADER: '自定义HTTP头的名称,其中包含CPE的实际IP地址。',
          IDLETIMEOUT: '系统会话过期时间',
          SWAGGER_ENABLED: '启用/禁用 restful API UI。',
          CLIENT_URL: 'CPE 使用 CPE WAN 管理协议连接到 ACS 的 URL。'
        },
        CONNECTIONREQUEST: {
          USERNAME: 'CR机制的可配置用户名。',
          PASSWORD: 'CR机制的可配置密码。',
          RETRYTIMEOUT: 'CR的可配置超时时间。',
          NUMBEROFRETRY: '最大CR尝试次数。',
          TYPE: "CR 的类型。",
          XMPPDOMAIN: '该设置为可配置的域名,用于JID自动生成。',
          XMPPPORT: 'XMPP服务器的端口。ejabberd服务器的默认值。',
          XMPPACSUSERNAME: 'XMPP CR调用的可配置ACS用户名。',
          XMPPACSPASSWORD: 'XMPP CR调用的可配置ACS密码。',
          XMPPADMINPORT: '服务器管理员的XMPP服务器端口。ejabberd服务器的默认值。',
          XMPPADMINUSERNAME: '该设置为可配置的XMPP管理员用户名凭据,用于自动XMPP用户注册。',
          XMPPADMINPASSWORD: '该设置为可配置的XMPP管理员密码凭据,用于自动XMPP用户注册。',
          XMPPRESOURCE: 'XMPP CR的可配置资源值。',
          XMPPUSETLS: '该设置切换开/关XMPP CR的TLS使用:如果开启,启用TLS;如果关闭,禁用TLS。',
        },
        USP: {
          BINDING: '绑定类型，WebSocket或MQTT。',
          ADDRESS: 'MTP服务器域名',
          PORT: 'MTP连接端口',
          APIKEY: "在MQTT中,使用API密钥查询服务器状态。",
          USERNAME: '代理所需的用户名(如果有)。',
          PASSWORD: '代理所需的密码(如果有)。',
          USE_TLS: 'MTP over TLS.',
          EXADDRESS: '设备可以连接到WebSocket/MQTT/CWMP/XMPP服务的地址或域。',
          EXPORT: '设备可以连接到WebSocket/mqtt服务的端口。',
          USETLS: '设备是否使用TLS连接到WebSocket/MQTT/CWMP/XMPP服务。',
          EXURL: '设备可以连接到CWMP服务的URL。',
        },
        FILES: {
          DOWNLOAD: {
            LATESTFIRMWARE: '该设置允许用户为CSR用户指定最新的固件版本。',
            FIRMWARESERVERURL: '用于AP下载固件的文件服务器路径。',
            FIRMWARESERVERUSERNAME: '与文件服务器进行身份验证的用户名凭据。',
            FIRMWARESERVERPASSWORD: '与文件服务器进行身份验证的密码凭据。',
          },
          UPLOAD: {
            FILETYPE: '设备上传文件类型',
            INSTANCEPATH: '数据模型中用户可以上传日志文件的路径.',
            LOGUPLOADURL: 'CSR用户可以上传AP日志文件的URL。此功能需要额外的服务器配置。',
            USERNAME: '可配置的文件上传用户名。',
            PASSWORD: '可配置的文件上传密码。'
          },
          CONF_DOWNLOAD: {
            DEFAULT_FILE: '从文件中选择的默认配置文件（类型为 3 Vendor Configuration File）。',
            DEFAULT_FILE_URL: '默认配置文件的 URL。',
            FILETYPE: '设备配置文件的下载类型。',
            CONFURL: '设备从 AMP 下载配置文件的 URL。',
            USERNAME: '用于设备下载的可配置用户名。',
            PASSWORD: '用于设备下载的可配置密码。'
          },
        },
        TELEMETRY: {
          TELEMETRYSERVERREDIRECTION: '该设置切换开/关设备信息页面上的第三方网站链接按钮:如果开启,显示第三方网站链接按钮;如果关闭,不显示第三方网站链接按钮。',
          VENDOR: '提供特定技术、服务或产品的供应商，选项包括 Druid、DNMM、HP 和 Open 5GC。',
          TYPE: '开源网络入侵检测系统类型，选项包括 Suricata',
          SERVERURL: '第三方网站的URL。',
          SERVERUSERNAME: '用于登录第三方网站的可配置用户名。',
          SERVERPASSWORD: '用于登录第三方网站的可配置密码。',
          KPIFACTORS: 'KPI因素可以根据用户定义的规则监控PM参数状态。',
          UEINTERVAL: '可配置的UE定时器间隔。',
          CELLINTERVAL: '可配置的单元定时器间隔。',
          ALARMINTERVAL: '可配置告警检查的时器间隔。',
          COMMONINTERVAL: '可配置通用定时器间隔。',
          APIURL: '性能服务的北向接口地址。',
          APIUSERNAME: '性能服务北向接口身份的用户名。',
          APIPASSWORD: '性能服务北向接口身份的密码。',
        },
        MAP: {
          TYPE: '地图服务器类型，AMP支持Google地图和Open街道地图。默认是谷歌地图。',
          URL: '地图服务器url。',
          APIKEY: '映射api密钥。',
        }
      },
      PREFERENCE_DESCRIPTION: {
        SMTP: {
          MAIL_HEALTHY: '系统健康监控,例如CPU负载、磁盘使用情况、崩溃和许可证。',
          MAIL_FORM: '电子邮件发送者用户名',
          MAIL_HOST: '对应邮箱的SMTP服务器地址。',
          MAIL_USERNAME: '电子邮件发送者地址',
          MAIL_PASSWORD: '电子邮件发送者密码',
          MAIL_PORT: '电子邮件发送者端口',
          MAIL_TO: '收件人地址',
          MAIL_SMTP_AUTH: 'SMTP协议相关配置,是否需要身份验证。',
          MAIL_SMTP_SECURITY: 'SMTP协议相关配置。',
          MAIL_TRANSPORT_PROTOCOL: '目前未使用',
        },
        SNMPTRAP: {
          SNMPTRAP_TARGET: 'SNMP Trap目标地址',
          SNMPTRAP_PORT: '发送请求的UDP端口,默认为161。',
          SNMPTRAP_RETRIES: '重新发送请求的次数,默认为1。',
          SNMPTRAP_TIMEOUT: '在重试或失败之前等待响应的毫秒数,默认为5000。',
          SNMPTRAP_TRANSPORT: '指定要使用的传输,可以是udp4或udp6,默认为udp4。',
          SNMPTRAP_TRAPPORT: '发送陷阱和通知的UDP端口,默认为162。',
          SNMPTRAP_VERSION: 'snmp.Version1或snmp.Version2c',
          SNMPTRAP_BACKOFF: '每次重试时增加超时的倍数,默认为1表示不增加。',
          SNMPTRAP_COMMUNITY: '用于确保通信和认证的安全性。',
        },
        REPORTS: {
          DEVICE_REPORT_CLEANUP_ENABLE: '如果打开,该设置将开启自动设备报告存储清理。如果关闭,自动设备报告存储清理将被禁用。',
          DEVICE_REPORT_RETENTION_PERIOD: '该设置指定在存储中保留设备报告条目的天数。',
          SERVER_REPORT_CLEANUPZZ_ENABLE: '如果打开,该设置将开启自动服务器报告存储清理。如果关闭,自动服务器报告存储清理将被禁用。',
          SERVER_REPORT_RETNETION_PERIOD: '该设置指定在存储中保留服务器报告条目的天数。',
        },
        STATISTICS: {
          CPU_COLLECTION_ENABLE: '如果显示启用，则许可证允许收集CPU指标。如果显示禁用，则许可证不允许收集CPU指标。',
          DISK_COLLECTION_ENABLE: '如果显示启用，则许可证允许收集磁盘指标。如果显示禁用，则许可证不允许收集磁盘指标。',
          MEMORY_COLLECTION_ENABLE: '如果显示启用，则许可证允许收集内存指标。如果显示禁用，则许可证不允许收集内存指标。。',
          REPORT_ENABLE: '如果显示启用，则许可证允许报告统计指标。如果显示禁用，则许可证不允许报告统计指标。。',
          REPORT_PERIOD: '该设置指定收集指标的频率。',
          PM_KPI_COLLECTION_RETENTION: '该设置指定保留 PM KPI DB 数据的天数。',
          PM_KPI_FILE_RETENTION: '该设置指定保留 PM KPI 文件的天数。'
        },
        LOGCONFIG: {
          DEVICE_GROUP_OPERATION_LOG_CLEANUP_ENABLE: '如果显示启用，许可证允许自动清除设备组操作数据。如果显示禁用，则许可证不允许自动清除设备组操作数据。',
          DEVICE_GROUP_OPERATION_LOG_RETENTION_PERIOD: '该设置指定在日志中保留组操作数据的天数。',
          DEVICE_OPERATION_LOG_CLEANUP_ENABLE: '如果显示启用，则许可证允许自动清除设备操作数据。如果显示禁用，则许可证不允许自动清除设备操作数据。',
          DEVICE_OPERATION_LOG_RETENTION_PERIOD: '该设置指定在日志中保留设备操作数据的天数。',
          SESSION_LOG_CLEANUP_ENABLE: '如果显示启用，则许可证允许自动清除事件日志。如果显示禁用，则许可证不允许自动清除事件日志。',
          SESSION_LOG_RETENTION_PERIOD: '该设置指定在日志中保留会话记录的天数。',
          STATISTICS_LOG_CLEANUP_ENABLE: '如果启用，则许可证允许自动统计日志清理。如果禁用，则许可证不允许自动统计日志清理。',
          STATISTICS_LOG_RENTENTION_PERIOD: '该设置指定在日志中保留统计日志的天数。',
        },
        FAULTMANAGEMENT: {
          EVENT_ALARM_ACK_ENABLE: '若开启,则设置自动确认事件。若关闭,则需要手动确认。',
          EVENT_ALARM_CLEANUP_ENABLE: '若开启,则设置自动清理事件日志。若关闭,则禁用自动清理事件日志。',
          EVENT_ALARM_EMAIL_ENABLE: '若开启,则设置在事件发生时自动发送邮件通知。若关闭,则不发送警报邮件通知。',
          EVENT_ALARM_RETENTION_PERIOD: '该设置指定保留事件日志的天数。'
        }
      }
    },
    COMMON: {
      DEVICES: '设备',
      DEVICE: '设备',
      CLIENTS: '终端装置',
      CLIENT: '终端装置',
      USERS: '使用者',
      ALARMS: '告警',
      TOTALALARMS: '告警总数',
      HISTORYALARMS: '历史警报',
      CRITICALALARMS: '严重告警',
      MAJORALARMS: '主要告警',
      WARNINGALARMS: '警告告警',
      MINORALARMS: '次要告警',
      PRODUCTS: '产品型号',
      PRODUCTSDISTRIBUTION: '产品分布',
      REGISTERDEVICECOUNT: "注册设备数量",
      REGISTERDEVICECOUNTDISTRIBUTION: "每个产品设备数量分布图",
      ONLINEDEVICE: '在线设备',
      HISTORYONLINEDEVICE: "历史在线设备",
      APPLY: '应用',
      DELETE: '删除',
      DELETEALL: '删除所有',
      CANCEL: '取消',
      OK: '确定',
      CLOSE: '关闭',
      ADD: '新增',
      EDIT: '编辑',
      Fail: '失败',
      SERIAL_NUMBER: '序列号',
      PRODUCT_CLASS: '产品类型',
      ACTION: '配置文件',
      NEW: '新',
      SELECTACTION: '选择动作',
      IMPORT: '导入',
      DOWNLOAD: '下载',
      DOWNLOADLOG: '下载日志',
      SAVE: '保存',
      DONTSAVE: '不保存',
      UPLOAD: '上传',
      NAME: '名称',
      ENTERNAME: '输入名称',
      VERSION: '版本',
      PRIORITY: '优先权',
      ENTERVERSION: '输入版本',
      SOFTVERSION: '软件版本',
      TYPE: '类型',
      SELECTTYPE: '选择类型',
      PREVIOUS: '上一页',
      NEXT: '下一页',
      USERNAME: '用户名',
      PASSWORD: '密码',
      USERNAME1: '用户名',
      PASSWORD1: '密码',
      ENTERUSERNAME: '输入用户名',
      ENTERPASSWORD: '输入密码',
      UPDATE: '更新',
      UNINSTALL: '卸载',
      PARAMETERS: '参数',
      PARAMNAME: '参数路径',
      ENTERPARAMNAME: '输入参数路径',
      PARAMTYPE: '参数类型',
      SELECTPARAMTYPE: '选择参数类型',
      PARAMVALUE: '参数值',
      ENTERPARAMVALUE: '输入参数值',
      ADDPARAM: '新增参数',
      EXCUTE: '执行',
      SIZE: '大小',
      CANCELALL: '取消所有',
      FIELDREQUIRED: '此字段必填!',
      DETAILS: '详情',
      SELECTPRODUCTNAME: '选择产品型号',
      SELECTPRODUCT: '选择产品型号',
      AND: '和',
      EDITPARAM: '编辑参数',
      VALUE: '值',
      EXPANDCOLLROW: '展开/折叠行',
      PORT: 'Port',
      HOST: 'Host',
      THECUSTOMIZE: '主题编辑器',
      CUSTOMIZEREALTIME: '即时定制和预览',
      SKIN: '皮肤',
      LIGHT: '浅色',
      BORDERED: '有边的',
      DARK: '深色',
      RED: "红色",
      BLUE: "蓝色",
      SEMIDARK: '半深色',
      ROUTETRA: '切换页面特效',
      FADEINLEFT: '向左渐隐',
      ZOOMIN: '放大',
      FADEIN: '渐显',
      NONE: '无',
      MENULAYOUT: '选单配置',
      VERTICAL: '垂直',
      HORIZONTAL: '水平',
      MENUCOLL: '菜单折叠',
      MENUHIDDEN: '菜单隐藏',
      NAVBARCOLOR: '导航栏颜色',
      NAVBARTYPE: '导航栏类型',
      MENYTYPE: '菜单类型',
      FLOATING: 'Floating',
      STICKY: 'Sticky',
      STATIC: 'Static',
      FOOTERTYPE: '页脚类型',
      WIDGETS: '自定义Widgets',
      EDITMODE: '编辑方式',
      CUSWIDGETS: '自定义Widgets',
      LOGOUT: '注销',
      RECENTNOTIF: '最近的通知',
      NOTIFICATIONS: '通知',
      READMORE: '查看更多',
      TOTAL: '总数',
      SELECTED: '已选',
      CREATED: '创建时间',
      SELECTCOLUMN: '选择列',
      ACTIVE: '有效',
      ALLOW: '允许',
      YES: '是',
      CLIENTLIST: '终端装置列表',
      WIFICLIENTLIST: 'WiFi 终端装置列表',
      WIFICLIENTLIST_DESCRIPTION: '列出此群组中的所有 WiFi 客户端。',
      WIFICLIENTLISTDESCRIPTION: '这台设备可用的无线电/频段上关联客户端的当前 WiFi 状态。',
      ONLINE: '在线',
      OFFLINE: '离线',
      EXPORT: '导出',
      MQTT: 'MQTT',
      CWMP: 'CWMP',
      NETCONF: 'NETCONF',
      CURRENTNODE: '当前节点',
      CHILDNODE: '子节点',
      EDITSTAGE: '编辑阶段名称',
      STAGENAME: '阶段名称',
      ENTERSTAGENAME: '输入阶段名称',
      OPERATIONNAME: '操作名称',
      ADDOPERATION: '添加操作',
      ALLPROTOCOL: '所有协议',
      ALLPRODUCTS: '所有产品',
      ALLEVENT: '所有事件',
      USER: '用户',
      ALLFILETYPES: '所有文件类型',
      ALLTARGETTYPES: '所有目标类型',
      TRANSMISSTIONTYPE: '传输类型',
      SELECTTRANSMISSTIONTYPE: '选择传输类型',
      REMOVEFROMGROUP: '从组中删除',
      SHUTDOWN: '关机',
      NOPERMISSION: '当前用户无权读取此页中的内容。',
      SEPARATED_BY_SEMICOLONS: '用分号分隔',
      SEPARATED_BY_COMMAS: '用逗号分隔',
      MAIL_SEPARATED_BY_SEMICOLONS: '用分号分隔 (<EMAIL>;<EMAIL>;)',
      SN_SEPARATED_BY_COMMAS: '用逗号分隔 (sn1,sn2)',
      WIDGETNAME: '输入Widget名称、类或子类。',
      APNAMEEDITSUCC: '编辑 AP 名称成功！',
      APNAMEEDITFAIL: '编辑 AP 名称失败。',
      LOCATE: '定位',
      RELOAD: "重新加载",
      DATA: "数据",
      STATE: "状态",
      REGISTER: '注册',
      GROUP: '组',
      SELECTCHARTTYPE: '选择图表类型',
      OPEN_MAXIMIZE: "打开最大化",
      SELECTORENTER: '请选择或输入您的选项',
      INVALIDFILETYPE: '文件类型无效。请选择以下类型之一: ',
    },
    CONFIRM: {
      CONF: '确认',
      REMOVAL: '删除?',
      REBOOT: '重启?',
      SHUTDOWN: '关机?',
      ADDFAIL: '新增失败!',
      NAMEEXIST: '名称已经存在!',
      ALARMNOTIF: '告警通知更新成功',
      CONFREMGROUP: '确认移出群组?',
      CONFGROUP: '确认移除群组?',
      CONFGROUPS: '确认组别删除？',
      DOGROUP: '是否要删除设备:',
      FROMGROUP: '?',
      IMPORTSUCCESS: "导入成功！",
      IMPORTFAIL: '导入失败!',
      FILEEMPTY: '文件为空',
      NOTSUPPORT: '不支持此文件格式',
      DODELETEGROUP: '是否要删除群组',
      DODELETEGROUPS: '您想删除这些组吗？',
      GROUPOPER: '设备群组操作!',
      WORKFLOWOPER: '设备工作流程操作!',
      WORKFLOWOPERATION: '设备工作流程操作',
      WORKFLOWDOREMOVEALL: '您想从设备工作流程操作日志中删除所有条目吗？',
      WORKFLOWCLEANSUCC: '设备工作流程操作日志已清理成功',
      WORKFLOWNOTCLEAN: '设备工作流程操作日志未被清理',
      SETGROUPOPERSUCC: '设置组操作成功!',
      RENAMESUCC: '重命名成功',
      CONFNIT: '确认下载告警通知?',
      DODOWNLOADNIT: '是否下载告警通知 ',
      ALARMNIT: '通知',
      DOWNLOADSUCC: '下载成功',
      PLESELECT: '请先选择告警通知!',
      CONFDOWNLOADNIT: '确认下载选择的告警通知?',
      DODOWNLOADSELECT: '是否要下载选择的告警通知?',
      DELETESUCC: '删除选择的告警通知成功!',
      DOWANT: '是否要',
      THEALARMNIT: '告警通知',
      SUCC: ' 成功!',
      CONFDELETENIT: '确认删除告警通知?',
      DODELETENIT: '是否要删除告警通知',
      NITDELSUCC: '删除告警通知删除成功!',
      NITID: '告警通知(ID:',
      WANDEL: ')已删除!',
      NITDELETEFAIL: '告警通知删除失败!',
      NOTDEL: ')未删除!',
      SELECTFIRST: '请先选择告警通知!',
      STATEITEMS: '所选的告警通知包含一个或多个活动状态条目!',
      CONFSELECTNIT: '确认删除选择的告警通知?',
      DOSELECTNIT: '是否要删除选择的告警通知?',
      SELECTNITSUCC: '删除选择的告警通知成功!',
      GROUPOPERATION: '设备群组操作',
      CANCELSUCC: ' 已取消成功',
      REMOVEDLOG: ' 从日志中移除',
      CONFCLEANUP: '确认清理日志',
      DOREMOVEALL: '是否要从群组操作日志中删除所有列表?',
      GROUPCLEANSUCC: '清除设备操作日志成功',
      GROUPNOTCLEAN: '设备群组操作未被清除',
      CONFPRODUCTREM: '确认删除型号',
      DOPRODUCT: '是否要删除型号:',
      CONFRANREM: '确认移除无线接入网？',
      CONFAPNREM: '确认移除 WiFi AP 网络？',
      CONFMESHREM: '确认移除 WiFi Mesh 网络？',
      DORAN: '您要删除此无线接入网 ',
      DOAP: '您要删除此 WiFi AP 网络 ',
      DOMESH: '您要删除此 WiFi Mesh 网络 ',
      CONFPRODUCTBAN: '确认禁止型号',
      CONFRANBAN: '确认禁止无线电接入网络',
      CONFAPNBAN: '确认禁止 WiFi AP 网络',
      CONFMESHNBAN: '确认禁止 WiFi Mesh 网络',
      PRODUCTACCESS: '?此产品的设备將无法访问服务器.',
      PRODUCTSACCESS: '?这些产品的设备將无法访问服务器.',
      RANSACCESS: '？此无线电接入网络的设备将无法访问服务器。',
      APSACCESS: '？此 WiFi AP 网络的设备将无法访问服务器。',
      MESHSACCESS: '？此 WiFi Mesh 网络的设备将无法访问服务器。',
      CONFFILE: '确认删除文件?',
      DELFILESUCC: '删除文件成功',
      CONFSCRIPT: '确认删除脚本?',
      DOSCRIPT: '是否要删除脚本',

      CONFFLOW: '确认下载流程?',
      WORKFLOW: '流程',
      DOWORKFLOW: '是否要下载流程',
      CONFSELECT: '确认要下载选定的流程?',
      DOSELECTFLOW: '您想将选定的工作流程下载为多个文件吗？',
      DOSELECTFLOWASONE: '您想将选定的工作流程下载为一个文件吗？',
      DOWNSELECTFLOW: '下载选定的流程成功!',
      DOWNSELECTFILESUCCESS: '已成功下载所选文件！',
      SELECTFLOW: '删除选定的流程成功!',
      PLEASEFLOWS: '请先选择流程!',
      THEFLOW: '该流程',
      CONFDELFLOW: '确认删除流程?',
      DODELFLOW: '是否要删除流程',
      DELSUCC: '流程删除成功',
      FLOWID: '流程(ID:',
      FLOWDELFAIL: '流程删除失败',
      FLOWITEM: '所选流程包含一个或多个活动状态条目',
      CONFSELECTFLOW: '确认删除选定流程?',
      DODELSELECTFLOW: '是否要删除选定流程?',

      CONFCONFIGURATION: '确认下载配置?',
      CONFIGURATION: '配置',
      DOWORKCONFIGURATION: '是否要下载配置',
      CONFSELECTCONFIGURATION: '确认要下载选定的配置?',
      DOSELECTCONFIGURATION: '您想将选定的工作配置下载为多个文件吗？',
      DOSELECTCONFIGURATIONASONE: '您想将选定的工作配置下载为一个文件吗？',
      DOWNSELECTCONFIGURATION: '下载选定的配置成功!',
      DELSELECTCONFIGURATION: '删除选定的配置成功!',
      PLEASECONFIGURATIONS: '请先选择配置!',
      THECONFIGURATION: '该配置',
      CONFDELCONFIGURATION: '确认删除配置?',
      DODELCONFIGURATION: '是否要删除配置',
      DELCONFIGURATIONSUCC: '配置删除成功',
      CONFIGURATIONID: '配置(ID:',
      CONFIGURATIONDELFAIL: '配置删除失败',
      CONFIGURATIONITEM: '所选配置包含一个或多个活动状态条目',
      CONFDELSELECTCONFIGURATION: '确认删除选定配置?',
      DODELSELECTCONFIGURATION: '是否要删除选定配置?',

      CONFPOLICY: '确认下载策略？',
      DOWORKPOLICY: '您是否要下载策略',
      POLICYID: '策略(ID:',
      POLICYFLOWSUCC: '策略更新成功',
      POLICY: '策略',
      POLICYCLONESUCCESS: '策略克隆成功！',
      CONFDELPOLICY: '确认删除策略？',
      DODELPOLICY: '是否要删除策略',
      DELPOLICYSUCC: '策略删除成功',
      POLICYDELFAIL: '策略删除失败',
      PLEASEPOLICYS: '请先选择策略！',
      DOSELECTPOLICY: '您想将选定的策略下载为多个文件吗？',
      DOSELECTPOLICYSONE: '您想将选定的策略下载为一个文件吗？',
      CONFSELEPOLICY: '确认下载所选策略？',
      DOWNSELECTPOLICY: '所选策略下载成功！',
      POLICYITEM: '所选策略包含一个或多个活动状态条目',
      CONFDELSELECTPOLICY: '确认删除选定策略？',
      DODELSELECTPOLICY: '是否要删除选定策略?',
      DELSELECTPOLICY: '删除选定的策略成功!',

      CONPROFILE: '确认下载配置文件?',
      PROFILE: '配置文件',
      DOPROFILE: '是否要下载配置文件',
      CONOSELECT: '确认下载选择的配置文件？',
      DOSELECTPROFILE: '您想将选定的工作配置文件下载为多个文件吗？',
      DOSELECTPROFILEASONE: '您想将选定的工作配置文件下载为一个文件吗？',
      DOWNSELECTPROFILE: '选定的配置文件已成功下载！',

      PROVISIONINGFILE: "确认下载文件吗？",
      PROVISIONINGCONFILE: "确认下载文件",
      PROVISIONINGSELECT: "确认下载选择的文件吗？",
      PROVISIONINGDOSELECTFILE: "您想将选定的文件下载为多个文件吗？",
      PROVISIONINGDOSELECTFILEONE: "您想将选定的文件下载为一个文件吗？",
      PROVISIONINGCONOSELECT: "确认要下载选定的文件?",

      ENDGREATER: '结束时间应该大于当前时间!',
      STARTHAVEVALUE: '开始日期或结束日期应该是一个值!',
      ENDGREATERSTART: '结束日期应该大于开始日期!',
      STARTENDALUE: '开始时间或结束时间应该是一个值!',
      EXACTLYEQUAL: '时间不可能完全相等!',
      CONFSTAGE: '确认删除阶段吗?',
      BADREQ: '错误的请求',
      PARAMNEED: '保存前需要填写参数',
      FLOWSUCC: '流程更新成功',
      CONFIGURATIONSUCC: '配置更新成功',
      WASUPDATE: ')已更新!',
      CROSSCLICK: '点击+',
      FLOWADDSUCC: '流程新增成功',
      WASADD: ')已添加!',
      FORMFAIL: 'Form验证失败!',
      CONFOPER: '确认移除操作?',
      VERSIONERROR: '版本错误!',
      ONLY16: '只能由字母、数字和特殊字符(_ - .)组成1~16个字符.',
      FORKSUCC: '流程分叉成功',
      WASFORK: '已分叉,新优先级:',
      FLOWSPACE: '流程 ',
      OPERFORKSUCC: '操作分叉成功!',
      OPERFLOWSPACE: '操作 ',
      OPERATION: '操作',
      CONFDELOPER: '确认删除配置文件?',
      DODELOPER: '要删除配置文件吗',
      DELSUCCESS: '删除成功!',
      PLEOPERFIRST: '请先选择操作!',
      CONFSELECTOPER: '确认删除选定的配置文件?',
      DOSELOPER: '您要删除选定的配置文件吗?',
      DELOPERSUCC: '成功删除所选配置文件!',
      CONFACTIONRE: '确认移除操作?',
      STAGE: '阶段?',
      ACTION: '动作?',
      OPERUPDATESUCC: '配置文件更新成功',
      OPERADDSUCC: '配置文件添加成功',
      ALARMADDSUCC: '告警通知添加成功',
      CONFLICT: '冲突',
      ALARMNOTNAME: '告警通知名称是:',
      ALREADYEXIST: '已存在',
      CONTAINDATA: '包含冲突的数据',
      NAMEERROR: '名称错误!',
      ONLYNAMESYM: '只允许使用1-64个字符的字母、数字和特殊字符（_-space）.',
      CLONENOTI: '告警通知复制成功!',
      WANCLONE: '已复制,新配置:',
      CONFIGUPDATESUCC: '配置更新成功',
      SETOPERSUCC: '设置设备操作成功!',
      SETOPERFAIL: '设置设备操作失败!',
      TASKSUCC: '任务添加成功！',
      LABELSUCC: '编辑标签成功!',
      LABELFAIL: '编辑标签失败!',
      UPDATEDEV: '更新成功',
      CMDSENQUSUCC: '命令加入队列成功!',
      FORKNOT: '告警通知分叉成功!',
      WANIMPORT: ')未导入.',
      NOTIIMPORTFAIL: '告警通知导入失败',
      IMPORTSUCC: '导入失败!',
      GROUPCREATESUCC: '设备群组创建成功',
      IMPORTTOGROUP: '设备已导入到群组',
      GNAMEEXIST: '该设备群组名已经存在',
      SAVESRSSUCC: '保存设备报表设置成功',
      PLEASECONF: '主题确认后无法修改,请确认',
      ADDPRODSUCC: '添加产品型号成功',
      ADDPARAMSUCC: '添加产品参数成功',
      UPDATEPRODSUCC: '产品更新成功。',
      UPDATERANSUCC: '无线接入网络更新成功。',
      UPDATEAPNSUCC: 'WiFi AP 网络更新成功。',
      UPDATEMESHSUCC: 'WiFi Mesh 网络更新成功。',
      UPDATEPARAMSUCC: '更新产品参数成功',
      PLEASEFILL: '请先填写*所有项目!',
      UPDATEPERM: '更新权限类型成功!',
      UPDATEPERMDEV: '更新权限设备成功!',
      ADDSUCC: '添加成功',
      UPDATESUCC: '更新成功',
      DONE: '完成',
      WARNING: '警告!',
      PARAMNAMEEXIST: '参数名已存在!',
      SCRITEMPTY: '脚本列表为空!',
      USPGROUPEMPTY: '群组列表为空!',
      GROUPNAMEREQ: '群组名必填',
      OPERMODEREQ: '操作模式必填',
      FLOWIMPORTFAIL: '流程导入失败',
      SYSSETSUCC: '保存系统设置成功!',
      SYSSETFAIL: '保存系统设置失败!',
      DEVSETSUCC: '保存设备设置成功!',
      DEVSETFAIL: '保存设备设置失败!',
      PROSETSUCC: '保存产品设置成功!',
      PROSETFAIL: '保存产品设置失败!',
      SYSPRESUCC: '保存系统偏好成功!',
      SYSPREFAIL: '保存系统偏好失败!',
      DELETEUSER: '删除用户:',
      CHANGEUSER: '更改用户:',
      SPFAIL: ' 失败',
      ACTIVESUSS: '启用状态成功',
      ACTIVEFAIL: '启用状态失败',
      IMAGEOVERSIZE: '图像过大.',
      PWDNOTSAME: '确认密码不匹配。',
      PWDREQ: '密码必填.',
      FORMATINCORRECT: '过期日期格式不正确.',
      MUSTADMIN: 'ADMIN/CSR用户的产品型号必须为"ADMIN".',
      PRODUCTREQ: '产品型号必填.',
      SELECTADMIN: '只有ADMIN角色能选择"ADMIN".',
      ROLEREQ: '用户角色必填.',
      AVATARSUCC: '更新头像成功',
      AVATARFAIL: '更新头像失败.',
      EMAILSUCC: '更新邮箱成功',
      EMAILFAIL: '更新邮箱失败.',
      UPPERLIMIT: '可以添加的设备数量已超过上限!',
      MAXLIMIT: '最大设备上限为:',
      NOTSAVETITLE: '是否保存更改?',
      NOTSAVECONTENT: '您做的更改不会被保存。',
      CONFIRMROLE: '确认删除角色？',
      DOROLE: '您想删除该角色吗？',
      DOSELECTROLE: '您想删除选定的角色吗？',
      DELROLESUCC: '角色删除成功。',
      ROLEUPSUCC: '角色更新成功。',
      ROLEADDSUCC: '角色添加成功。',
      CHANGEWILLBELOSE: "如果不保存更改,则更改将丢失。",
      SYSTEMRETURNLOGIN: "系统即将返回登录页面",
      SAVEEVENTTIP: '请在此阶段保存之前选择通知事件！',
      SAVENOTIFYORDEVICEPARAMTIP: '请在此阶段保存前添加通知参数或设备参数条件！',
      SAVEDEVICEFAULTTIP: '请在此阶段保存之前添加设备故障参数条件！',
      ADDMEMBERSUC: '添加群组成员成功！',
      ADDMEMBERFAIL: '添加群组成员失败！',
      SMTPHEALTHY: '测试电子邮件成功。',
      SNMPHEALTHY: '测试 SNMP Trap 成功。',
      XMPPHEALTHY: '测试 XMPP 成功。',
      GENSERVERREPORT: '成功生成服务器报告。',
      WORKFLOWCLONESUCCESS: '流程克隆成功！',
      CONFIGURATIONWCLONESUCCESS: '配置克隆成功！',
      HASBEENCLONED: '已被克隆！',
      HASBEENFORKED: '已被分叉!',
      APNAMEEDITFAIL: '编辑 AP 名称失败。',
      ADDAPNSUCC: '添加 WiFi AP 网络成功，并同步创建同名组。',
      ADDRANSUCC: '添加无线接入网络成功，并同步创建同名组。',
      ADDMESHSUCC: '添加 WiFi Mesh 网络成功，并同步创建同名组。',
      TAGERROR: '仅允许输入0-32个字符，包括字母、数字、连字符(-)、下划线(_)、点(.)和空格'
    },
    PM: {
      PMWORD: 'PM',
      PMPARAM: 'PM 参数',
      PMCHART: 'PM 图',
      PERFORMANCEREPORT: '性能报告',
      PMSTATEITEMS: '选定的指标 ID 包含一个或多个跟踪项！',
      PERFORMANCEREPORT_DESCRIPTION: "生成的服务器（AMP）报告列表，包含设备和服务的状态等详细信息。",
      PMSTATISTICS: 'PM统计',
      SERIALNUMBER: '序列号',
      TARGETSERIALNUMBER: '目标序列号',
      TARGETSN: '目标序列号',
      TARGETGROUP: "目标群组",
      TARGET: "目标",
      GROUP: '群组',
      PARAMNAME: '参数名称',
      PARAMPATH: '参数路径',
      CONDITION: '条件',
      CONDITIONS: '条件',
      PARAMVALUE: '参数值',
      FROM: '从',
      TO: '到',
      CREATEDBY: '创建者',
      BEGINTIME: '开始时间',
      ENDTIME: '结束时间',
      TIME: '时间',
      UPDATETIME: '更新时间',
      MODELNAME: '模型名称',
      PRODUCTCLASS: '产品类别',
      TIMERANGE: '时间范围',
      OUI: 'OUI',
      METRICRULE: '度量规则',
      ALL: '全部',
      CONFIRM_DELETE: '确认删除',
      DO_DELETE: '您是否要删除所选 ',
      DODELETE: '您是否要删除 ',
      DELETESUCCESS: '删除成功',
      DELETEFAIL: '删除失败',
      PLESESELECT: '请选择 ',
      CONFIRM_REFRESH: '确认重新搜索',
      DO_REFRESHSELECT: '您要重新搜索所选的吗',
      DO_REFRESH: '您要重新搜索吗',
      REFRESHSUCCESS: '重新搜索成功',
      REFRESHFAIL: '重新搜索失败',
      EXPIREDUPDATESUCCESS: "所有过期数据已成功更新。",
      DATAEXPIRED: "数据已过期",
      ADDCONDITION: '添加条件',
      DELETECONDITION: '删除条件',
      SEARCH: '搜索',
      REFRESH: '刷新',
      REFRESHALL: "全部刷新",
      SEARCHRESULT: '搜索结果',
      VIEWCHART: '查看图表',
      CHART: '图表',
      SAVERULE: '保存规则名称',
      UPDATERULE: '更新规则名称',
      NAME: '名称',
      DESCRIPTION: '描述',
      CLOSE: '关闭',
      SAVE: '保存',
      DELETE: '删除',
      DELETEALL: '全部删除',
      GOTDEVICEINFO: '前往设备的设备信息页面',
      VIEWALLCHART: "请选择设备查看性能图表。（最多：20）",
      DURATION: "持续时间",
      NOTIFICATIONTOOLTIP: '转换为通知',
      REFRESHRULETOOLTIP: '重新搜索规则',
      DEVICECOUNT: '设备数量',
      FAIL: '失败！',
      REFRESHLOADING: '重新搜索加载中...',
      DOWNLOAD: "下载",
      CONFIRM_DOWNLOAD: "确认下载",
      DOWNLOADSUCCESS: "下载成功",
      DOWNLOADFAIL: "下载失败",
      DO_DOWNLOAD: "您是否要下载所选设备？",
      DODOWNLOAD: "您是否要下载 ",
      OPENSEARCHBAR: '打开搜索栏',
      HIDESEARCHBAR: '隐藏搜索栏',
      LASTMACHINGTIME: '上次匹配时间',
      RESEARCH: '重新搜索',
      RESEARCHRULETOOLTIP: '重新搜索规则',
      TRACKING: '跟踪',
      RESULT: '结果',
      RESEARCHALL: '重新搜索全部',
      RESULTTOOLTIP: '符合条件的设备数量',
      LASTMACHING: "最后匹配",
    },
    REFURBISHMENT: {
      REFURBISHMENTSTATISTICS: '翻新统计',
      REFURBISHMENTTIME: '翻新时间',
      REFURBISHMENTCOUNT: '翻新计数',
      INSTALLATIONTIME: '安装时间',
    },
    CARE: {
      TITLE: 'care',
      GENERALINFO: '基本信息',
      GENERALINFODESCRIPTION: '设备的一般信息。',
      MAP: '地点',
      MAPDESCRIPTION: '设备在 Google 地图上的位置。',
      WIFICLIENTLIST: 'WiFi 客户端列表',
      WIFICLIENTLISTDESCRIPTION: 'WiFi 客户端连接到设备。',
      ERRORSTATUS: '错误状态',
      ERRORSTATUSDESCRIPTION: '设备的错误列表，包括事件代码、错误描述和错误时间。',
      ERRORSTCARE: "获取设备",
      SELECTDEVICE: "选择设备",
      SERIALNUMBER: "序列号",
      PRODUCTNAME: "产品名称",
    },
    FIVEGC: {
      CELL_CONNECTED: "基站已连接",
      CELL_CONNECTED_DESCRIPTION: "显示已连接和正常工作的无线电数量。",
      CELL_DISCONNECTED: "基站已断开",
      CELL_DISCONNECTED_DESCRIPTION: "显示断开连接的无线电数量。",
      ACTIVE_UE: '活动用户',
      ACTIVE_UE_DESCRIPTION: '活动用户的基站数量。',
      NO_ACTIVE_UE: '没有活动用户',
      NO_ACTIVE_UE_DESCRIPTION: '显示无活动的基站数量。这不一定意味着没有用户设备连接；他们可能已连接但未处于活动状态。',
      CELLS_WITHOUT_ATTACHED_UE: '无用户设备连接的基站',
      CELLS_WITHOUT_ATTACHED_UE_DESCRIPTION: '显示已连接但未有用户设备连接的无线电数量。',
      CELLS_WITH_ACTIVE_UE: "有活跃用户的基站",
      CELLS_WITH_ACTIVE_UE_DESCRIPTION: "此 widget 以长条图形式显示基站活动状态，包括无活动、1到5个活跃用户、6到10个活跃用户、11到20个活跃用户和20个或更多活跃用户。",
      CELLS_LIST: "基站列表",
      CELLS_LIST_DESCRIPTION: "此 widget 显示连接至 5G Core 的基站列表。",
      ALARM_LIST: "警报列表",
      ALARM_LIST_DESCRIPTION: "警报列表提供系统中各种警报的信息。",
      FIVECORENETWORK: "5G 核心网络",
      FIVECORENETWORK_DESCRIPTION: "此 widget 作为链接到 5G Core，在系统/设置中配置了 5G Core URL。",
      CELL_THROUGHPUT: "基站吞吐量",
      CELL_THROUGHPUT_DESCRIPTION: "此 widget 显示基站当前的下载和上传吞吐量。",
      UE_THROUGHPUT_BY_CELL: "小区用户吞吐量",
      UE_THROUGHPUT_BY_CELL_DESCRIPTION: "小区用户吞吐量显示连接到特定小区的所有用户设备（UE）的总下载和上传吞吐量，提供其数据传输性能的概览。",
      UE_LIST: "UE列表",
      UE_LIST_DESCRIPTION: "UE信息提供了有关连接到5G核心网络的用户设备（UE）的详细信息。",
      UE_5QI_PACKET: "用户设备 5QI 数据包",
      UE_5QI_PACKET_DESCRIPTION: "此 widget 显示用户设备的 5QI 数据包，包括上行和下行的各种流量指标和丢包率。",
      ACTIVE: '激活',
      INACTIVE: '未激活',
      STATUS: '状态',
      NAME: '名称',
      STATE: '状态',
      gNBID: 'gNB ID',
      BAND: '频段',
      SESSIONS: '会话数',
      DATA: '数据',
      PLMN: 'PLMN',
      TAC: 'TAC',
      IP: 'IP',
      SEVERITY: '严重程度',
      ALARM_ID: '告警ID',
      EVENT_TYPE: '事件类型',
      EVENT_TIME: '事件时间',
      PROBABLE_CAUSE: '可能原因',
      SPECIFIC_PROBLEM: '具体问题',
      OBJ_CLASS: "对象类别",
      ADD_TEXT: "添加文本",
      THROUGHPUT_HISTORY: "吞吐量历史",
      STATUS_HISTORY: "状态历史",
      HISTORY: '历史',
      IMSI: 'IMSI',
      PHONENUMBER: "电话号码",
      IMEI: 'IMEI',
      SUB_TYPE: '子类型',
      REG_TYPE: "注册类型",
      LOCAL_ATTACHMENT: '本地附着',
      LAST_ACTIVITY_TIME: "最后活动时间",
      REGISTRATION_TIME: "注册时间",
      DEREGISTRATION_TIME: "注销时间",
      UL_THROUGHPUT: '上行吞吐量',
      DL_THROUGHPUT: '下行吞吐量',
      SUPI: 'SUPI',
      FIVEQI: '5QI',
      UL_INGRESS: '上行入口',
      UL_EGRESS: '上行出口',
      UL_DROPPED: '上行丢包',
      UL_TOTAL_INGRESS: '上行总入口',
      UL_TOTAL_EGRESS: '上行总出口',
      UL_TOTAL_DROPPED: '上行总丢包',
      DL_INGRESS: '下行入口',
      DL_EGRESS: '下行出口',
      DL_DROPPED: '下行丢包',
      DL_TOTAL_INGRESS: '下行总入口',
      DL_TOTAL_EGRESS: '下行总出口',
      DL_TOTAL_DROPPED: '下行总丢包',
      ALARM: '告警',
      ALARM_DESCRIPTION: '实时显示来自5G核心网络的最新告警，包括告警严重性、时间戳和简要描述，便于快速识别和响应网络问题。',
      ue_activity: '5GC 的 UE 活动',
      ue_activity_DESCRIPTION: '此饼图显示用户设备（UE）的活动状态。',
      ue_presence: '5GC 的 UE 存在',
      ue_presence_DESCRIPTION: '显示已连接（已连接）和未连接（已断开）的 UE 数量。',
      cells_info: '5GC 的基站',
      cells_info_DESCRIPTION: '5GC 基站提供关于连接到 5GC 网络的基站的详细信息。',
      ACTIVITY_DESCRIPTION: {
        ACTIVE: '活动段显示活动中的已连接 UE 数量。',
        DATA: '数据段显示系统中的数据会话数量。一个 UE 可能有多个数据会话。',
        CALLS: '通话段显示当前正在通话中的已连接 UE 数量。',
        INACTIVE: '非活动段显示未活动的已连接 UE 数量。'
      },
      PRESENCE_DESCRIPTION: {
        ATTACHED: '“已连接”状态表示用户设备已成功连接到网络。',
        DETACHED: '“已断开”状态表示用户设备已从网络断开或尚未连接到网络。'
      },
      LICENSE_DESCRIPTION: {
        UE: '显示使用中的 UE 许可席位数量和可用的 UE 许可席位数量。',
        NETWORK: '显示 PDN 许可席位数量和可用的 PDN 许可席位数量。',
        CELLS_4G: '使用中的 4G 基站许可席位数量和可用的 4G 基站许可席位总数。',
        CELLS_5G: '使用中的 5G 基站许可席位数量和可用的 5G 基站许可席位总数。',
      },
      ACTIVITY_CHART_DESCRIPTION: {
        ACTIVE_20PLUS: "表示拥有20个或更多活跃用户的基站数量。",
        ACTIVE_11To20: "表示拥有11到20个活跃用户的基站数量。",
        ACTIVE_6To10: "表示拥有6到10个活跃用户的基站数量。",
        ACTIVE_1To5: "表示拥有1到5个活跃用户的基站数量。",
        NOACTIVE: "表示没有活动的基站。这并不一定意味着没有用户连接；他们可能已连接但未活跃。"
      },
      ACTION: {
        RESTARTSYSTEM: '重新启动系统',
        DORESTARTSYSTEM: '如果继续，所有服务将暂时丢失！',
        RESTARTSYSTEM_SUCESS: '系统重新启动成功！',
        RESTARTSYSTEM_FAIL: '系统重新启动失败！',
        BACKUPCONFIGURATION: '备份配置',
        BACKUPCONFIGURATION_SUCESS: '配置备份成功！',
        BACKUPCONFIGURATION_FAIL: '配置备份失败！',
        RESTORECONFIGURATION: '恢复配置',
        RESTORECONFIGURATION_SUCESS: '配置恢复成功！',
        RESTORECONFIGURATION_FAIL: '配置恢复失败！',
        FACTORYRESET: '恢复出厂设置',
        DOFACTORYRESET: '您即将重置您的配置为出厂默认设置！',
        FACTORYRESET_SUCESS: '恢复出厂设置成功！',
        FACTORYRESET_FAIL: '恢复出厂设置失败！',
        REFRESHALL: "全部刷新",
        REFRESHALL_SUCESS: "全部刷新成功！",
        REFRESHALL_FAIL: "全部刷新失败！",
        SYSTEMMANAGEMENT: "系统管理",
      },
    },
    POWER: {
      ENERGYSAVING: '能源管理',
      STARTTIME_MUST_BE_EARLIER: "开始时间必须早于结束时间",
      STARTDATE_MUST_BE_EARLIER: "开始日期不能晚于结束日期。",
      POWER_CONSUMPTION_SUMMARY: "电力消耗总览",
      REAL_AVG_ENERGY: "实际平均能耗",
      NORMAL_STATE_ENERGY: "Normal 状态能耗",
      ENERGY_CONSUMPTION_BY_POLICY: "按策略划分的能耗",
      POWER_CONSUMPTION: "电力消耗",
      TX_POWER: "发射功率",
      NETWORK_USAGE: "网络使用量",
      UPLOAD: "上传",
      DOWNLOAD: "下载",
      UE_COUNT: "连接设备数",
      LOCATION: "位置",
      CURRENT_POLICY: "当前策略",
      POLICY_SETTING: "策略设置",
      ENERGY_POLICY: "节能策略",
      MILD_SLEEP_DESCRIPTION: "在保持设备正常运行的同时降低发射功率。",
      MODERATE_SLEEP_DESCRIPTION: "降低功耗并暂时关闭无线电以节省更多能源。",
      WAKEABLE_DEEPSLEEP_DESCRIPTION: "逐步关闭设备电源，但在需要时可自动唤醒。",
      DEEPSLEEP_DESCRIPTION: "完全关闭设备，需手动唤醒以恢复运行。",
      SCHEDULE_SETTING: "计划设置",
      POLICY_LIST: "策略列表",
      DURATION: "时长",
      ENERGY: "耗电量",
      POWER_CONSUMPTION_PER_DEVICE: "每台设备的电力消耗",
      DL_PRB_LOADING: "下行 PRB 使用率",
      UL_PRB_LOADING: "上行 PRB 使用率",
      TOTAL_POWER_CONSUMPTION: "总电力消耗",
      NAME: "名称",
      SCHEDULE: "排程",
      CONDITION: "条件",
      ACTIVE: "启用中",
      NO_POLICIES_FOUND: "未找到任何策略。",
      NO_POLICY_CONFIGURED: "该设备尚未配置节能策略。",
      ENABLE_TO_ADD_POLICIES: "启用节能控制后即可添加策略。",
      ENERGY_SAVING_NOT_ENABLED: "节能控制尚未启用。",
      NO_POLICY_SETTINGS_AVAILABLE: "该设备暂无可用的策略设置。",
      ENABLE_TO_CONFIGURE_POLICY: "请启用节能控制以配置或查看策略详情。",
      ENERGY_MODE: "节能模式",
      TRAFFIC_LOADING: "流量负载",
      UE_CONTEXT: "UE 连接上下文",
      POLICY_DISABLE_TITLE: "停用省电模式？",
      POLICY_DISABLE_TEXT: "停用后，设备将恢复为一般功耗设置，是否确定要关闭？",
    }
  }
}
