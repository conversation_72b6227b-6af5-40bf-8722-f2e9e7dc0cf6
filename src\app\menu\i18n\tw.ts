export const locale = {
  lang: 'tw',
  data: {
    MENU: {
      HOME: 'Home',
      SAMPLE: 'Sample'
    },
    DASHBOARD: {
      TITLE: "儀表板",
      AVGSESSIONS: {
        TITLE: "平均會話",
        VIEWDETAILS: "查看詳情",
        LAST1DAY: "過去24小時",
        LAST7DAYS: "最近7天",
        LAST15DAYS: "最近15天",
        LAST28DAYS: "最近28天",
        LAST30DAYS: "最近30天",
        LASTMONTH: "上個月",
        LASTYEAR: "去年"
      },
      MAP: "位置",
      SESSIONDURATION: "會話時長",
      SESSIONDURATIONDESCRIPTION: "所有設備在固定時間間隔的會話時長平均值歷史圖。會話時長：設備與AMP之間會話的總時間。",
      SESSIONRATE: "會話速率",
      SESSIONRATEDESCRIPTION: "在線設備的CWMP請求頻率歷史圖。",
      LATENCY: "請求延遲",
      LATENCYDESCRIPTION: "所有設備在固定時間間隔的請求延遲平均值歷史圖。請求延遲：設備與AMP之間請求的總時間。",
      REGISTERED_COUNT_DISTRIBUTION: "註冊設備",
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: "每個產品的註冊設備分佈圖。",
      PROVISIONINGTYPEDISTRIBUTION: "供應類型分配",
      PROVISIONINGTYPEDISTRIBUTION_DESCRIPTION: "每個注册產品選擇的所有配寘類型的分佈。",
      ONLINE_COUNT_DISTRIBUTION: "在線設備",
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: "每個產品的在線設備數量分佈圖。",
      HISTORYONLINEDEVICE: "在線設備",
      ONLINEDEVICEDESCRIPTION: "每個產品的在線設備數量歷史圖。",
      SOFTWARE_VERSION_DISTRIBUTION: "軟體版本",
      SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION: "在線設備的軟體版本分佈圖。",
      PROVISIONING_CODE_DISTRIBUTION: "配置代碼",
      PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION: "在線設備的配置代碼分佈圖。",
      XMPP_STATUS_DISTRIBUTION: "XMPP狀態",
      XMPP_STATUS_DISTRIBUTION_DESCRIPTION: "在線設備的XMPP狀態分佈圖。",
      IMS_STATUS_DISTRIBUTION: "IMS狀態分佈",
      IMS_STATUS_DISTRIBUTION_DESCRIPTION: "在線設備的IMS註冊狀態分佈圖。",
      SIM_STATUS_DISTRIBUTION: "SIM狀態",
      SIM_STATUS_DISTRIBUTION_DESCRIPTION: "在線設備的SIM連接狀態分佈圖。",
      IPSEC_STATUS_DISTRIBUTION: "IPSec狀態",
      IPSEC_STATUS_DISTRIBUTION_DESCRIPTION: "在線設備的IPSec隧道連接狀態分佈圖。",
      TOTAL: "總數",
      ONLINE: "在線",
      ONLINE_DEVICE: "在線設備",
      ONLINE_DEVICE_DESCRIPTION: "在線設備的總數。",
      GROUPS_COUNT: "組",
      GROUPS_COUNT_DESCRIPTION: "組的總數。",
      ONLINE_USERS: "在線用戶",
      ONLINE_USERS_DESCRIPTION: "在半小時內登入的用戶總數。",
      UE_COUNT: "用戶設備",
      UE_COUNT_DESCRIPTION: "連接到小基站的用戶設備總數。",
      ALARMS_TOTAL: "告警總數",
      ALARMS_TOTAL_DESCRIPTION: "設備報告的告警總數。",
      ALARMS_SERVERITY: "告警",
      ALARMS_SERVERITY_DESCRIPTION: "不同嚴重級別的告警總數，如嚴重、主要、次要和警告。",
      GROUP_LIST: "組列表",
      GROUP_LIST_DESCRIPTION: "詳細資訊列表，包括設備數量、不同行級別的告警。",
      COVERMAP: "覆蓋地圖",
      COVERMAP_DESCRIPTION: "顯示組內設備的位置和無線覆蓋範圍。",
      ALARM_LIST: "告警列表",
      ALARM_LIST_DESCRIPTION: "列出所有設備報告的告警，包括已清除和未清除的告警，顯示嚴重級別、事件時間和可能的原因。",
      SYSTEM_EVENT_LIST: "系統事件",
      SYSTEM_EVENT_LIST_DESCRIPTION: "列出所有與通信和訪問失敗相關的日誌事件。",
      STSTEM_INFORMATIONS: "系統資訊",
      STSTEM_INFORMATIONS_DESCRIPTION: "AMP系統資訊或伺服器報告內容。",
      TOTAL_CLIENTS: "WiFi RSSI分佈總數",
      TOTAL_CLIENTS_DESCRIPTION: "不同RSSI水平的WiFi客戶端分佈圖。",
      TOTAL_CLIENTS_COUNT: "WiFi客戶端",
      TOTAL_CLIENTS_COUNT_DESCRIPTION: "連接到WiFi AP的WiFi客戶端總數。",
      EXCELLENT_CLIENTS: "優秀",
      GOOD_CLIENTS: "良好",
      POOR_CLIENTS: "較差",
      STATISTICSOFTOTALCLIENTS: "WiFi RSSI分佈記錄總數",
      STATISTICSOFTOTALCLIENTS_DESCRIPTION: "不同RSSI水平的WiFi客戶端歷史圖。",
      GROUPNAME: "組名稱",
      PRODUCTNAME: "產品名稱",
      MANAGEMENTSCOPE: "管理範圍",
      REGION: "區域",
      LOCATION: "位置",
      CONFIGURATION: "配置",
      APS: "AP總數",
      TOTALCLIENTS: "總客戶端數",
      EXCELLENTCLIENTS: "優秀客戶端",
      GOODCLIENTS: "良好客戶端",
      POORCLIENTS: "較差客戶端",
      EXCELLENT: "優秀",
      GOOD: "良好",
      POOR: "較差",
      EXCELLENT_DESCRIPTION: "RSSI > -65dBm",
      GOOD_DESCRIPTION: "-65dBm < RSSI < -80dBm",
      POOR_DESCRIPTION: "RSSI < -80dBm",
      TOTALCLIENTSTABLE: "客戶端表格",
      CLIENTS: "WiFi客戶端資訊",
      ONLINEAPS: "在線AP",
      TAGS: "標籤",
      GROUPSLOCATION: "組位置",
      GROUPSLOCATION_DESCRIPTION: "在地圖上顯示所有組的位置和基本資訊。",
      DEVICESLOCATION: "設備位置",
      DEVICESLOCATION_DESCRIPTION: "在地圖上顯示所有設備的位置和基本資訊。",
    },
    DEVICES: {
      WIFICLIENT: 'WiFi客戶端',
      WIFIAPNAME: 'WiFi AP 名称',
      WIFIAPROLE: 'WiFi AP 角色',
      WIFIAPCONFVERSION: 'WiFi AP 配置版本',
      WIFIAPNCONFVERSION: 'WiFi APN 配置版本',
      TAGS: '標籤',
      LIST: '設備列表',
      SERIAL_NUMBER: '序號',
      MODEL_NAME: '型號名稱',
      FIRMWARE: '韌體',
      LABEL: '標牌',
      GROUP: '群組',
      PRODUCT: '產品',
      LAST_CONNECTED: '最後連接',
      LAST_EVENT: '最後事件',
      UPTIME: '運行時間',
      TIME_ZONE: '時區',
      ACTIVE: '活躍',
      BAND: '頻段',
      CHANNEL: '頻道',
      BANDWIDTH: '頻寬',
      UTILIZATION: '利用率',
      RECEIVED: '已接收',
      SENT: '已傳送',
      DOWNLINK_RATE: '下行速率',
      UPLINK_RATE: '上行速率',
      MODE: '模式',
      CONNECTTIME: '連接時間',
      ERRORCODE: '錯誤代碼',
      ERRORDESCRIPT: '錯誤描述',
      ERRORTIME: '錯誤時間',
      DAILYSENT: '每日已發送',
      DAILYRECEIVED: '每日已接收',
      ACCESSCOUNT: '訪問次數',
      UNINSTALLEDTIME: '卸載時間',
      DATASIZE: '數據大小',
      CACHESIZE: '緩存大小',
      SERVICEDISCOVERYSERVER: '服務發現伺服器',
      ACTIVE_BCG_SERVERS: '活躍的BCG伺服器',
      POWERCONSUMPTION: '耗電量',
      STATE: '狀態',
      CLEAREDTIME: '清除時間',
      CONTAINERVERSION: '容器版本',
      APPLICATIONVERSION: '應用程式版本',
      ENABLE: '啟用',
      CELL_RESERVED_FOR_OPERATOR_USE: '為運營商保留的小區',
      EUTRA_CARRIER_ARFCN: 'EUTRA載波ARFCN',
      BLACKLISTED: '已列入黑名單',
      VENDORCLASSID: '供應商分類ID',
      EARFCNDOWNLOAD: 'EARFCN 下載',
      DOWNLOADBANDWIDTH: '下載頻寬',
      UPLOADBANDWIDTH: '上傳頻寬',
      REFERENCESIGNALPOWER: '參考信號功率',
      SECURITY: '安全性',
      SEVERITY: '嚴重程度',
      ALARMID: '警報ID',
      EVENTTYPE: '事件類型',
      EVENTTIME: '事件時間',
      PROBABLECAUSE: '可能原因',
      SPECIFICPROBLEM: '具體問題',
      ACKUSER: '確認使用者',
      ACKTIME: '確認時間',
      ADDITIONALTEXT: '附加文字',
      ADDITIONALINFORMATION: '附加資訊',
      PEER: '對等方',
      DURATION: '持續時間',
      CONNECT: '連接',
      START: '開始',
      END: '結束',
      UPLOAD: '上傳',
      DOWNLOAD: '下載',
      DOWNLOADDATAMODEL: '下載整個數據模型',
      DOWNLOADALL: '下載所有',
      DOWNLOADSELECT: '下載已選擇',
      TIME: '時間',
      UPLOADRESULT: '上傳測試結果',
      DOWNLOADRESULT: '下載測試結果',
      EVENT: '事件',
      LOGLEVEL: '日誌級別',
      REQUEST: '請求',
      CREATED: '已建立',
      IMEI: 'IMEI',
      MAC: 'MAC',
      IP: 'IP',
      RSSI: 'RSSI',
      SSID: 'SSID',
      BSSID: 'BSSID',
      APSN: 'AP S/N',
      APNAME: 'AP Name',
      APMAC: 'AP MAC',
      APIP: 'AP IP',
      LISTDESCRIPTION: '列出所有允許的設備，並提供一般資訊，如序號、MAP和IP地址。',
      SMALLCELL_LIST: '小型基站列表',
      SMALLCELL_LISTDESCRIPTION: '列出所有屬於無線接入網的允許小型基站，並提供特定資訊，如UE、PCI和GNB ID。',
      WIFI_AP_LIST: 'WiFi AP列表',
      WIFI_AP_LISTDESCRIPTION: '列出所有屬於WiFi AP網路的允許WiFi AP，並提供特定資訊，如客戶端、頻道和頻道利用率。',
      WIFI_MESH_LIST: 'WiFi Mesh列表',
      WIFI_MESH_LISTDESCRIPTION: '列出所有屬於WiFi Mesh網路的允許WiFi Mesh AP，並提供特定資訊，如客戶端、頻道和頻道利用率。',
      CURRENTNUMBERS: '當前警報數。',
      ALARMMGMTDESCRIPTION: "列出所有由設備報告的警報，包括已清除的、未清除的，並附上嚴重性、事件時間和可能原因。",
      REGISTERDEVICE: '註冊設備',
      REGISTERSMALLCELLDEVICE: '註冊小型基站設備',
      REGISTERAPDEVICE: '註冊WiFi AP設備',
      REGISTERNESHDEVICE: '註冊WiFi Mesh設備',
      LIVEUPDATE: '實時更新',
      SPEEDTEST: '速度測試',
      SPEEDTESTDESCRIPTION: "設備使用TR-143進行上傳和下載速度測試，以衡量網絡性能，確保數據傳輸速率最佳。",
      FIVECORE: '5G 核心網',
      FIVECORENETWORK: '5G核網',
      FIVECORENETWORK_DESCRIPTION: '提供連接至系統/設定中配置的5G核心的鏈接，並提供5G核心的URL。',
      CELL_THROUGHPUT: '小區吞吐量',
      CELL_THROUGHPUT_DESCRIPTION: "有關小型基站下載和上傳吞吐量的信息。",
      UE_LIST: 'UE列表',
      UE_LIST_DESCRIPTION: '附加到5G核心網路的UE（用戶設備）和脫離的UE，提供詳細信息，如狀態、IMSI、IMEI、GNB ID和IP地址。',
      UE_5QI_PACKET: 'UE 5QI封包',
      UE_5QI_PACKET_DESCRIPTION: '列出UE的5QI封包，包括上行和下行的各種流量指標和丟包率。',
      BULKDATAPROFILE_DESCRIPTION: "列出大容量數據檔案，並提供詳細信息，如別名、狀態、URL、參數和編碼類型。",
      SOFTWAREMODULES_DESCRIPTION: "列出軟體模組，並提供詳細信息，如UUID、別名、名稱、URL和最後更新時間。",
      ONLINE_COUNT_DISTRIBUTION: '在線設備',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: '每個產品的在線設備計數分布圖。',
      REGISTERED_COUNT_DISTRIBUTION: '註冊設備',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: '每個產品的註冊設備計數分布圖。',
      CONNECTIVITYTEST: '連接測試',
      REBOOT: '重啟',
      FACTORYRESET: '應用出廠重置',
      UPLOADLOG: '上傳日誌',
      UPGRADEFIRMWARE: '升級',
      GENERATEREPORT: '生成報告',
      ADDFILE: '添加檔案指針',
      SETTING: '管理偏好設定',
      GENERAL: '一般',
      GENERALSTATUS: '一般狀態',
      OPERATION: '分配操作',
      PROTOCOL: '協議',
      SELECTPROTOCOL: '選擇協議',
      NETCONFAUTH: 'NETCONF支持密碼和私鑰驗證，請輸入密碼或私鑰。',
      REGISTER: '註冊',
      CONNECTIONREQ: '連接請求',
      TELEMETRY: '遙測',
      INFO: '資訊',
      MAP: '地圖',
      FAPCONNECTEDCLIENTS: 'FAP已連接客戶端',
      FAPCONNECTEDCLIENTSDESCRIPTION: '已連接的FAP客戶端。',
      GENERALINFO: '一般資訊',
      GENERALINFODESCRIPTION: "設備的一般資訊，如序號、型號名稱和軟體版本。",
      CELLULARSTATUS: '行動狀態',
      CELLULARSTATUSDESCRIPTION: '行動資訊，如服務狀態、接入技術、頻段、RSRP、RSRQ和RSRI。',
      WORKFLOWLIST: '工作流程列表',
      WORKFLOWLISTDESCRIPTION: '目前對此設備應用的工作流程列表。',
      DATAUSAGE: '行動數據使用量',
      DATAUSAGEDESCRIPTION: '行動數據使用量的歷史圖表。',
      CLIENTS: '客戶端',
      CLIENTDESCRIPTION: '客戶端數量，如手機、筆記本電腦、平板電腦。',
      UE: 'UE',
      UEDESCRIPTION: '附加至小型基站的UE總數。',
      SIMCARDINFO: 'SIM卡資訊',
      SIMCARDINFODESCRIPTION: 'SIM卡資訊，如狀態、ICCID、IMSI、IMPI和IMPU。',
      WIFISTATUS: 'WiFi無線狀態',
      WIFISTATUSDESCRIPTION: "所有可用WiFi頻段/介面的當前無線設置和狀態。",
      WIFICHANNELUTLILIZATION: 'WiFi頻道利用率',
      WIFICHANNELUTLILIZATIONDESCRIPTION: '所有可用頻段的流量所產生的在空頻道的負載。',
      CONNECTEDHOSTS: '已連接的主機',
      CONNECTEDHOSTSDESCRIPTION: '有關連接到設備的主機的信息。',
      REGSTATUS: '註冊狀態',
      REGSTATUSDESCRIPTION: '註冊資訊，如最後註冊時間、最後斷開時間和最後斷開原因。',
      ERRORSTATUS: '錯誤狀態',
      ERRORSTATUSDESCRIPTION: '設備報告的錯誤列表，如錯誤描述和錯誤時間。',
      SPECTRUMDESCRIPTION: "有關小型基站部署模式的資訊，具體頻譜如NR頻段、ARFCN和TDD時間槽。",
      APPLIST: '應用列表',
      APPLISTDESCRIPTION: '有關設備應用的資訊。',
      SERVICEPRO: '服務提供者',
      SERVICEPRODESCRIPTION: '設備的服務提供者列表。',
      SPECTRUM: '頻譜',
      PLMNNEIGHBORLIST: '鄰居/PLMN列表',
      NEIGHBORLIST: '鄰居列表',
      NEIGHBORLISTDESCRIPTION: '有關ANR鄰居列表、切換列表和PLMN列表的資訊。',
      HANDOVERLIST: '已配寘鄰居清單',
      ANRNEIGHBORLIST: 'ANR鄰居列表',
      UTRANEIGHBORLIST: 'Utra鄰居列表',
      PLMNLIST: 'PLMN列表',
      APPSTATUS: '設備應用狀態',
      APPSTATUSDESCRIPTION: '設備應用的功耗。',
      LXCSTATUS: 'LXC狀態',
      LXCSTATUSDESCRIPTION: '設備的Linux容器狀態。',
      SUBSCRIPTION: 'USP 訂閱',
      SUBSCRIPTIONDESCRIPTION: '通過USP列出訂閱項目，提供詳細資訊，如別名、狀態、通知類型和接收者。',
      BULKDATAPROFILE: '大容量數據檔案',
      SOFTWAREMODULES: '軟件模塊',
      CONTROLLERTRUSTROLE: '控制器信任角色',
      CONTROLLERTRUSTROLEDESCRIPTION: '列出包含別名、狀態、許可權、條目數等詳細資訊的控制器信任角色',
      FIRMWAREIMAGES: '固件映像',
      FIRMWAREIMAGESDESCRIPTION: '列出包含别名、状态、啟動失敗日誌等详细信息的固件映像',
      BOOTFAILURELOG: "啟動失敗日誌",
      AVAILABLE: "可用",
      DEVICEACTION: '設備操作',
      CURRENTALARMLIST: '當前警報列表',
      ALARMLIST: '警報列表',
      ACKALARM: '確認警報',
      ALARMMGMT: '警報管理',
      ALARMMGMDESCRIPTION: '列出所有由設備報告的警報，包括已清除的、未清除的，並附上嚴重性、事件時間和可能原因。',
      HISTORYALARM: '歷史警報管理',
      CURRENTYALARM: '當前警報列表',
      DATAMODEL: '數據模型',
      SELECTDATAMODEL: '選擇數據模型',
      DATANODE: '數據節點',
      DATANODEDESCRIPTION: "顯示設備向AMP報告的所有參數節點的樹狀結構。",
      PARAMETERDATADESCRIPTION: "所選參數節點的詳細信息，如子節點、參數名稱、屬性、路徑和值。",
      PARAMETERDATA: '參數數據',
      SELECTDATANODE: '請從數據節點中選擇參數',
      LOGS: '日誌',
      LOG: '日誌',
      SESSIONLOG: '會話日誌列表',
      SESSIONLOGDESCRIPTION: 'AMP與設備之間的會話日誌列表。',
      SESSIONLOGRATE: '報告率',
      SESSIONLOGRATEDESCRIPTION: "過去24小時內，設備向AMP的定期報告率（計數）的統計。",
      SESSLOG: '會話日誌',
      PENDINGLOG: '待處理操作日誌列表',
      PENDINGLOGPENDINGLOGS: '待設備接收的待處理操作任務列表。',
      OPERATIONLOGS: '操作日誌列表',
      OPERATIONLOGSDESCRIPTION: '設備執行的AMP分配的操作任務列表，以及報告的結果。',
      CALLLOG: '通話日誌列表',
      CALLLOGDESCRIPTION: '通話記錄的列表，如對等方、類型和持續時間。',
      SPEEDTESTHISTORY: '速度測試歷史',
      CONNECTIVITYTESTHISTORY: '連接測試歷史',
      CONNECTIVITYTESTHISTORYDESCRIPTION: '連接測試歷史。',
      DEVICEREPORTLIST: '設備報告列表',
      DEVICEREPORTLISTDESCRIPTION: '列出生成的摘要報告，並提供設備的關鍵參數。',
      PMLOG: 'PM KPI日誌',
      PMLOGDESCRIPTION: '列出由小型基站報告的KPI數據記錄。',
      ADVANCE: '進階',
      CBSDSTATUS: 'CBSD狀態',
      CBSDSTATUSDESCRIPTION: '有關公民寬頻服務設備的信息，如SAS提供者、CBSD ID、GPS狀態和授權狀態。',
      CBSDCONDIGS: 'CBSD配置',
      CBSDCONDIGSDESCRIPTION: '公民寬頻服務設備的配置，如CBSD序號、型號和軟體版本。',
      TERMINAL: '終端',
      TERMINALDESCRIPTION: '提供支持通過終端進行遠程實時操作的設備。',
      COMMANDXML: '命令XML',
      COMMANDXMLDESCRIPTION: '設備命令XML。',
      ACSSTATUS: 'ACS狀態',
      DEVICESTATUS: '設備狀態',
      SASTATUS: '小區狀態',
      RFCONTROL: '小區RF控制',
      RFCONTROLDESCRIPTION: '提供RF相關的設置和開關。',
      ANTENNABEAM: '天線波束',
      BEAMMENU: '波束選單',
      ANTENNABEAMDESCRIPTION: "提供變更小型基站的天線波束角度。",
      BEAMID: '波束ID',
      GPSANTENNA: 'GPS天線',
      GPSANTENNAPATH: 'GPS天線路徑',
      ANTENNAPATH: '天線路徑',
      GPSANTENNADESCRIPTION: '提供選擇GPS天線路徑作為外部或內部。',
      DEPLOYMENTMODE: '部署模式',
      DEPLOYMENTMODEDESCRIPTION: '設備的部署模式。',
      HWMONITOR: '機器診斷',
      HWMONITORDESCRIPTION: "提供啟用小型基站的診斷，如CPU、溫度和功耗。",
      RECONNECT: '重連',
      DISCONNECT: '斷開連接',
      DOWNLOADLOG: '下載日誌',
      REPEATLASTCMD: '重複最新命令',
      CLEARSCROLLBACK: '清除',
      CELLULARDIAGNOSTIC: '請求Cellular接口診斷',
      WIFIDIAGNOSTIC: '請求WIFI接口診斷',
      SYSHEALTHREBOOT: '請求系統運行狀態重啓',
      DIFFRENTPROTOCOLNOTALLOWED: '不允許批量注册兩個或多個不同的協定設備',
      DEVICEERRCODE1: "不允許將設備添加到不存在的產品中",
      DEVICEERRCODE2: "設備已經被注册",
      DEVICEERRCODE3: '無效序號',
      INVALIDPRODUCT: '無效產品',
      DOFURBISHMENT: '將被翻新，所有設備數據都將被删除。是否仍要繼續？',
      CONFURBISHMENT: '翻新過程',
      MAXUES: '最大同時連線用戶數:',
      OSMERROR: "地圖加載錯誤。請檢查網路連線或稍後重試。",
      GOOGLEMAPERROR: "無法加載 Google Maps API。請檢查 Google Maps API 金鑰或網路連線。",
      PERMISSIONNUMBEROFENTRIES: "條目數",
      ACTION: {
        ASSIGN_OPERATION: '操作已分配',
        ASSIGN_OPERATION_SUCC: '操作分配成功。',
        ASSIGN_OPERATION_FAIL: '操作分配失敗。',
        ASSIGN_OPERATION_SUCCESS_MESSAGE: '操作成功。',
        ASSIGN_OPERATION_WARNING_MESSAGE1: '等待',
        ASSIGN_OPERATION_WARNING_MESSAGE2: '獲取操作。',
        ASSIGN_OPERATION_FAIL_MESSAGE: '操作分配失敗。',
        CONFIRM_LIVE_UPDATE: '確認即時更新嗎?',
        DO_LIVE: '是否要即時更新設備 ',
        DO_LIVE_UPDATE: '確定要即時更新選定的設備嗎?',
        LIVESUCCESS: '即時更新成功!',
        LIVEFAIL: '即時更新失敗!',
        CONFIRM_REBOOT: '重啟',
        DOREBOOT: '在重啟期間，設備提供的所有服務（包括遠程管理）將暫時中斷幾分鐘。',
        SELECTREBOOT: '在重啟期間，選定設備提供的所有服務（包括遠程管理）將暫時中斷幾分鐘。',
        ABOUT_TO_REBOOT: '即將重新啟動...',
        REBOOT_SUCCESS: '重啓成功!',
        WAIT_REBOOT: '等待重啟...',
        REBOOTFAIL: '重啓失敗!',
        REBOOT_TIMED_OUT: 'Reboot timed out!',
        SHUTDOWN_SUCCESS: '關閉成功！',
        CONFIRMFACTORYRESET: '恢復出廠設置',
        DOFACTORYRESET: '設備將恢復到原始製造狀態，所有用戶設置將被清除。',
        SELECTFACTORYRESET: '選定設備將恢復到原始製造狀態，所有用戶設置將被清除。',
        ACTION_CONFIRM: '您是否仍然要繼續?',
        ABOUT_TO_FACTORYRESET: '即將恢復出廠設定...',
        FACTORYRESETSUCC: '恢復出廠設置成功!',
        FACTORYRESETFAIL: '恢復出廠設置失敗!',
        FACTORYRESET_TIMED_OUT: '恢復出廠設定超時！',
        CONFIRMUPLOADLOG: '上傳日誌',
        DOUPLOADLOG: '請求設備將其系統日誌上傳到遠程URL以進行進一步故障排除。上傳URL可以在產品和系統頁面的設置文件部分進行配置。',
        SELECTUPLOADLOG: '請求選定設備將其系統日誌上傳到遠程URL以進行進一步故障排除。上傳URL可以在產品和系統頁面的設置文件部分進行配置。',
        UPLOADLOG: "上傳日誌",
        UPLOADLOGSUCC: "上傳日誌成功！",
        UPLOADLOGSUCCMESSAGE: "裝置操作已成功建立！",
        UPLOADLOGFAIL: '上傳日誌失敗!',
        ABOUT_TO_UPLOADLOG: '即將上傳日誌...',
        UPLOADLOG_TIMED_OUT: '上傳日誌超時!',
        CONFIRMFIRMWARE: '升級',
        SUCCESSFIRMWARE: '升級成功！',
        UPGRADE_BEING_DOWNLOADED: '正在下載中...',
        ASSIGN_UPGRADE_OPERATION: '分配升級操作',
        UPGRADE_STATUS_INSTALLING: '正在安裝...',
        UPGRADE_BEING_ACTIVATED: '正在激活...',
        UPGRADE_DEVICE_BEING_RESTARTED: '設備正在重新啟動...',
        WAIT_UPGRADE: '等待通知...',
        UPGRADING: '正在升級中...',
        UPGRADE_COMPLETE_REBOOTING: '升級完成，重啟',
        UPGRADE_TIMED_OUT: '升級已逾時!',
        UPGRADEFIRMWARE: "升級",
        UPGRADEFIRMWARESUCCMESSAGE: "固件升級的裝置操作已成功建立！",
        FAILFIRMWARE: '升級失敗！',
        FAILFIRMWAREPENDING: '升級待處理操作日誌。',
        REBOOTPENDING: '重新啟動在待處理操作日誌中。',
        FACTORYRESETPENDING: '恢復出廠設置在待處理操作日誌中。',
        UPLOADLOGPENDING: '上傳日誌在待處理操作日誌中。',
        DOFIRMWARE: '在固件升級過程中，設備提供的所有服務（包括遠程管理）將暫時中斷幾分鐘。',
        SELECTFIRMWARE: '在固件升級過程中，選定設備提供的所有服務（包括遠程管理）將暫時中斷幾分鐘。',
        CONFIRM_GENERATE_REPORT: '生成報告',
        CONFIRM_GENERATE: '生成',
        CONFIRM_REPORT: '報告',
        DO_GENERATE_REPORT: '生成設備關鍵參數的匯總報告。該報告可以在日誌頁面的設備報告列表Widget中找到。',
        SELECT_GENERATE_REPORT: '生成選定設備關鍵參數的匯總報告。該報告可以在日誌頁面的設備報告列表Widget中找到。',
        SUCCESSGENE: '生成報告成功!',
        FAILGENE: '生成報告失敗!',
        CONFIRM_DELETE: '確認刪除',
        DO_DELETE: '確認要刪除選定的設備嗎?',
        CONFIRM_APPLY: '確認應用',
        DO_APPLY_CONFIGRUATION: '您想要應用這個配置嗎？',
        IMPORT_JSON_FILE_MESSAGE: '請匯入配置 JSON 檔案。',
        APPLY_SUCC: '應用成功！',
        APPLY_FAIL: '應用失敗！',
        PLESESELECT: '請選擇設備',
        CONFIRMReset: '確認重置',
        SUREReset: '確定要重置嗎?',
        RESETPERSONALTHEME: '是否確定要重置所有頁面的個人主題?',
        RESETCURRENTPERSONALTHEME: '是否確定要重置當前頁面的個人主題?',
        RESETALL: '重置所有頁面',
        RESETCURRENTPAGE: '重置當前頁面',
        RESETSuccess: '重置Widget佈局成功',
        CONFIRMSave: '確認保存嗎?',
        SURESave: '確定要保存當前的版本嗎?',
        SAVESuccess: '保存Widget佈局成功',
        DODELETE: '是否要刪除: ',
        DELETESUCCESS: '刪除成功!',
        DELETEFAIL: '刪除失敗!',
        CONFIRMBAN: '確認禁止嗎?',
        BANSUCCESS: '禁止成功!',
        BANFAIL: '禁止失敗!',
        DOBAN: '是否要禁止 ',
        BANSELECT: '是否要禁止選定的設備嗎?',
        CONFIRMRegister: '確認註冊嗎?',
        ONLYVALID: '勾選會自動保留合法的設備',
        SUCCESSRegister: '註冊設備成功!',
        FAILRegister: '註冊設備失敗!',
        DORegister: '是否要註冊設備',
        FAIL: '失敗!',
        CONFIRMOPER: '確認刪除操作日誌',
        OPERSELECT: '是否要刪除選定設備的操作日誌?',
        CONNECTIVITYTESTSELECT: '您是否要刪除所選的連通性測試歷史記錄?',
        CONNECTIVITYTEAllCONFIRM: '確認清除連接測試歷史記錄嗎?',
        CONNECTIVITYTEAll: '您是否要清除連接測試歷史記錄?',
        SPEEDTESTSTSELECT: '您是否要刪除選定的速度測試歷史記錄？',
        SPEEDTESTAllCONFIRM: '確認清除速度測試歷史記錄？',
        SPEEDTESTAll: '您是否要清除速度測試歷史記錄？',
        PLESEOPER: '請選定完成的操作日誌',
        DOTAG: '是否要刪除標簽',
        TAGSUCC: '刪除標簽成功!',
        TAGFAIL: '刪除標簽失敗!',
        ADDTAGSUCC: '新增標簽成功!',
        ADDTAGFAIL: '新增標簽失敗!',
        UPDAGEDEVICE: '更新設備!',
        CONFIRMCANCEL: '確認取消操作?',
        DOCANCEL: '是否要取消: ',
        CANCELSUCC: '取消成功!',
        CANCELFAIL: '取消失敗!',
        SELECTCANCEL: '是否取消選中的待處理操作日誌?',
        PLEASECANCEL: '請選定待處理操作日誌',
        CONFIRMREPORT: '確認刪除報告',
        SELECTREPORT: '是否要刪除所選報表日誌?',
        PLEASEREPORT: '請選擇報表日誌',
        CONFIRMSESSION: '確認刪除連線日誌',
        SELECTSESSION: '是否要刪除選定的連線日誌?',
        PLEASESESSION: '請選擇連線日誌',
        CONFIRMACK: '確認告警?',
        SELECTACK: '是否要確認所有選定的告警?',
        DOACK: '是否要確認當前告警: ',
        CONFIRMSAVELOCA: '確認保存位置',
        DOSAVELOCA: '是否要保存位置?',
        CONFIRMRESETLOCA: '確認重置位置嗎?',
        DORESETLOCA: '是否要重置位置嗎?',
        SAVESUCC: '保存成功!',
        SAVEFAIL: '保存失敗!',
        RESETSUCC: "重置成功！",
        RESETFAIL: "重置失敗！",
        ACTIVESUCC: '啟用設備成功!',
        ACTIVEFAIL: '啟用設備失敗!',
        BANDEVSUCC: '禁止設備成功!',
        BANDEVFAIL: '禁止設備失敗!',
        WAITOTHER: '等待另一方加入連線...',
        ACSTROUBL: 'ACS加入troubleshooting連線',
        WAITCPE: '等待CPE進入troubleshooting連線',
        ACSCONNECT: 'ACS正在連接troubleshooting連線',
        ACSSETTING: '等待ACS設置troubleshooting連線',
        CPEJOIN: 'CPE加入troubleshooting連線',
        SESSESTABLISH: '連線已創建',
        CONFIRMTROUB: '確認斷開troubleshooting',
        DODISCONNECT: '確認斷開連線 ',
        CONFIRMUSER: '確認刪除用戶?',
        DOUSER: '確認刪除該用戶',
        SUCCESS: '成功!',
        ERROR: '錯誤!',
        CONFLOGENTRY: '確認刪除日誌條目',
        UPLOAD_CONNECTIVITY: '上傳連通性測試,請稍候...',
        DOWNLOAD_CONNECTIVITYTEST: '下載連通性測試,請稍候...',
        COMPLETE_CONNECTIVITYTEST: '連通性測試完成!',
        CONNECTIVITYTEST_URL: '測試文件網址',
        UPLOAD_SPEEDTEST: '上傳速度測試中，請稍候...',
        DOWNLOAD_SPEEDTEST: '下載速度測試中，請稍候...',
        COMPLETE_SPEEDTEST: '速度測試完成！',
        SPEEDTEST_URL: '測試檔案 URL',
        REQUESTUPDATEFAIL: '請求更新失敗',
        CONFVERSION_NOTCHANGED: "配置版本未更改。",
      },
      OPERATION_ACTION: {
        SELECT_OPERATION: '選擇設定檔',
        SELECT_WORKFLOW: '選擇工作流程',
        SELECT_Action_OR_WORKFLOW: "選擇設定檔或工作流程",
        ADD_OPERATION: '新增到設定檔',
        OPERATION_COUNT: '操作數',
        EDIT_OPERATION: '編輯操作',
        EDIT_CONFIGURATION: '編輯配置',
        OPERATION_DETAILS: '操作詳情',
        SETUP_OPERATION: '設定配置文件詳情',
        ADD_OPERATION_TO: '將操作新增至設定檔',
        OPERATION_ACTIONS: '操作設定檔',
        ENTER_OPERATION_ACTIONS: '輸入操作設定檔',
        OPERATION_TYPE: '操作類型',
        SELECT_OPERATIONTYPE: '選擇操作類型',
        EDIT_ACTIONS: '編輯操作設定檔',
        MANUAL_OPERATION: '手動設定檔',
        MANUAL_WORKFLOW: '手動工作流程',
        COMPLETION_RATE: '完成率'
      },
      AMP: 'AMP',
      GO: 'Go',
      COLLAPSE: '收合',
      COLLAPSEALL: '收合所有',
      EXPAND: '展開',
      EXPANDALL: '展開所有',
      DISCOVER: '發現',
      DISCOVERDATAMODEL: '發現：請求設備更新所選參數',
      PATH: '路徑',
      REQUIRED: '必須',
      OPTIONALPARAM: '沒有可選的參數',
      SENDRESP: 'Send Resp',
      ACCESSLIST: '訪問列表',
      SELECTACCESSLIST: '選擇訪問列表',
      REPORTEDVALUE: '值',
      INPUTLIST: '輸入列表',
      ADDEDITTAG: '新增/編輯標簽',
      ADDTAGS: '新增標簽',
      ADDTAG: '添加標簽',
      INPUTTAGNAME: '輸入標簽名',
      TAGSLIST: '標簽列表',
      EXISTTAG: '該標簽已存在!',
      ADDEDITLABEL: '新增/編輯標牌',
      INPUTLABELNAME: '輸入標牌名',
      LOCATION: '地圖',
      HEALTHYSTATUS: '在線率',
      INFORMHISTORY: 'Inform 歷史狀態',
      SESSIONLOGINTERVAL: '會話日誌間隔',
      SESSIONLOGINTERVALDESCRIPTION: "過去24小時內設備向AMP的定期報告間隔的歷史圖表。",
      TIMEINTERVAL_BETWEEN_CONSECUTIVE_INFORMS: "連續通知之間的時間間隔",
      LOCATIONDESCRIPTION: "在地圖上顯示設備的位置。",
      ONLINERATE: '在線率',
      ONLINERATEDESCRIPTION: '五角形雷達圖顯示設備的健康狀態。',
      RESET: '重置',
      EDITCOMPONENT: '新增組件',
      BULKLOGLIST: '主题日誌列表',
      EDITFILE: '編輯檔案',
      FILTERNODE: '過濾節點',
      SELECTACTION: '操作選定的設備',
      PRODUCTINFO: '產品信息',
      CONNHISTORY: '歷史連接',
      LAST24: '(最近24小時)',
      WIFIUSAGE: 'WiFi Channel使用情況',
      WIFIANALYZER: 'WiFi分析',
      WIFIANALYZERDESCRIPTION: '此設備上可用頻道/頻段的鄰近AP的WiFi RSSI圖表。',
      CELLSTATUS: '小型基站狀態',
      CELLSTATUSDESCRIPTION: "小型基站的狀態，例如無線服務狀態、PC、gNB ID、MCC、MNC和TAC。",
      CELLHANDOVERSTATUS: '小型基站切換狀態',
      CELLHANDOVERSTATUSDESCRIPTION: '小型基站切換狀態。',
      COVERMAP: '覆蓋地圖',
      COVERMAPDESCRIPTION: '顯示組內設備的位置和無線覆蓋範圍。',
      TOTALALARMSDESCRIPTION: "設備報告的警報總數。",
      ALARMSDESCRIPTION: "具有不同嚴重性的警報總數，例如關鍵、主要、次要和警告。",
      CREATEMAP: '創建地圖',
      SAVEMAP: '保存地圖',
      SELECTIMAGE: '選擇圖像',
      IMAGEPRE: '圖像預覽',
      MAPX: '地圖X轴',
      MAPY: '地圖Y轴',
      EDITMAP: '編輯地圖',
      M: '米',
      ENGMODE: '工程模式',
      CLIMODE: 'CLI模式',
      CONFIG: '配置',
      PROVISIONING: 'Provisioning',
      DEVICEALARM24HRS: '24小時內的警報',
      PMALARM24HRS: '在過去24小時內檢測到PM數據中的異常。',
      PMSTATUS_GOOD: '過去24小時內沒有異常的PM數據。',
      PMSTATUS_BAD: '過去24小時內的PM數據存在異常。',
      PMSTATUS_NORMAL: '過去24小時內沒有可用的PM數據。',
      PMSTATUS_NOPERMISSION: '無法存取 PM 資料。',
      ADDTOGROUP: '加入群組',
      ADDDEVTOGROUP: '添加設備到群組',
      SELECTDEVICE: '選定設備',
      RESTARTNOW: '現在重啟以使更改生效。',
      TAGSDESCRIPTION: '設備上的自定義Tag，以便更輕鬆地管理設備。',
      BATCHSAVE: '批量保存',
      CPUUSAGEDESCRIPTION: "設備的CPU使用百分比。",
      MEMORYUSAGEDESCRIPTION: "設備的記憶體使用百分比。",
      CPUUSAGECHART_DESCRIPTION: "設備的CPU使用百分比的歷史圖表。",
      MEMORYUSAGECHART_DESCRIPTION: "設備的內存使用情況的歷史圖表。",
      KPIDESCRIPTION: "小型基站的KPI歷史圖表，如RRC、UE上下文和吞吐量。",
      PMPARAMDESCRIPTION: "小型基站的KPI，如RRC、UE上下文和吞吐量。",
      ALARM_CURRENTNUMBERS: '設備報告的警報總數。',
      PRODUCTMODEL: '產品型號',
      PRODUCTMODELDESCRIPTION: '指示當前設備屬於哪個產品。',
      DEVICEOFFLINETIPS: '設備目前離線，AMP保留設備最後報告的信息。',
      DEVICENOTREGISTERTIPS: '該設備是新添加的設備，從未在線/供應過。',
      ONLINESTATUS: '在線狀態',
      ONLINESTATUSDESCRIPTION: "設備在線狀態的歷史圖表。",
      REGISTERSINGLEDEVICE: "注册單個設備",
      REGISTERBATCHDEVICE: "注册批次處理設備",
      DOWNLOADSESSIONGCSV: '下載 CSV (所有字段)',
      DOWNLOADSESSIONGJSON: '下載 JSON (完整內容)',
      DOWNLOADLATESTSESSIONGJSON: '下載最新會話日誌',
      TOTALCLIENTHISTORY: "已建立連接的WiFi客戶端",
      TOTALCLIENTHISTORY_DESCRIPTION: "此設備上已建立連接的客戶端數量的歷史圖表。",
      CLIENTSPERSSID: '每個 SSID 的 WiFi 客戶',
      CLIENTSPERSSID_DESCRIPTION: '此設備上按 SSID 分類的 WiFi 客戶連接數量概述的歷史圖表。',
      CLIENTSPERRADIO: '每個無線電的 WiFi 客戶',
      CLIENTSPERRADIO_DESCRIPTION: "此設備上按無線電/頻段分類的 WiFi 客戶端連接數量的歷史圖表。",
      TRAFFICPERSSID: '每個 SSID 的 WiFi 流量',
      TRAFFICPERSSID_DESCRIPTION: '此設備上按 SSID 分類的 WiFi 流量概述的歷史圖表。',
      TRAFFICPERRADIO: '每個無線電的 WiFi 流量',
      TRAFFICPERRADIO_DESCRIPTION: '此設備上按無線電/頻段分類的 WiFi 流量概述的歷史圖表。',
      VIEWCHART: '查看圖表',
      LOGDETAIL: '日誌詳細資訊',
      TXPOWER: '發射功率',
      BEACONPERIOD: '信標週期',
      SSIDSWITHCLIENTS: 'WiFi SSID分佈。',
      SSIDSWITHCLIENTS_DESCRIPTION: '此設備上與 WiFi 客戶端相關的 SSID 分佈網絡。',
      RSSIDISTRIBUTION: 'WiFi RSSI 分佈',
      RSSIDISTRIBUTION_DESCRIPTION: '此設備上與 WiFi 客戶端相關的 RSSI 分佈範圍。',
      WIFIRADIOTHROUGHPUT: 'WiFi 無線電數據使用情況',
      WIFIRADIOTHROUGHPUT_DESCRIPTION: '所有可用 WiFi 頻段/介面的當前無線電協議和數據使用情況。',
      OPERATINGCHANNELBANDWIDTH: '頻寬',
      BYTESSENT: '已發送',
      BYTESRECEIVED: '已接收',
      PACKETSSENT: '已發送封包',
      PACKETSRECEIVED: '已接收封包',
      ERRORSSENT: '發送錯誤封包',
      ERRORSRECEIVED: '接收錯誤封包',
      SSIDLIST: 'WiFi SSID 清單',
      SSIDLIST_DESCRIPTION: "關於此裝置預設SSID的當前WiFi狀態，包括啟用、WMM、頻寬和數據使用情況。",
      MAXCLIENTS: '最大客戶端數量',
      WMM: 'WMM',
      UTILIZATION_GREEN: '綠色',
      UTILIZATION_YELLOW: '黄色',
      UTILIZATION_RED: '紅色',
      UTILIZATION_GREEN_DESCRIPTION: '利用率 <= 30%',
      UTILIZATION_YELLOW_DESCRIPTION: '30% < 利用率 <= 60%',
      UTILIZATION_RED_DESCRIPTION: '利用率 > 60%',
      MCS: 'MCS',
      STATUS: '狀態',
      INACTIVE: '停用',
      DISABLE: '停用',
      WHITELISTED: '白名單',
      REFURBISHMENT: '整修',
      NETWORKTOPOLOGY: "網路拓撲",
      NETWORKTOPOLOGY_DESCRIPTION: "以樹狀視圖顯示所有相關節點資訊。",
      core_5g: {
        ue_info: 'UE資訊',
        ue_info_DESCRIPTION: 'UE資訊提供了連接到5G核心網路的使用者設備（UE）的詳細資訊。',
      },
      healthyStatus: {
        alarm_description: '在24小時內每發生一次嚴重警報將扣10分，而每發生一次重大警報將扣5分，以此類推。',
        session_log_rate_description: '設備正常運行周期與過去24小時內提交給AMP的報告之比。',
        online_rate_description: '設備在過去24小時內的在線率，表示設備運行並連接的時間百分比。',
        service_quality_description: "WiFi設備會對每個當前連接的差客戶端扣除5分，而小基站通過測量24小時平均RRC速率來評估服務質量。",
        service_active_description: '在過去24小時內每次設備Reboot(Bootstrap)，將扣20分。'
      },
      cableMedemInfo: {
        cable_medem_info: 'DOCSIS 狀態',
        cable_medem_info_description: '顯示有​​線接取網路的電纜數據機訊息'
      },
      ONTMediaInfo: {
        ont_media_info: 'ONT媒體狀態',
        ont_media_info_description: '顯示ONT設備的媒體資訊'
      },
      batteryStatus: {
        battery_information: '電池狀態',
        battery_description: '電池信息，如狀態、溫度和電量'
      },
      WIFIAPINFO: 'WiFi AP 資訊',
      WIFIAPINFO_DESCRIPTION: '',
      WIFIAPINFO_APCONFIGVERSION: 'AP 配置',
      APCONFIG: 'AP 配置',
      APNCONFIG: 'APN 配置',
      CONFIGVER: "設定版本",
      WIFIAPINFO_APNAME: 'AP 名稱',
      WIFIAPINFO_APROLE: 'AP 角色',
      WIFIAPINFO_APNCONFIGVERSION: 'APN 配置',
      EDITAPNAME: '編輯 AP 名稱',
      WIFIAPINFODESCRIPTION: 'WiFi AP 資訊，包括 AP 角色、AP 名稱、AP 配置版本和 APN 配置版本。',
      FIVEGLICENSE: '單元軟體許可證',
      FIVEGLICENSEDESCRIPTION: '"小型基站的許可證信息，如到期時間和支持的項目。',
      OPERATION_LOCATION: '設定檔位置',
      OPERATION_SETUPLOCATION: '設定檔設置位置',
      SEARCH_KEYWORD: "搜索關鍵字",
      SERVICE_STATUS: "服務狀態",
      NCI: 'NCI',
      GNB_ID: 'GNB ID',
      GNB_ID_LENGTH: 'GNB ID Length',
      MCC: 'MCC',
      MNC: 'MNC',
      AMF_IP_CONTROL_PLANE: 'AMF IP-Control-Plane',
      CELL_ID: 'Cell ID',
      TAC: 'TAC',
      NSSAI: 'NSSAI',
      PCI: 'PCI',
      PARAMETER: '參數',
      REPORTINTERVAL: '報告間隔',
      TIMEREFERENCE: '時間參攷',
      NUMBEROFRETAINEDFAILEDREPROTS: '保留失敗報告的數量',
      ENCODINGTYPE: "編碼類型",
      REQUESTURIPARAMETER: "請求URI參數",
      NOTIFTYPE: '通知類型',
      REFERENCELIST: '參攷清單',
      RECIPIENT: '容器',
      COMPLETED_DESCRIPTION: "裝置完成了AMP分配的操作。",
      FAIL_DESCRIPTION: "裝置執行的所有操作都失敗了。",
      PARTIAL_DESCRIPTION: "裝置執行的部分操作失敗了。",
      CANCEL_DESCRIPTION: "AMP分配的操作在執行前被取消。",
      PENDING_DESCRIPTION: "等待裝置接收AMP分配的操作。",
      INPROCESS_DESCRIPTION: "等待裝置向AMP報告執行操作的結果。",
      LOADMAPFAIL: '地圖瓦片載入失敗，請稍後重試。',
      MISMATCHPROFILE: '參數 "conGlobalProfile" 和 "conDeviceSpecificProfile" 與配置不匹配。',
      ENERGYSAVING: '能源管理',
      STARTTIME_MUST_BE_EARLIER: "開始時間必須早於結束時間",
      STARTDATE_MUST_BE_EARLIER: "開始日期不能晚於結束日期。",
    },
    GROUPS: {
      WIFICLIENT: 'WiFi客戶端',
      TITLE: '設備群組',
      TOTAL: '群組總數',
      LIST: '設備群組列表',
      LISTDESCRIPTION: 'AMP 中包含裝置的獨立單元，負責批次操作、網絡狀態監控等特定功能。',
      ADDGROUP: '新增群組',
      EDITGROUP: '編輯群組',
      NAME: '群組名',
      ACCEPT: '接受',
      MEMBERS: '設備',
      MEMBERSTITLE: '成員',
      PENDINGLOGS: '待處理日誌列表',
      PENDINGLOGSDESCRIPTION: '等待裝置接收的待處理操作任務列表。',
      OPERATIONLOGS: '操作日誌列表',
      OPERATIONLOGSDESCRIPTION: '由 AMP 分配給群組內裝置的操作任務列表及回報的結果。',
      IMPORTTYPE: '選擇導入類型',
      SELECTGROUP: '選擇設備群組',
      CREATE: '創建',
      ADDDEVICE: '添加設備',
      INPUTTYPE: '選擇輸入的類型',
      INPUTVALUE: '輸入值',
      GROUPUPDATE: '群組更新',
      FILTERING: '清除非法設備',
      ADDTOGROUP: '添加到群組',
      SELECTGROUPTYPE: '選擇群組類型',
      SEARCH: '搜索',
      PENDINGOPER: '待辦操作',
      OPERLOG: '操作日誌',
      PARAMNOTIFICATION: '通知參數',
      SELECTPARAMNOTIFICATION: '選擇通知參數',
      NONEXISTINGMEN: '自動過濾掉不存在的設備',
      BYINPUT: '通過輸入',
      BYIMPORTFILE: '通過導入文件',
      ADDMEMBER: '添加成員',
      FILTERMEMBERIN: '產品中的過濾條件',
      ENTERKEYWORDS: '輸入關鍵字',
      BYPRODUCT: '通過產品型號',
      CONFIRM: '確認',
      DEVICECOUNT: '設備',
      ONLINERATE: '在線率',
      SERVICEAVAILABILITY: '服務可用性',
      CREATEBY: '創建者',
      CREATETIME: '創建時間',
      ALARMCOUNT: '告警數量',
      ALARMCOUNTDESCRIPTION: '不同嚴重程度（如嚴重、主要、次要和警告）的警報總數。',
      DRAGANDDROP: '拖放所選操作以安排執行操作的順序',
      GROUPACT: '群組動作',
      ADDMEMBY: '通過導入新增成員',
      ADDMEMINPUT: '通過輸入新增成員',
      NETWORKTOPOLOGY: '網絡拓撲結構 ',
      CANTDOOPERATION: '當前組不存在設備',
      ASSIGNOPERATION: '分配操作',
      REASSIGNOPERATION: '重新分配操作',
      DIFFRENTPROTOCOLNOTALLOWED: '不允許將2個以上不同的協議設備加至1個组',
      CONFIRMADDTOGROUPMEMBER: '確認添加到組',
      ONLYVALID: '將只添加合法設備',
      DEVICEERRCODE1: '該產品不包含此設備',
      DEVICEERRCODE2: '該設備已存在於組中',
      DEVICEERRCODE3: '該設備是非法的',
      UE: 'UE',
      UEDESCRIPTION: '此群組內附屬於小型基站的 UE 總數。',
      ONLINEDEVICESTITLE: '線上裝置',
      ONLINEDEVICESDESCRIPTION: '此群組內的在線裝置總數。',
      WIFICLIENTDESCRIPTION: '此群組內的 WiFi 用戶總數。',
      ALARMSDESCRIPTION: '此群組內裝置報告的警報總數。',
      KPIDESCRIPTION: '此群組內小型基站 KPI（例如 RRC、UE Context 和 Throughput）的歷史圖表。',
      DEVICESLISTDESCRIPTION: '此群組內的所有裝置列表。',
      GROUPSDELETED: '所選組已成功刪除。',
      RFMAP_MAX_WARNING: '覆蓋圖僅支持最多50台設備。',
      SSIDLIST: 'WiFi SSID 列表',
      SSIDLIST_DESCRIPTION: '此群組內的所有 SSID 列表。',
      SSID: 'SSID',
      SECURITY: '安全性',
      CAPTIVEPORTAL: 'Captive Portal',
      MACACL: 'MAC ACL',
      ATF: 'ATF 啟用',
      ATFPERCENTAGE: 'ATF',
      BEAMFORMING: '波束成形',
      MAXCLIENTS: '最大客戶端數',
      WMM: 'WMM',
      BAND: '頻段',
      BANDWIDTHCONTROL: '頻寬控制',
      DOWNLOAD: '發送',
      UPLOAD: '接收',
      FOREIGNAPSLIST: '外部 AP 列表',
      FOREIGNAPSLIST_DESCRIPTION: '不包含在此群組中的 WiFi AP 列表。',
      VLAN: 'VLAN',
      BSSID: 'BSSID',
      CHANNEL: '頻道',
      RSSI: 'RSSI',
      NEARBYAPS: '附近的 AP',
      TIME: '時間',
      SSIDSWITHCLIENTS: '擁有最多客戶端的前 6 名 WiFi SSID',
      SSIDSWITHCLIENTS_DESCRIPTION: '此群組中擁有最多客戶端連線的前 6 名 WiFi SSID。',
      APSWITHCLIENTS: '擁有最多客戶端的前 6 名 WiFi AP',
      APSWITHCLIENTS_DESCRIPTION: '按客戶端數量排列的前 6 名最受歡迎的 WiFi AP。',
      APSWITHTRAFFIC: '流量最高的前 6 名 WiFi AP',
      APSWITHTRAFFIC_DESCRIPTION: '發送和接收數據最多的六個 WiFi AP 的總數值。',
      CLIENTSPERSSID: "客戶端連接的WiFi SSID",
      CLIENTSPERSSID_DESCRIPTION: "針對包含在群組中的設備，依客戶端連接數量按WiFi SSID分類的歷史圖表。",
      CLIENTSPERRADIO: "WiFi 客戶端連接的頻寬",
      CLIENTSPERRADIO_DESCRIPTION: "針對包含在群組中的設備，依 WiFi 客戶端連接數量按頻寬分類的歷史圖表。",
      TRAFFICPERSSID: "WiFi SSID的流量",
      TRAFFICPERSSID_DESCRIPTION: "針對包含在群組中的設備，依流量按WiFi SSID分類的歷史圖表。",
      TRAFFICPERRADIO: "WiFi 頻寬的流量",
      TRAFFICPERRADIO_DESCRIPTION: "針對包含在群組中的設備，依流量按無線電頻率分類的歷史圖表。",
      BYTESRECEIVED: '接收',
      BYTESSENT: '發送',
      ENABLE: '啟用',
      DISABLE: '禁用',
      TAGSGROUPDESCRIPTION: '針對群組的自訂標籤，方便裝置管理。',
      COUNTRY: '國家',
      SELECTCOUNTRY: '選擇國家',
      STREETADDRESS: '街道地址',
      CITY: '城市',
      STATE: '州',
      ZIPCODE: '郵遞區號',
      TAGS: '標籤',
      INPUTTAGNAME: '輸入標簽名',
      ROAD: '道路/街道',
      HOUSE: '建築號碼',
      GROUPDETAILS: '群組詳情',
      GROUPLOCATION: '群組位置',
      GROUPLOCATIONDESCRIPTION: '在地圖上顯示群組位置。',
      EDITLOCATION: '編輯位置',
      INPUTLAT: '輸入緯度',
      INPUTLNG: '輸入經度',
      RESET: '重置',
      LATLNG: '緯度和經度',
      LAT: '緯度',
      LNG: '經度',
      LOCATION: '位置',
      VIEWLOCATION: '檢視群組位置',
      VIEWCOVERAGEMAP: '檢視覆蓋範圍地圖',
      VIEWLOCATIONDESCRIB: '當群組位置小工具開啟時，點擊欄中的圖示允許用戶檢視該群組的位置和資訊。',
      VIEWCOVERAGEMAPDESCRIB: '當覆蓋範圍地圖小工具開啟時，點擊欄中的圖示允許用戶檢視群組中包含的設備的分佈和資訊。',
      WIDGETNAME: '小工具名稱、類別或子類別',
      COVERMAP: '覆蓋地圖',
      COVERMAPDESCRIPTION: '顯示群組內所有裝置的位置和無線覆蓋範圍。',
      TOTALALARMSDESCRIPTION: '此群組內裝置報告的警報總數。',
      ALARMMGMT: '警報管理',
      ALARMMGMTDESCRIPTION: '列出此群組內裝置報告的所有警報，包括已清除、未清除的警報，以及其嚴重程度、事件時間和可能原因。',
      NETWORK: '網絡'
    },
    PRODUCTS: {
      LIST: '型號列表',
      LISTDESCRIPTION: '包含設備的單位，具有特定功能，如訪問列表控制、默認配置參數和用戶帳戶的管理範圍。設備註冊和上線時必須提供此單位。',
      NETWORKRADIOACCESSLIST: '無線接入網路列表',
      NETWORKRADIOACCESSLISTDESCRIPTION: '用於管理特定設備（如小型基站）的單位。出於網絡管理目的，還會創建同名的群組。',
      NETWORKWIFIAPLIST: 'WiFi AP 網路列表',
      NETWORKWIFIAPLISTDESCRIPTION: '用於管理特定設備（如 WiFi AP）的單位。出於網絡管理目的，還會創建同名的群組。',
      NETWORKWIFIMESHLIST: 'WiFi Mesh 網路列表',
      NETWORKWIFIMESHLISTDESCRIPTION: '用於管理特定設備（如 WiFi Mesh AP）的單位。出於網絡管理目的，還會創建同名的群組。',
      ACTION: '型號動作',
      ADD: '新增型號',
      ADDRADIOACCESSNETWORK: '新增無線接入網路',
      ADDWIFIAPNETWORK: '新增 WiFi AP 網路',
      ADDWIFIMESHNETWORK: '新增 WiFi Mesh 網路',
      PERMISSION: '許可',
      DEVICEPERMISSION: '許可的設備',
      ADDPERMISSION: '允許清單',
      PERMITTEDTYPE: '存取控制',
      PROVISIONINGTYPE: 'Provisioning類型 ',
      SELECTTYPE: '選擇存取控制',
      ALLOWALL: '允許所有設備',
      WHITELIST: '允許清單',
      CREATEWHITELIST: '創建允許清單',
      LABEL: '標牌名稱',
      LABELREQUIRED: '標牌名稱必填!',
      NAMEREQUIRED: '名稱必填!',
      PRODUCTTYPE: '型號類型',
      SELECTPRODUCTTYPE: '選擇型號類型',
      PRODUCTPICTURE: '型號圖片',
      PRODUCTNAME: '型號名',
      ISPRODUCTNAME: '型號名必填!',
      ISPRODUCTCLASS: '型號必填!',
      TARGETPRODUCT: '目標型號名',
      OBJECTNAME: '目標名',
      ENTEROBJECTNAME: '輸入目標名',
      PRODUCTDETAILS: '型號詳情',
      DETAILS: '詳細資料',
      CHOOSEFILE: '選擇檔案',
      PERMITEDDEVICE: '允許接入設備',
      DEVICELIMITS: '設備數上限',
      UPDATEDTIME: '更新時間',
      EDITPRODUCT: '編輯型號',
      EDITRAN: '編輯無線接入網絡',
      EDITAPN: '編輯 WiFi AP 網絡',
      EDITMESH: '編輯 WiFi Mesh 網絡',
      PRODUCTMODEL: '產品模型',
      PRODUCTMODELDESCRIPTION: '產品模型的圖片和說明',
      BYDEFAULT: '選擇圖片',
      BYUPLOAD: '上傳圖片',
      DATACOLLECT: '數據收集',
      DATACOLLECTDESCRIPTION: "切換開啟以開始收集數據,相關設備的KPI圖表Widget可在設備資訊頁面中建立。",
      DATAEXPORTDESCRIPTION: "開啟此選項以將性能指標導出到第三方遙測系統。",
      PRODUCTNOTEXIST: '當前產品不存在',
      DEVICETEXIST: '設備已存在',
      DEVICEERRCODE1: '項目包含非法字串',
      DEVICEERRCODE2: 'DelItem不存在',
      DEVICEERRCODE3: "該設備已存在於產品：",
      LABELORPATHEXISTS: "標籤名稱或參數路徑已存在",
      PARSINGFAILED: "解析檔案失敗",
      FILETYPEERROR: "只能上傳CSV檔案",
      EXPORTINITDEFAULTTOJSON: "下載JSON (配寘預設值）",
      EXPORTINITDEFAULTTOCSV: "下載CSV (批量注册示例）",
      EDITPROVISIONINGDEFAULTVALUE: "編輯設定預設值 ",
      TAGS: '標籤',
      LOCATION: '位置',
      REGISTERDEVICE: '註冊設備',
      NAME: '名稱',
      NAMETOOLTIP: '輸入用戶定義的產品名稱',
      NETWORKNAME: '輸入用戶定義的網絡名稱',
      PICTURE: '圖片',
      UEFORPRODUCT: '各產品的 UE 數量',
      OUI: '輸入正確的OUI以符合設備要求',
      DESCRIPTION: '描述',
      NETWORKDESCRIPTION: '輸入網絡中設備的特點或專有屬性',
      PRODUCTDESCRIPTION: '輸入產品中設備的特點或專有屬性',
      PRODUCTCLASS: '輸入符合設備要求的產品類別',
      PROCPELIMIT: '輸入產品的容量',
      NETCPELIMIT: '輸入網絡的容量',
      OUIDESCRIPTION: 'OUI必須正確以便AMP驗證訪問的設備',
      ALLOWALLDESCRIPTION: '設備的OUI、產品類別需被驗證',
      PROALLOWLISTDESCRIPTION: '設備的OUI、產品類別和序列號需被驗證，創建產品後需通過序列號註冊設備',
      NETALLOWLISTDESCRIPTION: '設備的OUI、產品類別和序列號需被驗證，創建網絡後需通過序列號註冊設備',
      PRODUCTCLASSDESCRIPTION: '產品類別必須正確，以便AMP驗證訪問的設備。例如：Femtocell_5G_SA、EAP等',
      PRODEVICELIMITDESCRIPTION: '產品中設備的數量容量',
      NETDEVICELIMITDESCRIPTION: '網絡中設備的數量容量',
      PROVISIONINGTYPEDESCRIPTION: '配置類型必須正確，以便AMP驗證訪問的設備'
    },
    ALARMS: {
      TOTAL: '告警總數',
      ALARMCOUNT: '嚴重/主要/次要/警告 數量',
      CRITICAL: '嚴重',
      MAJOR: '主要',
      WARNING: '警告',
      INDETERMINATE: '不確定',
      MINOR: '次要',
      CURRENTNUMBERS: '目前警告數量',
      SYSTEM: '系統事件',
      LIST: '系統事件列表',
      DEVICE: '設備警告',
      DEVICELIST: '設備警告列表',
      ACKALARM: '確認警告',
      ERRORLIST: '設備錯誤列表',
      DEVICEEVENTTRACKING: '設備事件追蹤',
      DEVICEEVENTTRACKINGDESCRIPTION: '列出由 AMP 跟蹤的與設備相關的重大事件，如重啟、離線和重置。',
      NOTIFICATION: '通知',
      NOTIFICATIONLIST: '通知列表',
      NOTIFICATIONLISTDESCRIPTION: '用戶創建的規則列表，用於在特定條件下通過 SMTP 或 SNMP Trap 接收通知，基於需求。',
      FORK: '分叉',
      FORKNOTIFICATION: '分叉通知',
      CLONE: '複製',
      CLONENOTIFICATION: '複製通知',
      EDITNOTIFICATION: '編輯通知',
      SETUPDETAILS: '詳情',
      ENTERDETAILS: '輸入詳情訊息',
      GENERIC_TRAP_TYPE: "通用陷阱類型",
      SPECIFIC_TRAP_OID: "特定陷阱OID",
      VARIABLE_BINDINGS: '變數綁定（選擇性）',
      ALIASVALUE: '別名值',
      OlDNAME: '舊名稱/名稱',
      VARIABLE_BINDINGS_DESCRIPTION: 'Varbinds的值與階段中配置的別名清單相關聯。',
      TARGET: '目標',
      EDITTARGET: '編輯目標',
      SELECTTARGET: '選擇目標',
      ENTERTARGET: '輸入目標',
      TARGETREQUIRED: '必須輸入目標檔案名!',
      TARGETTYPE: '目標類型',
      TARGETDEVICESN: '目標設備序號',
      ISTARGETDEVICESN: '目標設備序號必填!',
      SCHEDULE: '時程',
      EDITSCHEDULE: '編輯時程',
      ENTERSCHEDULE: '輸入時程',
      SCHEDULEDOWNLOAD: '時程下載詳情',
      STARTDATE: '開始日期',
      ENDDATE: '結束日期',
      STARTTIME: '開始時間',
      ENDTIME: '結束時間',
      ENTEROPERATION: '輸入操作名',
      TRIGGER: '觸發類型',
      SELECTTRIGGER: '選擇觸發類型',
      INFORMPARAM: '告知參數',
      CONDITION: '執行條件',
      SELECTCONDITION: '選擇條件',
      BUILDCONDITION: '編譯條件',
      PARAMCONDITION: '參數條件',
      SELECTPARAMCONDITION: '選擇參數條件',
      ATTACHED: '附加訊息',
      ADDITIONALPARAMINFO: '附加參數信息',
      ADDITIONALPARAMINFO_DESCRIPTION: '通知消息中包含特定參數值的更詳細信息。',
      NODE: '節點',
      ENTERNODE: '輸入節點',
      SELECTNODE: '選擇節點',
      VIEWNODE: 'NetConf節點視圖',
      REFERNODE: '參照節點',
      REFERNODEREQUIRED: '參照節點必填!',
      PARENTNODE: '上層節點',
      SELECTPARENTNODE: '選擇上層節點',
      CHILDNODE: '子節點',
      ADDCHILDNODE: '新增子節點',
      SELECTCHILDNODE: '選擇子節點',
      CHILDCONTENT: '子節點内容',
      CONTENT: '内容',
      ENTERCONTENT: '輸入内容',
      CONFIG: '配置',
      NAMESPACE: '命名空間',
      ENTERNAMESPACE: '輸入命名空間',
      ALIAS: '別名',
      ENTERALIAS: '輸入別名',
      ADDATTACHED: '新增附加訊息',
      ADDDEVICEFALUT: '新增設備故障參數',
      SELECTDEVICEFAULTNAME: '選擇設備故障名稱',
      BUILD: '建立操作(可選)',
      BUILDREQUIRED: '操作',
      PROGRESS: '前進',
      ACTIONS: '動作',
      REPEAT: '重演',
      REPEATTYPE: '重複類型',
      UPLOADNOTIFI: '上傳告警通知',
      DROPFILE: '在這裏放置文件或點擊',
      UPLOADFORMATSARESUPPORTED: '僅支援 .tar .tar.gz .tgz .zip .gzip 格式上傳。',
      UPLOADALL: '上傳所有',
      UPLOAURL: '上傳URL',
      BANDWIDTH: '上傳頻寬',
      QUEUEPROGRESS: '隊列進展',
      PARAMLIST: '參數列表',
      SELECT: '選擇',
      ENTERSELECT: '輸入選擇(XPath expression)',
      SOURCE: '來源',
      SELECTSOURCE: '選擇源',
      FILTERSTATE: '過濾狀態',
      SELECTFILTERSTATE: '選擇過濾狀態',
      FILTERTYPE: '過濾類型',
      SELECTFILTERTYPE: '選擇過濾類型',
      REMOVEALL: '移除所有',
      REMOVE: '移除',
      UPDATEUSER: '更新用戶',
      UPDATEDBY: '更新者',
      LASTACTIVE: '最後活動',
      UPDATELOG: '更新日誌',
      NOTIF: '通知',
      ONLYONCE: '僅一次',
      ALLSTATE: '所有狀態',
      ALLSEVERITY: '所有嚴重程度',
      ALLGROUPS: '所有群組',
      SEVERITY: '嚴重性',
      STATE: '狀態',
      CLEAREDTIME: '清除時間',
      CLEARED: '已清除',
      NOTCLEARED: '未清除',
      CLEAREDALARM: '已清除警報',
      UNCLEAREDALARM: '未清除警報',
      CHANGEDALARM: '已變更警報',
      NEWALARM: '新警报',
      NOTIFICATIONTYPE: '通知類型',
      PROBABLECAUSE: '可能原因',
      SPECIFICPROBLEM: '具體問題',
      ADDITIONALTEXT: '附加文字',
      ADDITIONALINFORMATION: '附加資訊',
      ALARMID: '報警ID',
      EVENTTYPE: '事件類型',
      EVENTTIME: '活動時間',
      ACKUSER: '確認用戶',
      ACKTIME: '確認時間',
      ERRORCODE: '錯誤',
      DEVICEFAULTPARAM: '設備故障參數',
      BYTAG: '按標籤 ',
      RECEIVERLIST: '接收者列表',
      RECEIVERCCLIST: '抄送者列表',
      RECEIVETAGSTOOLTIP: '添加帶有特定標籤的接收者',
      RECEIVERCCTAGSTOOLTIP: '添加帶有特定標籤的抄送者',
      EMAILSUBJECT: '電子郵件主題',
      EMAILCONTENT: '電子郵件內容',
      WITHACTIVEHOURS: '有活躍時間',
      PRIPHONENUM: '主電話號碼',
      SECPHONENUM: '輔助電話號碼',
      TEXTMESSAGE: '簡訊',
      ACK: '確認',
      EDIT_STAGE_NOTIFICATIONS: '使用階段/通知建立通知',
      TOTALALARMSDESCRIPTION: '系統報告的警報總數。',
      ALARMMGMT: '警報管理',
      ALARMMGMTDESCRIPTION: '列出系統報告的所有警報，包括已清除、未清除的警報，並顯示嚴重性、事件時間和可能原因。',
      ALARMCOUNTDESCRIPTION: '不同嚴重程度（如嚴重、主要、次要和警告）的警報總數。',
      TARGETDEVICETAG: '目標設備標籤',
    },
    PROVISIONING: {
      COLLAPSIBLE: '配置供應',
      WORKSFLOW: '流程規劃',
      WORKSFLOWLIST: '流程列表',
      WORKSFLOWLISTDESCRIPTION: '用戶創建的規則列表，用於在特定條件下對特定設備執行操作，基於需求。',
      CONFIGURATIONS: '配寘',
      CONFIGURATIONLIST: '配寘列表',
      CONFIGURATIONLISTDESCRIPTION: '自動或用戶創建的規則列表，用於在特定條件下對設備執行配置，以滿足需求。',
      POLICYLIST: '能源策略',
      POLICYLISTDESCRIPTION: '列出自動建立的節能規則。這些規則在啟用節能模式時對設備執行配置設定。',
      CLONEPOLICY: '克隆策略',
      POLICYS: '策略',
      FROMWORKSFLOW: '起始流程',
      FROM: '起始',
      VALIDFROM: '有效起始日期',
      CLONEWORKSFLOW: '複製流程',
      CLONECONFIGURATION: '复制配寘',
      EDITWORKSFLOW: '編輯流程',
      FORKWORKSFLOW: '分叉流程',
      UPLOADWORKSFLOW: '上傳流程',
      UPLOADQUEUE: '上傳隊列',
      OPERATIONS: '操作',
      PROFILES: '設定檔',
      ACTIONS: '行動',
      CLONEOPERATIONS: '克隆設定檔',
      FORKOPERATIONS: '分叉設定檔',
      EDITOPERATIONS: '編輯操作',
      OPERATIONLIST: '設定檔列表',
      OPERATIONLISTDESCRIPTION: '用戶創建的規則列表，用於在特定條件下對特定設備執行操作，基於需求。',
      DUOPERATION: '新增DU操作',
      PARAPATH: '參數路徑',
      ENTERPARAPATH: '輸入參數路徑',
      ISPARAPATH: '必須輸入參數路徑.',
      NEXTLEVEL: '跳過',
      PRODUCT: '產品型號',
      SCRIPTS: '腳本',
      SCRIPTLIST: '腳本列表',
      EDITSCRIPT: '編輯腳本',
      SCRIPTNAME: '腳本名',
      FILES: '檔案',
      FILELIST: '檔案列表',
      FILELISTDESCRIPTION: '用戶創建的列表，包含設備上傳或下載文件時所需的訊息，例如文件類型、URL 和身份驗證。',
      FILETYPE: '檔案類型',
      SELECTFILETYPE: '選擇檔案類型',
      ENTERFILETYPE: '輸入檔案類型',
      ISFILETYPE: '檔案類型必填!',
      ISURL: 'URL必填!',
      DELAYSECONDS: '延遲秒數',
      TARGETNAME: '目標檔案名稱',
      ENTERTARGETNAME: '輸入目標檔案名稱',
      FILESIZE: '檔案大小',
      DESCRIPTION: '詳情描述',
      SUBSCRIBE: '訂閲',
      SUBSCRIBETOPIC: '訂閲主題',
      SELECTTOPIC: '選擇訂閲主題',
      VENDORFILE: '供應商專用檔案',
      VENDORFILEDESCRIPTION: '用戶創建的列表，包含設備上傳或下載文件時所需的廠商特定訊息，例如文件類型、URL 和身份驗證。',
      ADDVENDORFILE: '新增供應商專用檔案',
      EDITVENDORFILE: '編輯供應商專用檔案',
      LATESTFIRMWARE: '最新的韌體',
      AVAILABLEFILES: '可用檔案',
      SETUPDETAILS: '設置詳情',
      CODEDISTRIBUTION: 'Provisioning Code分佈',
      OPERATENAME: '操作名',
      ENTEROPERATENAME: '輸入操作名',
      ADDINFORM: '添加Inform參數',
      PUBLICTOPIC: '發佈主題',
      SELECTPUBLICTOPIC: '選擇發佈主題',
      ISDATAMODEL: '數據模型必填!',
      CPELIMIT: 'CPE數目上限',
      ISCPELIMIT: 'CPE數目上限必填!',
      SUMMARYACTION: '匯總活動',
      ADDSUMMARYREPORT: '新增匯總報告',
      SUMMARYREPORT: '匯總報告',
      SUMMARYREPORTSETTING: '匯總報告設置',
      PLEASESELECTTEMPLATE: '請先選擇一個範本，然後再繼續',
      PLEASEFILLPARAMS: '請先填寫標籤名稱和參數路徑，然後再繼續',
      INFORMLIST: 'Inform參數列表',
      SELECTTRIGGERREQ: '選擇觸發 (必填)',
      DEVICEPARAMLIST: '設備參數列表',
      ADDSTAGE: '階段',
      ENTERSTAGENAME: '輸入階段名',
      SELECTFILE: '選擇檔案',
      SUCCESSURL: '成功URL',
      FAILURL: '失敗URL',
      NOTIFYTYPE: '通報類型',
      SELECTNOTIFYTYPE: '選擇通報類型',
      NOTIFYPARAMS: '通報參數',
      SHOWDETAILS: '顯示詳情',
      NOTIFYTYPEREQU: '選擇通報類型(必選)',
      EDITNOTIFYPARA: '編輯通報參數',
      OBJECTPATH: '目標路徑',
      ENTEROBJECTPATH: '输入目標路徑',
      ALLOWPAR: '部分允许',
      ADDCREATEOBJ: '添加創建對象',
      ISOBJECTNAME: '目標名必填!',
      FILETARGET: '目標文件',
      SELECTSN: '選擇序號',
      GENERSUMMARYREPORT: '生成匯總報告',
      SCRIPT: '腳本',
      SELECTSCRIPT: '選擇腳本',
      ACTIONSLIST: '動作列表',
      PARAMTYPE: '參數類型',
      ADDITIONALCON: '觸發條件',
      ADDCONDITION: '添加附加條件',
      EDITCONDITION: '編輯附加條件',
      DEVICEPARAMTRIGGER: '設備參數觸發器',
      INFORM: '通知',
      DURATION: '期間',
      FIELD: '領域',
      TRIGGEREVENTS: '觸發事件',
      TRIGGERTYPE: '觸發類型',
      INFORMEVENT: '通知事件',
      SELECTINFORMEVENT: '選擇通知事件',
      EVENTNAME: '事件名稱',
      ENTEREVENTNAME: '輸入活動名稱',
      PARAMETERSKEY: '參數鍵',
      ENTERPARAMETERSKEY: '輸入參數鍵',
      PARAMETERSVALUE: '參數值',
      ENTERPARAMETERSVALUE: '輸入參數值',
      PROTOCOLVER: '協議版本',
      SOURCEURL: '來源網址',
      TARGETURL: '目標網址',
      SESSIONID: '會話ID',
      OPERATEMODE: '操作模式',
      SELECTOPERATEMODE: '選擇操作模式',
      INPUTURLFORMATE: '支持的格式: http / https / ftp / ftps / sftp',
      HISTORY: '歷史',
      WORKFLOWHISTORY: '流程歷史記錄',
      CONFIGURATIONHISTORY: '配寘歷史記錄',
      POLICYHISTORY: '策略歷史',
      TRIGGERTIME: "觸發時間",
      TARGETPRODUCT: '目標產品',
      TARGETGROUP: '目標群組',
      TARGETSOFTWAREVERSION: '目標軟體版本',
      TARGETSN: '目標設備序號',
      TARGETSV: '目標裝置軟體版本',
      SUPPORTEDPRODUCT: '支援的產品',
      MANAGEDEDPRODUCT: '管理的產品',
      ALWAYSACTIVE: "始終活躍",
      SELECTACTIVEDATERANGE: "選擇活動日期和時間範圍",
      DAYOFWEEK: '星期幾',
      WITHONLYONCE: '只用一次',
      ACTIVEDATERANGE: '活動日期範圍',
      ACTIVETIMERANGE: '一天中的活躍時間範圍',
      EVERYDAY: '每天',
      SUNDAY: '週日',
      MONDAY: '週一',
      TUESDAY: '週二',
      WEDNESDAY: '週三',
      THURSDAY: '週四',
      FRIDAY: '週五',
      SATURDAY: '週六',
      EXECUTIONSTATUS: '執行狀態',
      EXECUTIONTIME: '執行時間',
      EDITACTIONS: '編輯操作',
      DOWNLOADASMULTI: '下載為多文件',
      DOWNLOADASONE: '下載為一個文件',
      LASTEXECUTIONTIME: '最後執行時間',
      SEARCHSN: '搜尋序號',
      ACTIVATE: '啟用',
      DEACTIVATE: '停用',
      LOADING: '載入中',
      STATETYPE: '狀態類型',
      STAGE: '階段',
      EDIT_STAGE_OPERATIONS_DESCRIPTION: '使用階段/操作構建工作流程',
      EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION: '使用階段/操作構建配寘',
      EDIT_POLICY_STAGE_OPERATIONS_DESCRIPTION: '透過階段/操作建構策略',
      TRIGGERCONDITIONS: '觸發條件',
      BUILD: '建置',
      SETUP_SCHEDULE_DESCRIPTION: '設置活動時間範圍',
      INVALID_VALUE_MESSAGE: '存在無效值，請檢查表單。',
      RESET_TOOLTIP: '重置為預設值',
      RESET_CONFIRM: '您是否要重置所有初始配寘值？',
      COUNT: '計數',
      COMPLETEDCOUNT: '已完成',
      PARTIALFAILEDCOUNT: '部分失敗',
      CANCELEDCOUNT: '取消',
      INPROGRESS: '處理中',
      FAILCOUNT: '已失敗',
      ADDTAGFAIL: '添加標籤失敗！',
      ADDTAGSUCC: '新增標簽成功!',
      DELTAGFAIL: '刪除標籤失敗!',
      DELTAGSUCC: '刪除標籤成功!',
      STEPAFTERSUCCESS: "如果失敗，停止後續任務",
      WORKFLOWOPERATIONLOG: '工作流程操作日誌',
      OPERATIONLOGS: '操作日誌'
    },
    USERS: {
      ACCOUNT: '使用者列表',
      ONLINEUSERS: '在線使用者',
      ONLINEUSERSDESCRIPTION: "正在使用的或者半小時內登入過的使用者數量",
      PROFILE: '頭像',
      PROFILEDESCRIPTION: '當前登入用戶的詳細信息。',
      STATUS: '狀態',
      ALLSTATUS: '所有狀態',
      ALLTYPE: '所有類型',
      ROLE: '角色',
      ROLELIST: '角色清單',
      ROLELISTDESCRIPTION: '用於用戶權限控制的角色列表。',
      CANNOTFINDROLE: "找不到角色權限列表",
      CHANGE: '更改',
      ACCOUNTLIST: '賬戶列表',
      ACCOUNTLISTDESCRIPTION: '當前登入用戶可管理的帳戶列表。',
      EDITUSER: '編輯用戶',
      EXPIRATION: '期限',
      DEPLOYEXPIRATION: '部署過期',
      CONTROLLIST: '權限控制表',
      ALLEDITABLE: '全部可編輯',
      EDITABLE: '可編輯',
      ALLREADONLY: '全部唯讀',
      READONLY: '唯讀 ',
      ALLDISABLED: '全部停用',
      DISABLED: '停用',
      SUBMIT: '提交',
      AMPNODE: 'AMP節點',
      SUPERADMINPASSWORD: '超級管理員密碼',
      ACTIVITIES: '使用者日誌',
      ACTIVITIESLIST: '用戶日誌列表',
      ACTIVITIESLISTDESCRIPTION: '記錄當前登入用戶可管理帳戶的所有請求事件。',
      DATERANGE: '選擇時間範圍',
      BEGINTIMEDATERANGE: '選擇開始日期',
      ENDTIMEDATERANGE: '選擇結束日期',
      DASHPERMISSION: '儀表板許可',
      CONFIRMPASSWORD: '確認密碼',
      NOTMATCH: '密碼和確認密碼不同.',
      NOTMATCH2: '新密碼和確認密碼不同.',
      ISPASSWORD: '需要輸入密碼。',
      ISUSERNAME: '必須填寫使用者名。',
      ADDUSER: '新增用戶',
      USERROLE: '用戶角色',
      CONFIRMPSW: '密碼必須包含8-128個字符, 並至少包含以下內容：字母、數字和符號。',
      SPECIALSYMBOLS: '只有1~32個字符的字母、數字和特殊符号(@!#?$/\_-.)允许.',
      SPECIALSYMBOLS_NO_DASH: '只有1~128個字符的字母、數字和特殊符号(@!#?$/\_.)允许.',
      ADDNEWUSER: '新增用戶',
      USERACTION: '用戶動作',
      LANGUAGE: '語言',
      AUTHORITYLIST: 'Widget類別權限列表',
      CHANGEPASSWORD: '更改密码',
      APIDOCUMENT: 'API 文档',
      USERMANUAL: '用戶手冊',
      OLDPASSWORD: '舊密碼',
      NEWPASSWORD: '新密碼',
      PREVIOUSTIME: '上次登入時間',
      PREVIOUSLOCATION: '上次登入地址',
      LASTLOGINLOCATION: '上次登入位置',
      CURRENTTIME: '最新登入時間',
      CURRENTLOCATION: '最新登入地址',
      EDITROLE: '編輯角色',
      ADDNEWROLE: '新增角色',
      AUTHORITY: '角色權限',
      EMAIL: '電子郵箱',
      ACTIONTYPE: '動作類型',
      EMAILERROR: '電子郵箱的格式不正確',
      ISMAIL: '必須填寫電子郵箱。',
      TAGHASCREATED: '此標籤已由其他用戶創建',
      DEVICEADMIN_TITLE: '設備管理',
      DEVICEADMIN_DESCRIPTION: '具有編輯/讀取設備類/設備管理員子類中設備相關Widget的權限。包括Widget：設備列表、即時更新、重新啟動、升級固件。',
      PRODUCTADMIN_TITLE: '產品管理',
      PRODUCTADMIN_DESCRIPTION: '具有編輯/讀取設備類/產品管理員子類中產品相關Widget的權限。包括Widget：已註冊設備、產品列表。',
      GROUPADMIN_TITLE: '群組管理',
      GROUPADMIN_DESCRIPTION: '具有編輯/讀取設備類/群組管理員子類中群組相關Widget的權限。包括Widget：群組列表、新增設備至群組、新增操作。',
      GENERALDATA_TITLE: '通用資料',
      GENERALDATA_DESCRIPTION: '具有編輯/讀取設備類/通用資料子類中設備通用資料相關Widget的權限。包括Widget：在線設備、群組、產品型號、通用資訊、關鍵績效指標。',
      ALARMMANAGEMENT_TITLE: '警報管理',
      ALARMMANAGEMENT_DESCRIPTION: '具有編輯/讀取設備類/警報管理員子類中特定設備的警報管理Widget的權限。包括Widget：警報管理。',
      REMOTETROUBLESHOOTING_TITLE: '遠端故障排除',
      REMOTETROUBLESHOOTING_DESCRIPTION: '具有編輯/讀取設備類/遠端故障排除子類中高級遠端相關Widget的權限。包括Widget：終端、命令 XML。',
      DATAMODEL_TITLE: '資料模型',
      DATAMODEL_DESCRIPTION: '具有編輯/讀取設備類/資料模型子類中資料模型相關Widget的權限。包括Widget：資料節點、參數資料。',
      NETWORKLOCATION_TITLE: '網路位置',
      NETWORKLOCATION_DESCRIPTION: '具有編輯/讀取設備類/網路位置子類中網路位置相關Widget的權限。包括Widget：位置、網路拓樸、覆蓋地圖。',
      LOGCOLLECTION_TITLE: '日誌收集',
      LOGCOLLECTION_DESCRIPTION: '具有編輯/讀取設備類/日誌收集子類中日誌收集相關Widget的權限。包括Widget：會話日誌列表、生成報告、操作日誌列表。',
      STATISTICALANALYSIS_TITLE: '統計分析',
      STATISTICALANALYSIS_DESCRIPTION: '具有編輯/讀取設備類/統計分析子類中統計分析Widget的權限。',
      WIFISPECIFIC_TITLE: 'WiFi特定',
      WIFISPECIFIC_DESCRIPTION: '具有編輯/讀取設備類/WiFi特定子類中WiFi特定相關Widget的權限。包括Widget：WiFi客戶端、WiFi無線狀態、WiFi分析器、WiFi鄰居列表。',
      CELLULARSPECIFIC_TITLE: '蜂窩特定',
      CELLULARSPECIFIC_DESCRIPTION: '具有編輯/讀取設備類/蜂窩特定子類中蜂窩特定相關Widget的權限。包括Widget：UE、蜂窩狀態、天線波束、鄰居/PLMN列表。',
      PMKPICOUNTER_TITLE: 'PM KPI計數器',
      PMKPICOUNTER_DESCRIPTION: '具有編輯/讀取設備類/PM KPI計數器特定子类中PM KPI計數器特定相关Widget的权限。包括Widget：PM 圖,PM 參數.',
      FAPSPECIFIC_TITLE: 'FAP特定',
      FAPSPECIFIC_DESCRIPTION: '具有編輯/讀取設備類/FAP特定子類中FAP特定相關Widget的權限。',
      APPSPECIFIC_TITLE: '應用特定',
      APPSPECIFIC_DESCRIPTION: '具有編輯/讀取設備類/應用特定子類中應用特定相關Widget的權限。包括Widget：應用列表、服務提供商、設備應用狀態。',
      YANGMODULE_TITLE: 'YANG模組',
      YANGMODULE_DESCRIPTION: '具有編輯/讀取設備類/YANG模組子類中YANG模組相關Widget的權限。',
      POWERSAVING_TITLE: '省電',
      POWERSAVING_DESCRIPTION: '具有編輯/讀取設備類/省電特定子類中FAP特定相關Widget的權限。包括Widget：能源管理.',
      DOCSIS_TITLE: 'Docsis特定',
      DOCSIS_DESCRIPTION: '具有編輯/讀取設備類/Docsis特定子類中Docsis特定相關Widget的權限。包括Widget：DOCSIS 狀態.',
      DEVICEALARM_TITLE: '設備警報',
      DEVICEALARM_DESCRIPTION: '具有編輯/讀取警報類/設備警報子類中設備警報相關Widget的權限。包括Widget：總警報、警報管理（下載/確認）、設備事件追蹤。',
      NOTIFICATIONMANAGMENT_TITLE: '通知管理',
      NOTIFICATIONMANAGMENT_DESCRIPTION: '具有編輯/讀取警報類/通知管理子類中通知管理相關Widget的權限。包括Widget：通知列表。',
      WORKFLOWSETUP_TITLE: '工作流程/配寘設置',
      WORKFLOWSETUP_DESCRIPTION: '具有編輯/讀取分類為配置類/工作流程/配寘設置子類的工作流程設置Widget的權限。包括Widget：工作流程/配寘列表、工作流程/配寘歷史。',
      OPERATIONSETUP_TITLE: '操作設置',
      OPERATIONSETUP_DESCRIPTION: '具有編輯/讀取分類為配置類/操作設置子類的操作設置Widget的權限。包括Widget：操作列表。',
      POLICYSETUP_TITLE: '策略設置',
      POLICYSETUP_DESCRIPTION: '具有編輯/讀取分類為配置類/策略設置子類的策略設置Widget的權限。包括Widget：能源策略。',
      SCRIPTSETUP_TITLE: '腳本設置',
      SCRIPTSETUP_DESCRIPTION: '具有編輯/讀取腳本設置Widget的權限。',
      FILESETUP_TITLE: '文件設置',
      FILESETUP_DESCRIPTION: '具有編輯/讀取分類為配置類/文件設置子類的文件設置Widget的權限。包括Widget：文件列表、特定供應商文件。',
      ACCOUNTADMIN_TITLE: '帳戶管理',
      ACCOUNTADMIN_DESCRIPTION: '具有編輯/讀取分類為用戶類/帳戶管理員子類的帳戶管理Widget的權限。包括Widget：個人檔案、帳戶列表。',
      ACCOUNTLOG_TITLE: '帳戶日誌',
      ACCOUNTLOG_DESCRIPTION: '具有編輯/讀取分類為用戶類/帳戶日誌子類的帳戶日誌Widget的權限。包括Widget：活動列表。',
      ACCOUNTROLE_TITLE: '帳戶角色',
      ACCOUNTROLE_DESCRIPTION: '具有編輯/讀取分類為用戶類/帳戶角色子類的帳戶角色Widget的權限。包括Widget：角色列表。',
      DEVICESTATISTICS_TITLE: '設備統計',
      DEVICESTATISTICS_DESCRIPTION: '具有編輯/讀取分類為分析類/設備統計子類的設備統計Widget的權限。包括Widget：在線設備、新增設備、事件代碼。',
      SYSTEMSTATISTICS_TITLE: '系統統計',
      SYSTEMSTATISTICS_DESCRIPTION: '具有編輯/讀取分類為分析類/系統統計子類的系統統計Widget的權限。包括Widget：數據庫狀態、設備會話持續時間、設備會話速率、空閒內存。',
      PROVISIONINGSTATISTICS_TITLE: '配置統計',
      PROVISIONINGSTATISTICS_DESCRIPTION: '具有編輯/讀取分類為分析類/配置統計子類的配置統計Widget的權限。包括Widget：配置代碼、軟件版本、SIM卡狀態。',
      PMSTATISTICS_TITLE: '性能管理統計',
      PMSTATISTICS_DESCRIPTION: '具有編輯/讀取分類為分析類/性能管理統計子類的性能管理統計Widget的權限。包括Widget：性能管理、性能管理狀態、性能報告。',
      SERVERSETTING_TITLE: '伺服器設置',
      SERVERSETTING_DESCRIPTION: '具有編輯/讀取分類為系統類/伺服器設置子類的伺服器設置Widget的權限。包括Widget：通用、即時更新、CWMP、Netconf、性能服務。',
      SERVERPREFERENCE_TITLE: '伺服器偏好設置',
      SERVERPREFERENCE_DESCRIPTION: '具有編輯/讀取分類為系統類/伺服器偏好設置子類的伺服器偏好設置Widget的權限。包括Widget：SMTP通知、SNMP Trap通知、報告、統計、日誌。',
      SERVERLICENSE_TITLE: '伺服器許可證',
      SERVERLICENSE_DESCRIPTION: '具有編輯/讀取分類為系統類/伺服器許可證子類的伺服器許可證Widget的權限。包括Widget：許可證。',
      SERVERREPORT_TITLE: '伺服器報告',
      SERVERREPORT_DESCRIPTION: '具有生成/讀取分類為附加功能類/報告匯出子類的伺服器報告Widget的權限。包括Widget：生成摘要報告。',
      SYSTEMEVENTS_TITLE: '系統事件',
      SYSTEMEVENTS_DESCRIPTION: '具有編輯/讀取分類為系統類/系統事件子類的系統事件Widget的權限。包括Widget：系統事件、註冊日誌。',
      SYSTEMNODES_TITLE: '系統節點',
      SYSTEMNODES_DESCRIPTION: '具有編輯/讀取分類為系統類/系統節點子類的系統節點Widget的權限。包括Widget：節點。',
      PERSONALTHEME_TITLE: '個人主題',
      PERSONALTHEME_DESCRIPTION: '具有編輯/讀取分類為附加功能類/個人主題子類的個人主題Widget的權限。',
      REPORTEXPORT_TITLE: '報告匯出',
      REPORTEXPORT_DESCRIPTION: '具有編輯/讀取分類為附加功能類/報告匯出子類的報告匯出Widget的權限。包括Widget：生成摘要報告。',
      SYSTEMINFORMATION_TITLE: '系統資訊',
      SYSTEMINFORMATION_DESCRIPTION: '具有編輯/讀取分類為系統類/系統資訊子類的AMP系統資訊Widget的權限。包括Widget：系統資訊。',
      FURBISHMENTSTATISTICS_TITLE: '翻新統計',
      GENERAL_TITLE: '一般',
      GENERAL_DESCRIPTION: '在 5G 核心類/一般子類中編輯/讀取一般小部件的權限。',
      UE_TITLE: '用戶設備',
      UE_DESCRIPTION: '在 5G 核心類/用戶設備子類中編輯/讀取用戶設備小部件的權限。',
      CELL_TITLE: '小區',
      CELL_DESCRIPTION: '在 5G 核心類/小區子類中編輯/讀取小區小部件的權限。',
      ALARM_TITLE: '警報',
      ALARM_DESCRIPTION: '在 5G 核心類/警報子類中編輯/讀取警報小部件的權限。',
      SYSTEM_TITLE: "系統",
      SYSTEM_DESCRIPTION: "有權編輯/讀取與 5GC 系統相關的操作和組件。",
      TOTALREQUESTS_TITLE: '請求總數',
      TOTALREQUESTS_DESCRIPTION: '過去24小時內的總請求次數',
      EXTERNAL_DEVICE_TITLE: '設備',
      EXTERNAL_DEVICE_DESCRIPTION: '具有編輯/讀取外部類/設備子類中設備相關NBI的權限。',
      CBSD_DEVICE_TITLE: '設備',
      CBSD_DEVICE_DESCRIPTION: '具有編輯/讀取CBSD類/設備子類中設備相關NBI的權限。',
      REQUESTSHISTORY_TITLE: "請求歷史",
      REQUESTSHISTORY_DESCRIPTION: "過去24小時總請求量的歷史圖表",
      IPREQUESTDISTRIBUTION: "IP請求分佈圖",
      IPREQUESTDISTRIBUTION_DESCRIPTION: "過去 24 小時內，每個 IP 的請求數量前五名",
    },
    ANALYSIS: {
      COLLAPSIBLE: '分析',
      SYSTEM: '系統統計',
      SESSIONDURATION: '設備會話時長',
      SESSIONRATE: '設備會話頻率',
      LATENCY: '設備請求延遲',
      REQUESTRATE: '設備請求頻率',
      PARSING: '設備請求解析',
      MEMORY: '記憶體使用',
      SPACEUSAGE: '空間使用',
      CPUUTILIZE: 'CPU 使用率',
      MEMORYUSAGECHART: '記憶體使用圖表',
      FREEMEMORY: '可用記憶體',
      CPUUSAGE: 'CPU 使用率',
      CPUUSAGECHART: 'CPU 使用率圖表',
      FREEDISK: '可用磁碟',
      DEVICE: '設備統計',
      PM: 'PM 統計',
      TOTALDEVICE: '總設備數',
      NEWDEVICE: '新設備',
      SESSIONS: '會話',
      EVENTCODE: '事件代碼',
      MEMORYUTILIZATION: '記憶體利用率',
      DISKUTILIZATION: '磁碟利用率',
      MEMORYUTILIZATIONDESCRIPTION: '記憶體利用率歷史圖表。',
      DISKUTILIZATIONDESCRIPTION: '磁碟利用率歷史圖表。',
      SESSIONDURATIONDESCRIPTION: '所有設備的平均會話時長的歷史圖表。會話時長：設備與 AMP 之間的會話所花費的總時間。',
      SESSIONRATEDESCRIPTION: '所有設備的平均會話頻率的歷史圖表。會話頻率：設備每秒向 AMP 發起的會話次數。',
      LATENCYDESCRIPTION: '所有設備的平均請求延遲的歷史圖表。請求延遲：設備與 AMP 之間的請求所花費的總時間。',
      REQUESTRATEDESCRIPTION: '所有設備的平均請求頻率的歷史圖表。請求頻率：設備每秒向 AMP 發起的請求次數。',
      PARSINGDESCRIPTION: '所有設備的平均請求解析的歷史圖表。請求解析：AMP 解析設備發起的請求所花費的總時間。',
      MEMORYDESCRIPTION: '可用記憶體歷史圖表。',
      CPUUSAGEDESCRIPTION: 'CPU 使用率歷史圖表。',
      FREEDISKDESCRIPTION: '可用磁碟歷史圖表。',
      TOTALDEVICEDESCRIPTION: '定期統計的設備總數的歷史圖表。',
      ONLINEDEVICEDESCRIPTION: '定期統計的線上設備總數的歷史圖表。',
      NEWDEVICEDESCRIPTION: '定期統計的新註冊設備總數的歷史圖表。',
      SESSIONSDESCRIPTION: '定期統計的每個產品的總會話數的歷史圖表。',
      EVENTCODEDESCRIPTION: '定期統計的每個產品的事件代碼總數的歷史圖表。',
      PROVISIONING: '設備配置統計',
      PMSTATISTICS: 'PM 統計',
      STATUSFORDEVICES: '線上設備狀態',
      RATE: '頻率',
      NUMBER: '數量',
      VERSIONDISTRIBUTION: '軟體版本分佈',
      CODEDISTRIBUTION: '配置代碼分佈',
      XMPPSTATUS: 'XMPP 狀態',
      XMPPSTATUS_DESCRIPTION: 'XMPP 服務狀態。',
      IMSSTATUS: 'IMS 註冊狀態',
      SIMSTATUS: 'SIM 卡狀態',
      IPSECSTATUS: 'IPSec 隧道狀態',
      STATUSFORDEVICE: '設備狀態總覽',
      DBSTATUS: '資料庫狀態',
      DBSTATUS_DESCRIPTION: '資料庫服務狀態。',
      SELECTDURATION: '選擇時長',
      PMSTATUS: 'PM 服務狀態',
      PMSTATUS_DESCRIPTION: 'PM 服務狀態。',
      REFURBISHMENETHISTORY: '重置歷史'
    },
    SYSTEM: {
      COLLAPSIBLE: "系統",
      EXTERNALSERVICE: "外部服務",
      PERFORMANCESERVICE: "性能服務",
      PERFORMANCESERVICEDESCRIPTION: "與性能管理相關的設置，例如設備上傳KPI檔案的預設URL。",
      PREFERENCE: "偏好",
      DBSERVER: "數據庫伺服器",
      SOURCES: "數據源",
      SNMPTRAP: "SNMP陷阱通知",
      SNMPTRAP_DESCRIPTION: "有關通過SNMP進行通知的配置。",
      SMTP: "SMTP通知",
      SMTP_DESCRIPTION: "有關通過SMTP進行通知的配置。",
      SMS: "簡訊通知",
      STATISTICSPRE: "統計",
      STATISTICSPRE_DESCRIPTION: "有關AMP中數據收集輪轉的偏好設置。",
      LOGPRE: "日誌",
      LOGPRE_DESCRIPTION: "設備會話和操作的日誌輪轉設置。",
      FAULT: "告警",
      FAULT_DESCRIPTION: "關於告警的偏好設置，例如自動確認和告警輪轉。",
      REPORTS: "報告",
      REPORTS_DESCRIPTION: "設備摘要報告的報告輪轉設置。",
      SYSREPORT: "伺服器報告列表",
      GENERATESYSREPORT: "生成伺服器報告",
      CONFIRMDELETE: "確認刪除所有伺服器報告",
      DOCONFIRMDELETE: "您要刪除所有伺服器報告嗎？",
      CONFIRMDELETESELECT: "確認刪除所選伺服器報告",
      DOCONFIRMDELETESELECT: "您要刪除所選伺服器報告嗎？",
      DOWNLOADCSV: "下載CSV",
      DOWNLOADTXT: "下載TXT",
      DOWNLOADXLSX: "下載XLSX",
      LICENSE: "許可證",
      LICENSE_DESCRIPTION: "有關AMP許可證的信息，例如狀態、類型、支持的協議和到期日。允許用戶更新許可證以延長到期或啟用AMP中的更多功能。",
      LICENSETYPE: "版本",
      LICENSESTATE: "許可證狀態",
      KEY_DESCRIPTION: "提供在輸入金鑰後啟動AMP的功能，以及與金鑰相關的資訊。",
      CPENUM: "設備容量",
      SESSIONNUM: "會話編號",
      REMAININGTIME: "剩餘時間",
      VALIDTO: "有效至",
      DISPLAY: "顯示",
      GENERAL: "常規",
      GENERAL_DESCRIPTION: "系統常規設置，例如會話日誌級別、登出超時和伺服器報告。",
      CWMP: "CWMP",
      CWMP_DESCRIPTION: "與CWMP協議相關的設置，例如會話超時和在線標準。",
      NETCONF: "Netconf",
      NETCONF_DESCRIPTION: "與NETCONF協議相關的設置，例如重試間隔和保持活動間隔。",
      USP: "USP",
      USP_DESCRIPTION: "與USP協議相關的設置，例如通過Websocket或MQTT。",
      LIVEUPDATE: "實時更新",
      LIVEUPDATE_DESCRIPTION: "用於立即在AMP和特定設備之間啟動通信的功能。",
      MQTT: "系統MQTT",
      FILES: "文件",
      FILES_DESCRIPTION: "系統預設文件設置，例如FOTA和設備日誌URL及認證。",
      TELEMETRY: "系統遙測",
      SUMMARYREPORT: "摘要報告",
      SUMMARYREPORT_DESCRIPTION: "與地圖相關的設置，例如選擇地圖數據提供者和來源。",
      EVENTS: "事件",
      SYSEVENTS: "系統事件",
      SYSEVENTS_DESCRIPTION: "列出所有與AMP相關的事件及詳細信息，例如嚴重性、特定問題、可能原因和事件時間。",
      REGISTRATIONLOG: '註冊日誌',
      REGISTRATIONLOG_DESCRIPTION: 'Usp mqtt 註冊日誌',
      NODES: "節點",
      DEL_TITLE_NODES: '是否要刪除節點',
      NODES_DESCRIPTION: "列出AMP節點信息，例如節點名稱、IP地址、AMP版本和運行時間。",
      ENTERLICENSEKEY: "輸入許可證密鑰",
      LICENSEKEY: "許可證密鑰",
      KEY: '密鑰',
      UNIQUEID: '唯一標識',
      KEYVALIDITY: '金鑰有效性',
      EDITURL: "編輯URL",
      VERIFYSMTP: "測試發送郵件",
      VERIFYSNMP: "測試發送SNMP",
      SERVICESTATUSTRACKING: "服務狀態跟蹤",
      KPIFACTORS: "KPI因素",
      KPIFACTORSTRACKING: "KPI因素跟蹤",
      VERIFYXMPP: "測試XMPP",
      MAP_DESCRIPTION: "地圖",
      PROCESS: "過程",
      NODEID: "節點ID",
      SEVERITY: "嚴重性",
      LOCATIONMAP: "位置地圖",
      LOCATIONMAP_DESCRIPTION: "與地圖相關的設置，例如選擇地圖數據提供者和來源。",
      FIVECORESERVICE: "5G核心服務",
      FIVECORESERVICE_DESCRIPTION: "與5G核心服務相關的設置，例如5G核心供應商和伺服器URL。",
      ENERGYMANAGEMENT: "能源管理",
      ENERGY_DESCRIPTION: "與能源管理相關的設定，例如功率上限與休眠間隔。",
      NIDS: "NIDS",
      NIDS_DESCRIPTION: "網路入侵檢測系統。",
      PROMETHEUS: "指標收集 - Prometheus",
      PROMETHEUS_DESCRIPTION: "與使用Prometheus進行數據收集相關的設置。",
      PROMETHEUS_PARAMETER_DESCRIPTION: {
        PULL_PATH: '用於抓取指標的URL。',
        USERNAME: '每個Prometheus指標拉取請求中Authorization頭中的用戶名。',
        PASSWORD: '每個Prometheus指標拉取請求中Authorization頭中的密碼。',
      },
      KAFKA: "指標收集 - Kafka",
      KAFKA_DESCRIPTION: "與使用Kafka進行數據收集相關的設置。",
      KAFKA_PARAMETER_DESCRIPTION: {
        BROKERS: 'kafka 經紀人的 URL。',
        TOPIC: '生產者訊息的 Kafka 主題。',
        ROUTING_KEY: '訊息路由機制。',
        ACCESS_TOKEN: '用於身份驗證的令牌。'
      },
      NODE_DESCRIPTION: {
        UPGRADE: '升級',
        ADDRESS: '地址',
        HASHKEY: '哈希鍵',
        TYPE: '類型',
        USERNAME: '用戶名',
        PASSWORD: '密碼',
        TARGEVERSION: '圓盾版本',
        COMPOENT: '組件',
        RUNTOOL: '運行工具',
        UPGRADESUCC: '升級成功',
        UPGRADEFAIL: '升級失敗',
        WAITFORUPDATE: '下載升級文件',
        STARTUPDATING: '開始升級...',
        BEINGUPDATED: '升級完成並等待重啟',
        SERVERRESTART: '伺服器正在重啟...',
        TIMEOUT: '升級超時！',
        ACSNODEREBOOT: '重啓AMP節點可能導致數據丟失。',
        ACSNODESHUTDOWN: '關閉AMP節點可能導致資料遺失。',
      },
      SETTING_DESCRIPTION: {
        GENERAL: {
          SESSIONTIMEOUT: '該設置為 CWMP 會話的可配置超時時間。',
          TRANSACTIONTIMEOUT: '交易超時是請求和響應的超時時間。',
          REFRESHINTERVAL: 'Netconf 重試間隔',
          KEEPALIVEINTERVAL: 'Netconf 保持連線間隔',
          SESSIONLOGLEVEL: '此設置影響詳細設備會話日誌視圖:如果是“ORIGINAL”,會話會以 SOAP 封包形式呈現;如果是“RPC”,則以解析後的 SOAP 消息呈現。',
          DEVICE: "此設定可切換開關發送 'Device.',以下所有參數均用於設備的初始上線 BootStrap 方法。",
          DEVICE_XMPP_CONNECTION_1: '此設置可開關發送 Device.XMPP.Connection.1 用於 BootStrap 方法。',
          DEVICE_MQTT_CLIENT_1: '此設置開關發送 Device.MQTT.Client.1 用於 BootStrap 方法。',
          DEIVCE_DEVICEINFO_XVENDOR_HOLD: '此設置可開關發送Devcie.DeviceInfo.X_VENDOR.HOID 用於 BootStrap 方法。',
          DEVICE_MANAGEMENTSERVER_PERIODICINFORMTIME: '此設置可開關發送Device.ManagementServer.PeriodicInformTime 用於 BootStrap 方法。',
          TYPEOFINFORMRECEIVEDWITHIN: '此設置用於判斷設備是否在線。',
          INTERVALOFINFORMRECEIVEDWITHIN: '此設置配置丟棄條件的時間段。',
          SERVERREPORTENABLED: '此設置可開關服務器報告生成:如果開啟,ACS 將生成服務器報告。如果關閉,ACS 將不會生成服務器報告。',
          SERVERREPORTEMAILNOTIFICATION: '開啓/關閉系統報表郵件通知功能。',
          SERVERREPORTPERIOD: '該設置配置服務器報告生成的頻率(以天為單位)。',
          SERVERREPORTCONTENT: '系統報告可選內容，AMP支持MONGODB、XMPP、MQTT和PM服務器的信息收集。',
          PRIORITY: '定義 CPE 的 IP 檢測源的優先級:RemoteAddress - IP 從指定的數據節點獲取。Eq.Device.DeviceInfo.X_Vendor_GlobalIPAddress; X-Forwarded-For - IP 從 XForwarded-For HTTP 頭部獲取;Custom - IP 從自定義 HTTP 頭部獲取,該設置中指定了名稱。',
          CUSTOMHEADER: '自定義 HTTP 頭部的名稱,其中包含 CPE 的實際 IP 地址。',
          IDLETIMEOUT: '系統會話過期時間',
          SWAGGER_ENABLED: '啟用/停用 restful API UI。',
          CLIENT_URL: 'CPE 使用 CPE WAN 管理協議連接到 ACS 的 URL。'
        },
        CONNECTIONREQUEST: {
          USERNAME: 'CR機制的可配置用戶名。',
          PASSWORD: 'CR機制的可配置密碼。',
          RETRYTIMEOUT: 'CR的可配置超時時間。',
          NUMBEROFRETRY: '最大CR嘗試次數。',
          TYPE: "CR 的類型。",
          // XMPPADDRESS:'',
          XMPPDOMAIN: '此設定為可配置的域名,用於JID自動生成。',
          XMPPPORT: 'XMPP服務器的端口。預設值適用於ejabberd服務器。',
          XMPPACSUSERNAME: 'XMPP CR調用的可配置ACS用戶名。',
          XMPPACSPASSWORD: 'XMPP CR調用的可配置ACS密碼。',
          XMPPADMINPORT: '服務器管理員的XMPP服務器端口。預設值適用於ejabberd服務器。',
          XMPPADMINUSERNAME: '可配置的XMPP管理員用戶名憑據,用於自動XMPP用戶註冊。',
          XMPPADMINPASSWORD: '可配置的XMPP管理員密碼憑據,用於自動XMPP用戶註冊。',
          XMPPRESOURCE: 'XMPP CR的可配置資源值。',
          XMPPUSETLS: '此設定切換開/關XMPP CR的TLS使用:如果開啟,則啟用TLS。如果關閉,則禁用TLS。'
        },
        USP: {
          BINDING: '綁定類型，WebSocket或MQTT。',
          ADDRESS: 'MTP服務器域名',
          PORT: 'MTP連接端口',
          APIKEY: "在MQTT中,使用API金鑰來查詢伺服器的狀態。",
          USERNAME: '經紀人所需的用戶名(如果有)。',
          PASSWORD: '經紀人所需的密碼(如果有)。',
          USE_TLS: 'MTP over TLS。',
          EXADDRESS: '設備可以連接到WebSocket/MQTT/CWMP/XMPP服務的地址或域。',
          EXPORT: '設備可以連接到WebSocket/mqtt服務的埠。',
          USETLS: '設備是否使用TLS連接到WebSocket/MQTT/CWMP/XMPP服務。',
          EXURL: '設備可以連接到CWMP服務的URL。',
        },
        FILES: {
          DOWNLOAD: {
            LATESTFIRMWARE: '此設定允許用戶指定CSR用戶的最新固件版本。',
            FIRMWARESERVERURL: '用於AP下載固件的文件服務器路徑。',
            FIRMWARESERVERUSERNAME: '用於與文件服務器進行身份驗證的用戶名憑據。',
            FIRMWARESERVERPASSWORD: '用於與文件服務器進行身份驗證的密碼憑據。',
          },
          UPLOAD: {
            FILETYPE: '設備上傳文件類型',
            INSTANCEPATH: '資料模型中用戶可以上傳日誌檔的路徑。',
            LOGUPLOADURL: 'CSR用戶可以上傳AP日誌文件的URL。此功能需要額外的伺服器配置。',
            USERNAME: '可配置的文件上傳用戶名。',
            PASSWORD: '可配置的文件上傳密碼。'
          },
          CONF_DOWNLOAD: {
            DEFAULT_FILE: '從檔案中選擇的預設設定檔（類型為 3 Vendor Configuration File）。',
            DEFAULT_FILE_URL: '預設設定檔的 URL。',
            FILETYPE: '設備設定檔的下載類型。',
            CONFURL: '設備從 AMP 下載設定檔的 URL。',
            USERNAME: '用於設備下載的可設定使用者名稱。',
            PASSWORD: '用於設備下載的可設定密碼。'
          },
        },
        TELEMETRY: {
          TELEMETRYSERVERREDIRECTION: '此設定切換開/關設備信息頁面上的第三方網站鏈接按鈕:如果開啟,則顯示第三方網站鏈接按鈕。如果關閉,則不顯示第三方網站鏈接按鈕。',
          VENDOR: '提供特定技術、服務或產品的供應商，選項包括 Druid、DNMM、HP 和 Open 5GC。',
          TYPE: '開源網路入侵偵測系統類型，選項包括Suricata',
          SERVERURL: '第三方網站的URL。',
          SERVERUSERNAME: '用於登錄第三方網站的可配置用戶名。',
          SERVERPASSWORD: '用於登錄第三方網站的可配置密碼。',
          KPIFACTORS: 'KPI因素可以根據使用者定義的規則監控PM參數狀態。',
          UEINTERVAL: '可設定的UE定時器間隔。',
          CELLINTERVAL: '可設定的單元定時器間隔。',
          ALARMINTERVAL: '可配置警報檢查的時器間隔。',
          COMMONINTERVAL: '可配置通用定時器間隔。',
          APIURL: '效能服務的北向介面位址。',
          APIUSERNAME: '效能服務北向介面身分的使用者名稱。',
          APIPASSWORD: '效能服務北向介面身分的密碼。'
        },
        MAP: {
          TYPE: '地圖服務器類型，AMP支持Google地圖和Open街道地圖。默認是谷歌地圖。',
          URL: '地圖服務器url。',
          APIKEY: '映射api密鑰。',
        }
      },
      PREFERENCE_DESCRIPTION: {
        SMTP: {
          MAIL_HEALTHY: '系統健康監控,例如CPU負載、磁碟使用率、崩潰和許可證。',
          MAIL_FORM: '電子郵件寄件者使用者名稱',
          MAIL_HOST: '對應到郵箱的SMTP伺服器位址。',
          MAIL_USERNAME: '電子郵件寄件者位址',
          MAIL_PASSWORD: '電子郵件寄件者密碼',
          MAIL_PORT: '電子郵件寄件者連接埠',
          MAIL_TO: '收件人地址',
          MAIL_SMTP_AUTH: 'SMTP協定相關的配置,是否需要驗證。',
          MAIL_SMTP_SECURITY: 'SMTP協定相關的配置。',
          MAIL_TRANSPORT_PROTOCOL: '目前未使用',
        },
        SNMPTRAP: {
          SNMPTRAP_TARGET: 'SNMP Trap目標位址',
          SNMPTRAP_PORT: '用於發送請求的UDP埠,預設為161。',
          SNMPTRAP_RETRIES: '重新發送請求的次數,預設為1。',
          SNMPTRAP_TIMEOUT: '在重新嘗試或失敗之前等待回應的毫秒數,預設為5000。',
          SNMPTRAP_TRANSPORT: '指定要使用的傳輸方式,可以是udp4或udp6,預設為udp4。',
          SNMPTRAP_TRAPPORT: '用於發送陷阱和通知的UDP埠,預設為162。',
          SNMPTRAP_VERSION: 'snmp.Version1或snmp.Version2c',
          SNMPTRAP_BACKOFF: '每次重試時增加超時時間的倍數,預設為1,表示不增加。',
          SNMPTRAP_COMMUNITY: '用於確保通訊和認證的安全性。',
        },
        REPORTS: {
          DEVICE_REPORT_CLEANUP_ENABLE: '如果打開,該設置將開啟自動設備報告存儲清理。如果關閉,自動設備報告存儲清理將被禁用。',
          DEVICE_REPORT_RETENTION_PERIOD: '該設置指定在存儲中保留設備報告條目的天數。',
          SERVER_REPORT_CLEANUPZZ_ENABLE: '如果打開,該設置將開啟自動服務器報告存儲清理。如果關閉,自動服務器報告存儲清理將被禁用。',
          SERVER_REPORT_RETNETION_PERIOD: '該設置指定在存儲中保留服務器報告條目的天數。',
        },
        STATISTICS: {
          CPU_COLLECTION_ENABLE: '如果显示啟用，則許可證允許收集CPU名額。如果显示禁用，則許可證不允許收集CPU名額。',
          DISK_COLLECTION_ENABLE: '如果显示啟用，則許可證允許收集磁片名額。 如果显示禁用，則許可證不允許收集磁片名額。',
          MEMORY_COLLECTION_ENABLE: '如果显示啟用，則許可證允許收集記憶體名額。如果显示禁用，則許可證不允許收集記憶體名額。',
          REPORT_ENABLE: '如果显示啟用，則許可證允許報告統計指標。如果显示禁用，則許可證不允許報告統計指標。。',
          REPORT_PERIOD: '該設置指定收集指標的頻率。',
          PM_KPI_COLLECTION_RETENTION: '此設定指定保留 PM KPI DB 資料的天數。',
          PM_KPI_FILE_RETENTION: '此設定指定保留 PM KPI 檔案的天數。'
        },
        LOGCONFIG: {
          DEVICE_GROUP_OPERATION_LOG_CLEANUP_ENABLE: '如果显示啟用，許可證允許自動清除設備組操作數據。如果显示禁用，則許可證不允許自動清除設備組操作數據。',
          DEVICE_GROUP_OPERATION_LOG_RETENTION_PERIOD: '該設置指定在日誌中保留組操作數據的天數。',
          DEVICE_OPERATION_LOG_CLEANUP_ENABLE: '如果显示啟用，則許可證允許自動清除設備操作數據。如果显示禁用，則許可證不允許自動清除設備操作數據。',
          DEVICE_OPERATION_LOG_RETENTION_PERIOD: '該設置指定在日誌中保留設備操作數據的天數。',
          SESSION_LOG_CLEANUP_ENABLE: '如果显示啟用，則許可證允許自動清除事件日誌。如果显示禁用，則許可證不允許自動清除事件日誌。',
          SESSION_LOG_RETENTION_PERIOD: '該設置指定在日誌中保留會話記錄的天數。',
          STATISTICS_LOG_CLEANUP_ENABLE: '如果显示啟用，則許可證允許自動統計日誌清理。如果显示禁用，則許可證不允許自動統計日誌清理。',
          STATISTICS_LOG_RENTENTION_PERIOD: '該設置指定在日誌中保留統計日誌的天數。',
        },
        FAULTMANAGEMENT: {
          EVENT_ALARM_ACK_ENABLE: '若開啟,則事件自動確認。若關閉,則需要手動確認。',
          EVENT_ALARM_CLEANUP_ENABLE: '若開啟,則自動清理事件日誌。若關閉,則停用自動清理事件日誌。',
          EVENT_ALARM_EMAIL_ENABLE: '若開啟,則在事件發生時自動發送郵件通知。若關閉,則不發送警報郵件通知。',
          EVENT_ALARM_RETENTION_PERIOD: '該設定指定保留事件日誌的天數。'
        }
      }
    },
    COMMON: {
      DEVICES: '設備',
      DEVICE: '設備',
      CLIENTS: '終端裝置',
      CLIENT: '終端裝置',
      USERS: '使用者',
      ALARMS: '告警',
      TOTALALARMS: '告警總數',
      HISTORYALARMS: '歷史告警',
      CRITICALALARMS: '嚴重告警',
      MAJORALARMS: '主要告警',
      WARNINGALARMS: '警告告警',
      MINORALARMS: '次要告警',
      PRODUCTS: '產品型號',
      PRODUCTSDISTRIBUTION: '產品分佈',
      REGISTERDEVICECOUNT: "註冊設備數量",
      REGISTERDEVICECOUNTDISTRIBUTION: "每個產品設備數量分布圖",
      ONLINEDEVICE: '在線設備',
      HISTORYONLINEDEVICE: "歷史上線設備",
      APPLY: '應用',
      DELETE: '刪除',
      DELETEALL: '刪除所有',
      CANCEL: '取消',
      OK: '確定',
      CLOSE: '關閉',
      ADD: '新增',
      EDIT: '編輯',
      Fail: '失敗',
      SERIAL_NUMBER: '序號',
      PRODUCT_CLASS: '產品類型',
      ACTION: '設定檔',
      NEW: '新',
      SELECTACTION: '選擇動作',
      IMPORT: '導入',
      DOWNLOAD: '下載',
      DOWNLOADLOG: '下載日誌',
      SAVE: '保存',
      DONTSAVE: '不保存',
      UPLOAD: '上傳',
      NAME: '名稱',
      ENTERNAME: '輸入名稱',
      VERSION: '版本',
      PRIORITY: '優先權',
      ENTERVERSION: '輸入版本',
      SOFTVERSION: '軟體版本',
      TYPE: '類型',
      SELECTTYPE: '選擇類型',
      PREVIOUS: '上一頁',
      NEXT: '下一頁',
      USERNAME: '用戶名',
      PASSWORD: '密碼',
      USERNAME1: '用戶名',
      PASSWORD1: '密碼',
      ENTERUSERNAME: '輸入用戶名',
      ENTERPASSWORD: '輸入密碼',
      UPDATE: '更新',
      UNINSTALL: '卸載',
      PARAMETERS: '參數',
      PARAMNAME: '參數路徑',
      ENTERPARAMNAME: '輸入參數路徑',
      PARAMTYPE: '參數類型',
      SELECTPARAMTYPE: '選擇參數類型',
      PARAMVALUE: '參數值',
      ENTERPARAMVALUE: '輸入參數值',
      ADDPARAM: '新增參數',
      EXECUTE: '執行',
      SIZE: '大小',
      CANCELALL: '取消所有',
      FIELDREQUIRED: '此字段必填!',
      DETAILS: '詳情',
      SELECTPRODUCTNAME: '選擇產品型號',
      SELECTPRODUCT: '選擇產品型號',
      AND: '和',
      EDITPARAM: '編輯參數',
      VALUE: '值',
      EXPANDCOLLROW: '展開/收合行',
      PORT: 'Port',
      HOST: 'Host',
      THECUSTOMIZE: '主題編輯器',
      CUSTOMIZEREALTIME: '即時定制和預覽',
      SKIN: '皮膚',
      LIGHT: '淺色',
      BORDERED: '有邊的',
      DARK: '深色',
      RED: "紅色",
      BLUE: "藍色",
      SEMIDARK: '半深色',
      ROUTETRA: '切換頁面特效',
      FADEINLEFT: '向左漸隱',
      ZOOMIN: '放大',
      FADEIN: '漸顯',
      NONE: '無',
      MENULAYOUT: '選單配置',
      VERTICAL: '垂直',
      HORIZONTAL: '水平',
      MENUCOLL: '選單收合',
      MENUHIDDEN: '選單隱藏',
      NAVBARCOLOR: '導航欄顔色',
      NAVBARTYPE: '導航欄類型',
      MENYTYPE: '選單類型',
      FLOATING: 'Floating',
      STICKY: 'Sticky',
      STATIC: 'Static',
      FOOTERTYPE: '頁腳類型',
      WIDGETS: '自定義Widgets',
      EDITMODE: '編輯方式',
      CUSWIDGETS: '自定義Widgets',
      LOGOUT: '註銷',
      RECENTNOTIF: '最近的通知',
      NOTIFICATIONS: '通知',
      READMORE: '查看更多',
      TOTAL: '總數',
      SELECTED: '已選',
      CREATED: '創建時間',
      SELECTCOLUMN: '選擇列',
      ACTIVE: '有效',
      ALLOW: '允許',
      YES: '是',
      CLIENTLIST: '終端裝置列表',
      WIFICLIENTLIST: 'WiFi 終端裝置列表',
      WIFICLIENTLIST_DESCRIPTION: '列出此群組中的所有 WiFi 用戶端。',
      WIFICLIENTLISTDESCRIPTION: '這台設備可用的無線電/頻段上關聯客戶端的當前 WiFi 狀態。',
      ONLINE: '在綫',
      OFFLINE: '離綫',
      EXPORT: '導出',
      MQTT: 'MQTT',
      CWMP: 'CWMP',
      NETCONF: 'NETCONF',
      CURRENTNODE: '當前節點',
      CHILDNODE: '子節點',
      EDITSTAGE: '編輯階段名稱',
      STAGENAME: '階段名稱',
      ENTERSTAGENAME: '輸入階段名稱',
      OPERATIONNAME: '操作名稱',
      ADDOPERATION: '添加操作',
      ALLPROTOCOL: '所有協議',
      ALLPRODUCTS: '所有產品',
      ALLEVENT: '所有事件',
      USER: '用戶',
      ALLFILETYPES: '所有文件類型',
      ALLTARGETTYPES: '所有目標類型',
      TRANSMISSTIONTYPE: '傳輸類型',
      SELECTTRANSMISSTIONTYPE: '選擇傳輸類型',
      REMOVEFROMGROUP: '從組中删除',
      SHUTDOWN: '停機',
      NOPERMISSION: '當前用戶無權讀取此頁中的內容。',
      SEPARATED_BY_SEMICOLONS: '用分號分隔',
      SEPARATED_BY_COMMAS: '用逗號分隔',
      MAIL_SEPARATED_BY_SEMICOLONS: '用分號分隔 (<EMAIL>;<EMAIL>;)',
      SN_SEPARATED_BY_COMMAS: '用逗號分隔 (sn1,sn2)',
      WIDGETNAME: '輸入Widget名稱、類別或子類別。',
      APNAMEEDITSUCC: '編輯 AP 名稱成功！',
      APNAMEEDITFAIL: '編輯 AP 名稱失敗。',
      LOCATE: '定位',
      RELOAD: "重新加載",
      DATA: "資料",
      STATE: "狀態",
      REGISTER: '註冊',
      GROUP: '群組',
      SELECTCHARTTYPE: '選擇圖表類型',
      OPEN_MAXIMIZE: "開啟最大化",
      SELECTORENTER: '請選擇或輸入您的選項',
      INVALIDFILETYPE: '文件類型無效。請選擇以下類型之一: ',
    },
    CONFIRM: {
      CONF: '確認',
      REMOVAL: '刪除?',
      REBOOT: ' 重新啟動?',
      SHUTDOWN: ' 停機?',
      ADDFAIL: '新增失敗!',
      NAMEEXIST: '名稱已經存在!',
      ALARMNOTIF: '告警通知更新成功',
      CONFREMGROUP: '確認移出群組?',
      CONFGROUP: '確認移除群組?',
      CONFGROUPS: '確認組別刪除？',
      DOGROUP: '是否要刪除設備:',
      FROMGROUP: '?',
      IMPORTSUCCESS: "匯入成功！",
      IMPORTFAIL: '導入失敗!',
      FILEEMPTY: '文件為空',
      NOTSUPPORT: '不支持此文件格式',
      DODELETEGROUP: '是否要刪除群組 ',
      DODELETEGROUPS: '您想刪除這些群組嗎？',
      GROUPOPER: '設備群組操作!',
      WORKFLOWOPER: '設備工作流程操作!',
      WORKFLOWOPERATION: '設備工作流程操作',
      WORKFLOWDOREMOVEALL: '您想從設備工作流程操作日誌中刪除所有條目嗎？',
      WORKFLOWCLEANSUCC: '設備工作流程操作日誌已清理成功',
      WORKFLOWNOTCLEAN: '設備工作流程操作日誌未被清理',
      SETGROUPOPERSUCC: '設置組操作成功!',
      RENAMESUCC: '重命名成功',
      CONFNIT: '確認下載告警通知?',
      DODOWNLOADNIT: '是否下載告警通知 ',
      ALARMNIT: '通知',
      DOWNLOADSUCC: '下載成功',
      PLESELECT: '請先選擇告警通知!',
      CONFDOWNLOADNIT: '確認下載選擇的告警通知?',
      DODOWNLOADSELECT: '是否要下載選擇的告警通知?',
      DELETESUCC: '刪除選擇的告警通知成功!',
      DOWANT: '是否要',
      THEALARMNIT: '告警通知',
      SUCC: ' 成功!',
      CONFDELETENIT: '確認刪除告警通知?',
      DODELETENIT: '是否要刪除告警通知',
      NITDELSUCC: '刪除告警通知刪除成功!',
      NITID: '告警通知(ID:',
      WANDEL: ')已刪除!',
      NITDELETEFAIL: '告警通知刪除失敗!',
      NOTDEL: ')未刪除!',
      SELECTFIRST: '請先選擇告警通知!',
      STATEITEMS: '所選的告警通知包含一个或多個活動狀態項!',
      CONFSELECTNIT: '確認刪除選擇的告警通知?',
      DOSELECTNIT: '是否要刪除選擇的告警通知?',
      SELECTNITSUCC: '刪除選擇的告警通知成功!',
      GROUPOPERATION: '設備群組操作',
      CANCELSUCC: ' 已取消成功',
      REMOVEDLOG: ' 從日誌中移除',
      CONFCLEANUP: '確認清理日誌',
      DOREMOVEALL: '是否要從群組操作日誌中刪除所有條目?',
      GROUPCLEANSUCC: '清除設備操作日誌成功',
      GROUPNOTCLEAN: '設備群組操作未被清除',
      CONFPRODUCTREM: '確認刪除型號',
      DOPRODUCT: '是否要刪除型號 ',
      CONFRANREM: '確認移除無線電接入網絡 ',
      CONFAPNREM: '確認移除 WiFi AP 網絡 ',
      CONFMESHREM: '確認移除 WiFi Mesh 網絡 ',
      DORAN: '您要刪除此無線電接入網絡嗎 ',
      DOAP: '您要刪除此 WiFi AP 網絡嗎 ',
      DOMESH: '您要刪除此 WiFi Mesh 網絡嗎 ',
      CONFPRODUCTBAN: '確認禁止型號',
      CONFRANBAN: '確認禁止無線電接入網路',
      CONFAPNBAN: '確認禁止 WiFi AP 網路',
      CONFMESHNBAN: '確認禁止 WiFi Mesh 網路',
      PRODUCTACCESS: '?此產品的設備將無法訪問服務器.',
      PRODUCTSACCESS: '?這些產品的設備將無法訪問服務器.',
      RANSACCESS: '？此無線電接入網路的裝置將無法訪問伺服器。',
      APSACCESS: '？此 WiFi AP 網路的裝置將無法訪問伺服器。',
      MESHSACCESS: '？此 WiFi Mesh 網路的裝置將無法訪問伺服器。',
      CONFFILE: '確認刪除檔案?',
      DELFILESUCC: '刪除檔案成功',
      CONFSCRIPT: '確認刪除腳本?',
      DOSCRIPT: '是否要刪除腳本',

      CONFFLOW: '確認下載流程?',
      WORKFLOW: '流程',
      DOWORKFLOW: '是否要下載流程',
      CONFSELECT: '確認要下載選定的流程?',
      DOSELECTFLOW: '您要將選定的工作流程下載為多個檔案嗎？',
      DOSELECTFLOWASONE: '您要將選定的工作流程下載為一個檔案嗎？',
      DOWNSELECTFLOW: '下載選定的流程成功!',
      DOWNSELECTFILESUCCESS: '已成功下載所選檔案！',
      SELECTFLOW: '刪除選定的流程成功!',
      PLEASEFLOWS: '請先選擇流程!',
      THEFLOW: '該流程',
      CONFDELFLOW: '確認刪除流程?',
      DODELFLOW: '是否要刪除流程',
      DELSUCC: '流程刪除成功',
      FLOWID: '流程(ID:',
      FLOWDELFAIL: '流程刪除失敗',
      FLOWITEM: '所选流程包含一個或多個活動狀態项',
      CONFSELECTFLOW: '確認刪除選定流程?',
      DODELSELECTFLOW: '是否要刪除選定流程?',

      CONFCONFIGURATION: '確認下載配寘?',
      CONFIGURATION: '配寘',
      DOWORKCONFIGURATION: '是否要下載配寘',
      CONFSELECTCONFIGURATION: '確認要下載選定的配寘?',
      DOSELECTCONFIGURATION: '您要將選定的配寘下載為多個檔案嗎？',
      DOSELECTCONFIGURATIONASONE: '您要將選定的配寘下載為一個檔案嗎？',
      DOWNSELECTCONFIGURATION: '下载选定的配寘成功!',
      DELSELECTCONFIGURATION: '刪除選定的配寘成功!',
      PLEASECONFIGURATIONS: '請先選擇配寘!',
      THECONFIGURATION: '該配寘',
      CONFDELCONFIGURATION: '確認刪除配寘?',
      DODELCONFIGURATION: '是否要刪除配寘',
      DELCONFIGURATIONSUCC: '配寘刪除成功',
      CONFIGURATIONID: '配寘(ID:',
      CONFIGURATIONDELFAIL: '配寘刪除失敗',
      CONFIGURATIONITEM: '所选配寘包含一個或多個活動狀態项',
      CONFDELSELECTCONFIGURATION: '確認刪除選定配寘?',
      DODELSELECTCONFIGURATION: '是否要刪除選定配寘?',

      CONFPOLICY: '確認下載策略？',
      DOWORKPOLICY: '您是否要下載策略',
      POLICYID: '策略(ID:',
      POLICYFLOWSUCC: '策略更新成功',
      POLICY: '策略',
      POLICYCLONESUCCESS: '策略複製成功！',
      CONFDELPOLICY: '確認刪除策略？',
      DODELPOLICY: '是否要刪除策略？',
      DELPOLICYSUCC: '策略刪除成功',
      POLICYDELFAIL: '策略刪除失敗',
      PLEASEPOLICYS: '請先選擇策略！',
      DOSELECTPOLICY: '您想將選定的策略下載為多個檔案嗎？',
      DOSELECTPOLICYSONE: '您想將選定的策略下載為一個檔案嗎？',
      CONFSELEPOLICY: '確認下載所選策略？',
      DOWNSELECTPOLICY: '所選策略下載成功！',
      POLICYITEM: '所選策略包含一個或多個活動狀態條目',
      CONFDELSELECTPOLICY: '確認刪除選定策略？',
      DODELSELECTPOLICY: '是否要刪除選定策略?',
      DELSELECTPOLICY: '刪除選定的策略成功!',

      CONPROFILE: '是否下載設定檔？',
      PROFILE: '設定檔',
      DOPROFILE: '如果下載了設定檔',
      CONOSELECT: '確認下載選擇的設定檔？',
      DOSELECTPROFILE: '您想將所選的工作設定檔下載到多個檔案中嗎？',
      DOSELECTPROFILEASONE: '您想將所選的工作設定檔下載為檔案嗎？',
      DOWNSELECTPROFILE: '選定的設定檔已成功下載！',

      PROVISIONINGFILE: "確認下載檔案嗎？",
      PROVISIONINGCONFILE: "是否下載檔",
      PROVISIONINGSELECT: "确认下载選中的文件嗎？",
      PROVISIONINGDOSELECTFILE: "您想將所選的檔下載到多個檔案中嗎？",
      PROVISIONINGDOSELECTFILEONE: "您想將選定的文件下載為一個文件嗎？",
      PROVISIONINGCONOSELECT: "您想將所選的檔下載為檔案嗎?",

      ENDGREATER: '結束時間應該大於當前時間!',
      STARTHAVEVALUE: '開始日期或結束日期應該是一個值!',
      ENDGREATERSTART: '結束日期應該大於開始日期!',
      STARTENDALUE: '開始時間或結束時間應該是一個值!',
      EXACTLYEQUAL: '時間不可能完全相等!',
      CONFSTAGE: '確認刪除階段嗎?',
      BADREQ: '錯誤的請求',
      PARAMNEED: '保存前需要填寫參數',
      FLOWSUCC: '流程更新成功',
      CONFIGURATIONSUCC: '配寘更新成功',
      WASUPDATE: ')已更新!',
      CROSSCLICK: '點擊+',
      FLOWADDSUCC: '流程新增成功',
      WASADD: ')已添加!',
      FORMFAIL: 'Form驗證失敗!',
      CONFOPER: '確認移除操作?',
      VERSIONERROR: '版本錯誤!',
      ONLY16: '只能由字母、數字和特殊字符(_ - .)組成1~16個字符.',
      FORKSUCC: '流程分叉成功',
      WASFORK: '已分叉,新優先順序:',
      FLOWSPACE: '流程 ',
      OPERFORKSUCC: '操作分叉成功!',
      OPERFLOWSPACE: '操作 ',
      OPERATION: '操作',
      CONFDELOPER: '確認刪除配置文件?',
      DODELOPER: '要刪除配置文件嗎',
      DELSUCCESS: '刪除成功!',
      PLEOPERFIRST: '請先選擇操作!',
      CONFSELECTOPER: '確認刪除選取的設定檔?',
      DOSELOPER: '您要刪除選定的設定檔嗎?',
      DELOPERSUCC: '成功刪除所選設定檔!',
      CONFACTIONRE: '確認操作刪除？',
      STAGE: '階段?',
      ACTION: '設定檔?',
      OPERUPDATESUCC: '設定檔更新成功',
      OPERADDSUCC: '設定檔添加成功',
      ALARMADDSUCC: '告警通知添加成功',
      CONFLICT: '衝突',
      ALARMNOTNAME: '告警通知名稱是:',
      ALREADYEXIST: '已存在',
      CONTAINDATA: '包含衝突的數據',
      NAMEERROR: '名稱錯誤!',
      ONLYNAMESYM: '只允許使用1-64個字元的字母、數位和特殊字元（_-space）.',
      CLONENOTI: '告警通知複製成功!',
      WANCLONE: '已複製.新配置:',
      CONFIGUPDATESUCC: '配置更新成功',
      SETOPERSUCC: '設置設備操作成功!',
      SETOPERFAIL: '設置設備操作失敗!',
      TASKSUCC: '任務添加成功!',
      LABELSUCC: '編輯標牌成功!',
      LABELFAIL: '編輯標牌失敗!',
      UPDATEDEV: '更新成功',
      CMDSENQUSUCC: '命令加入隊列成功!',
      FORKNOT: '告警通知分叉成功!',
      WANIMPORT: ')未導入.',
      NOTIIMPORTFAIL: '告警通知導入失敗',
      IMPORTSUCC: '導入失敗!',
      GROUPCREATESUCC: '設備群組創建成功',
      IMPORTTOGROUP: '設備已導入到群組',
      GNAMEEXIST: '該設備群組名已經存在',
      SAVESRSSUCC: '保存設備報表設置成功',
      PLEASECONF: '主題確認後無法修改,請確認',
      ADDPRODSUCC: '添加產品型號成功',
      ADDPARAMSUCC: '添加產品參數成功',
      UPDATEPRODSUCC: '產品更新成功。',
      UPDATERANSUCC: '無線接入網絡更新成功。',
      UPDATEAPNSUCC: 'WiFi AP 網絡更新成功。',
      UPDATEMESHSUCC: 'WiFi Mesh 網絡更新成功。',
      UPDATEPARAMSUCC: '更新產品參數成功',
      PLEASEFILL: '請先填寫*所有項目!',
      UPDATEPERM: '更新權限類型成功!',
      UPDATEPERMDEV: '更新權限設備成功!',
      ADDSUCC: '添加成功',
      UPDATESUCC: '更新成功',
      DONE: '完成',
      WARNING: '警告!',
      PARAMNAMEEXIST: '參數名已存在!',
      SCRITEMPTY: '腳本條目為空!',
      USPGROUPEMPTY: '群組條目為空!',
      GROUPNAMEREQ: '群組名必填',
      OPERMODEREQ: '操作模式必填',
      FLOWIMPORTFAIL: '流程導入失敗',
      SYSSETSUCC: '保存系統設置成功!',
      SYSSETFAIL: '保存系統設置失敗!',
      DEVSETSUCC: '保存設備設置成功!',
      DEVSETFAIL: '保存設備設置失敗!',
      PROSETSUCC: '保存產品設置成功!',
      PROSETFAIL: '保存產品設置失敗!',
      SYSPRESUCC: '保存系統偏好成功!',
      SYSPREFAIL: '保存系統偏好失敗!',
      DELETEUSER: '刪除用戶:',
      CHANGEUSER: '更改用戶:',
      SPFAIL: ' 失敗',
      ACTIVESUSS: '啟用狀態成功',
      ACTIVEFAIL: '啟用狀態失敗',
      IMAGEOVERSIZE: '圖像過大.',
      PWDNOTSAME: '確認密碼不匹配。',
      PWDREQ: '密碼必填.',
      FORMATINCORRECT: '過期日期格式不正確.',
      MUSTADMIN: 'ADMIN/CSR用戶的產品型號必須為"ADMIN".',
      PRODUCTREQ: '產品型號必填.',
      SELECTADMIN: '只有ADMIN角色能選擇"ADMIN".',
      ROLEREQ: '用戶角色必填.',
      AVATARSUCC: '更新頭像成功',
      AVATARFAIL: '更新頭像失敗.',
      EMAILSUCC: '更新郵箱成功',
      EMAILFAIL: '更新郵箱失敗.',
      UPPERLIMIT: '可以添加的設備數量已超過上限!',
      MAXLIMIT: '最大設備上限為:',
      NOTSAVETITLE: '是否保存更改?',
      NOTSAVECONTENT: '您做的更改不會被保存。',
      CONFIRMROLE: '確認刪除角色?',
      DOROLE: '您想刪除該角色嗎?',
      DOSELECTROLE: '您想刪除選定的角色嗎?',
      DELROLESUCC: '角色刪除成功。',
      ROLEUPSUCC: '角色更新成功。',
      ROLEADDSUCC: '角色添加成功。',
      CHANGEWILLBELOSE: "如果不保存更改,則更改將遺失。",
      SYSTEMRETURNLOGIN: "系統即將返回登入頁面",
      SAVEEVENTTIP: '請在此階段儲存之前選擇通知事件！',
      SAVENOTIFYORDEVICEPARAMTIP: '請在此階段保存前添加通知參數或設備參數條件！',
      SAVEDEVICEFAULTTIP: '請在此階段儲存之前新增設備故障參數條件！',
      ADDMEMBERSUC: '新增群組成員成功！',
      ADDMEMBERFAIL: '新增群組成員失敗！',
      SMTPHEALTHY: '測試電子郵件成功。',
      SNMPHEALTHY: '測試 SNMP Trap 成功。',
      XMPPHEALTHY: '測試 XMPP 成功。',
      GENSERVERREPORT: '成功產生伺服器報告。',
      WORKFLOWCLONESUCCESS: '流程尅隆成功！',
      CONFIGURATIONWCLONESUCCESS: '配寘尅隆成功！',
      HASBEENCLONED: '已被尅隆！',
      HASBEENFORKED: '已被分叉了！',
      APNAMEEDITFAIL: '編輯 AP 名稱失敗。',
      ADDAPNSUCC: '新增 WiFi AP 網路成功，並同步創建同名群組。',
      ADDRANSUCC: '新增無線接入網路成功，並同步創建同名群組。',
      ADDMESHSUCC: '新增 WiFi Mesh 網路成功，並同步創建同名群組。',
      TAGERROR: '僅允許0-32個字母、數字、連字符(-)、底線(_)、點(.) 和空格。'
    },
    PM: {
      PMWORD: 'PM',
      PMPARAM: 'PM 參數',
      PMCHART: 'PM 圖',
      PERFORMANCEREPORT: '性能報告',
      PMSTATEITEMS: '所選指標 ID 包含一個或多個追蹤項目！',
      PERFORMANCEREPORT_DESCRIPTION: "生成的伺服器（AMP）報告列表，包含設備和服務的狀態等詳細資訊。",
      PMSTATISTICS: 'PM統計',
      SERIALNUMBER: '序號',
      TARGETSERIALNUMBER: '目標序號',
      TARGETSN: '目標序號',
      TARGETGROUP: "目標群組",
      TARGET: "目標",
      GROUP: '群組',
      PARAMNAME: '參數名稱',
      PARAMPATH: '參數路徑',
      CONDITION: '條件',
      CONDITIONS: '條件',
      PARAMVALUE: '參數值',
      FROM: '從',
      TO: '到',
      CREATEDBY: '創建者',
      BEGINTIME: '開始時間',
      ENDTIME: '結束時間',
      TIME: '時間',
      UPDATETIME: '更新時間',
      MODELNAME: '模型名稱',
      PRODUCTCLASS: '產品類別',
      TIMERANGE: '時間範圍',
      OUI: 'OUI',
      METRICRULE: '度量規則',
      ALL: '全部',
      CONFIRM_DELETE: '確認刪除',
      DO_DELETE: '您確定要刪除所選 ',
      DODELETE: '您確定要刪除 ',
      DELETESUCCESS: '刪除成功',
      DELETEFAIL: '刪除失敗',
      PLESESELECT: '請選擇 ',
      CONFIRM_REFRESH: '確認重新搜索',
      DO_REFRESHSELECT: '您要重新搜索所選的嗎',
      DO_REFRESH: '您要重新搜索嗎',
      REFRESHSUCCESS: '重新搜索成功',
      REFRESHFAIL: '重新搜索失敗',
      EXPIREDUPDATESUCCESS: "所有過期數據已成功更新。",
      DATAEXPIRED: "數據已過期",
      ADDCONDITION: '添加條件',
      DELETECONDITION: '刪除條件',
      SEARCH: '搜尋',
      REFRESH: '刷新',
      REFRESHALL: "全部刷新",
      SEARCHRESULT: '搜尋結果',
      VIEWCHART: '查看圖表',
      CHART: '圖表',
      SAVERULE: '保存規則名稱',
      UPDATERULE: '更新規則名稱',
      NAME: '名稱',
      DESCRIPTION: '描述',
      CLOSE: '關閉',
      SAVE: '保存',
      DELETE: '刪除',
      DELETEALL: '全部刪除',
      GOTDEVICEINFO: '前往設備資訊頁面查看 ',
      VIEWALLCHART: "請選擇設備查看性能圖表。（最多：20）",
      DURATION: "持續時間",
      NOTIFICATIONTOOLTIP: '轉換為通知',
      REFRESHRULETOOLTIP: '重新搜索規則',
      DEVICECOUNT: '設備數量',
      FAIL: '失敗！',
      REFRESHLOADING: '重新搜索加載中...',
      DOWNLOAD: '下載',
      CONFIRM_DOWNLOAD: '確認下載',
      DOWNLOADSUCCESS: '下載成功',
      DOWNLOADFAIL: '下載失敗',
      DO_DOWNLOAD: '您要下載所選裝置嗎？',
      DODOWNLOAD: '您是否要下載 ',
      OPENSEARCHBAR: '開啟搜尋欄',
      HIDESEARCHBAR: '隱藏搜尋欄',
      LASTMACHINGTIME: '上次匹配時間',
      RESEARCH: '重新搜尋',
      RESEARCHRULETOOLTIP: '重新搜尋規則',
      TRACKING: '追蹤',
      RESULT: '結果',
      RESEARCHALL: '重新搜索全部',
      RESULTTOOLTIP: '符合條件的設備數量',
      LASTMACHING: "最後匹配",
    },
    REFURBISHMENT: {
      REFURBISHMENTSTATISTICS: '翻新統計',
      REFURBISHMENTTIME: '翻新時間',
      REFURBISHMENTCOUNT: '翻新計數',
      INSTALLATIONTIME: '安裝時間',
    },
    CARE: {
      TITLE: 'care',
      GENERALINFO: '基本訊息',
      GENERALINFODESCRIPTION: '設備的一般資訊。',
      MAP: '地點',
      MAPDESCRIPTION: '裝置在 Google 地圖上的位置。',
      WIFICLIENTLIST: 'WiFi 用戶端列表',
      WIFICLIENTLISTDESCRIPTION: 'WiFi 用戶端連接到裝置。',
      ERRORSTATUS: '錯誤狀態',
      ERRORSTATUSDESCRIPTION: '設備的錯誤列表，包括事件代碼、錯誤描述和錯誤時間。',
      ERRORSTCARE: "取得設備",
      SELECTDEVICE: "選擇設備",
      SERIALNUMBER: "序號",
      PRODUCTNAME: "產品名稱",
    },
    FIVEGC: {
      CELL_CONNECTED: "基站已連接",
      CELL_CONNECTED_DESCRIPTION: "顯示已連接和正常運作的無線電數量。",
      CELL_DISCONNECTED: "基站已斷開",
      CELL_DISCONNECTED_DESCRIPTION: "顯示斷開連接的無線電數量。",
      ACTIVE_UE: '活動用戶',
      ACTIVE_UE_DESCRIPTION: '活動用戶的基站數量。',
      NO_ACTIVE_UE: '沒有活動用戶',
      NO_ACTIVE_UE_DESCRIPTION: '顯示無活動的基站數量。這不一定意味著沒有用戶設備連接；他們可能已連接但未處於活動狀態。',
      CELLS_WITHOUT_ATTACHED_UE: '無用戶設備連接的基站',
      CELLS_WITHOUT_ATTACHED_UE_DESCRIPTION: '顯示已連接但未有用戶設備連接的無線電數量。',
      CELLS_WITH_ACTIVE_UE: "有活躍用戶的基站",
      CELLS_WITH_ACTIVE_UE_DESCRIPTION: "此 widget 以長條圖形式顯示基站活動狀態，包括無活動、1到5個活躍用戶、6到10個活躍用戶、11到20個活躍用戶和20個或更多活躍用戶。",
      CELLS_LIST: "基站列表",
      CELLS_LIST_DESCRIPTION: "此 widget 顯示連接至 5G Core 的基站列表。",
      ALARM_LIST: "警報列表",
      ALARM_LIST_DESCRIPTION: "警報列表提供系統中各種警報的信息。",
      FIVECORENETWORK: "5G 核心網絡",
      FIVECORENETWORK_DESCRIPTION: "此 widget 作為連結到 5G Core，在系統/設置中配置了 5G Core URL。",
      CELL_THROUGHPUT: "基站吞吐量",
      CELL_THROUGHPUT_DESCRIPTION: "此 widget 顯示基站當前的下載和上傳吞吐量。",
      UE_THROUGHPUT_BY_CELL: "小區用戶吞吐量",
      UE_THROUGHPUT_BY_CELL_DESCRIPTION: "小區用戶吞吐量顯示連接到特定小區的所有使用者設備（UE）的總下載和上傳吞吐量，提供其數據傳輸效能的概覽。",
      UE_LIST: "UE列表",
      UE_LIST_DESCRIPTION: "UE資訊提供了關於連接到5G核心網路的使用者設備（UE）的詳細資訊。",
      UE_5QI_PACKET: "用戶設備 5QI 數據包",
      UE_5QI_PACKET_DESCRIPTION: "此 widget 顯示用戶設備的 5QI 數據包，包括上行和下行的各種流量指標和丟包率。",
      ACTIVE: '啟用',
      INACTIVE: '未啟用',
      STATUS: '狀態',
      NAME: '名稱',
      STATE: '狀態',
      gNBID: 'gNB ID',
      BAND: '頻段',
      SESSIONS: '會話數',
      DATA: '資料',
      PLMN: 'PLMN',
      TAC: 'TAC',
      IP: 'IP',
      SEVERITY: '嚴重程度',
      ALARM_ID: '警示ID',
      EVENT_TYPE: '事件類型',
      EVENT_TIME: '事件時間',
      PROBABLE_CAUSE: '可能原因',
      SPECIFIC_PROBLEM: '具體問題',
      OBJ_CLASS: "對象類別",
      ADD_TEXT: "添加文本",
      THROUGHPUT_HISTORY: "吞吐量歷史",
      STATUS_HISTORY: "狀態歷史",
      HISTORY: '歷史',
      IMSI: 'IMSI',
      PHONENUMBER: "電話號碼",
      IMEI: 'IMEI',
      SUB_TYPE: '子類型',
      REG_TYPE: "註冊類型",
      LOCAL_ATTACHMENT: '本地附著',
      LAST_ACTIVITY_TIME: "最後活動時間",
      REGISTRATION_TIME: "註冊時間",
      DEREGISTRATION_TIME: "註銷時間",
      UL_THROUGHPUT: '上行吞吐量',
      DL_THROUGHPUT: '下行吞吐量',
      SUPI: 'SUPI',
      FIVEQI: '5QI',
      UL_INGRESS: '上行入口',
      UL_EGRESS: '上行出口',
      UL_DROPPED: '上行丟包',
      UL_TOTAL_INGRESS: '上行總入口',
      UL_TOTAL_EGRESS: '上行總出口',
      UL_TOTAL_DROPPED: '上行總丟包',
      DL_INGRESS: '下行入口',
      DL_EGRESS: '下行出口',
      DL_DROPPED: '下行丟包',
      DL_TOTAL_INGRESS: '下行總入口',
      DL_TOTAL_EGRESS: '下行總出口',
      DL_TOTAL_DROPPED: '下行總丟包',
      ALARM: '告警',
      ALARM_DESCRIPTION: '即時顯示來自5G核心網絡的最新告警，包括告警嚴重性、時間戳和簡要描述，便於快速識別和回應網絡問題。',
      ue_activity: '5GC 的 UE 活動',
      ue_activity_DESCRIPTION: '此圓餅圖顯示用戶設備（UE）的活動狀態。',
      ue_presence: '5GC 的 UE 存在狀態',
      ue_presence_DESCRIPTION: '顯示已連接（已連接）和未連接（已斷開）的 UE 數量。',
      cells_info: '5GC 的基站',
      cells_info_DESCRIPTION: '5GC 基站提供有關連接到 5GC 網絡的基站的詳細信息。',
      ACTIVITY_DESCRIPTION: {
        ACTIVE: '活動段顯示活動中的已連接 UE 數量。',
        DATA: '數據段顯示系統中的數據會話數量。一個 UE 可能有多個數據會話。',
        CALLS: '通話段顯示目前正在通話中的已連接 UE 數量。',
        INACTIVE: '非活動段顯示未活動的已連接 UE 數量。'
      },
      PRESENCE_DESCRIPTION: {
        ATTACHED: '“已連接”狀態表示用戶設備已成功連接到網絡。',
        DETACHED: '“已斷開”狀態表示用戶設備已從網絡斷開或尚未連接到網絡。'
      },
      LICENSE_DESCRIPTION: {
        UE: '顯示使用中的 UE 授權座位數量和可用的 UE 授權座位數量。',
        NETWORK: '顯示 PDN 授權座位數量和可用的 PDN 授權座位數量。',
        CELLS_4G: '使用中的 4G 基站授權座位數量和可用的 4G 基站授權座位總數。',
        CELLS_5G: '使用中的 5G 基站授權座位數量和可用的 5G 基站授權座位總數。',
      },
      ACTIVITY_CHART_DESCRIPTION: {
        ACTIVE_20PLUS: "表示擁有20個或更多活躍用戶的基站數量。",
        ACTIVE_11To20: "表示擁有11到20個活躍用戶的基站數量。",
        ACTIVE_6To10: "表示擁有6到10個活躍用戶的基站數量。",
        ACTIVE_1To5: "表示擁有1到5個活躍用戶的基站數量。",
        NOACTIVE: "表示沒有活動的基站。這並不一定意味著沒有用戶連接；他們可能已連接但未活躍。"
      },
      ACTION: {
        RESTARTSYSTEM: '重新啟動系統',
        DORESTARTSYSTEM: '如果繼續，所有服務將暫時丟失！',
        RESTARTSYSTEM_SUCESS: '系統重新啟動成功！',
        RESTARTSYSTEM_FAIL: '系統重新啟動失敗！',
        BACKUPCONFIGURATION: '備份配置',
        BACKUPCONFIGURATION_SUCESS: '配置備份成功！',
        BACKUPCONFIGURATION_FAIL: '配置備份失敗！',
        RESTORECONFIGURATION: '恢復配置',
        RESTORECONFIGURATION_SUCESS: '配置恢復成功！',
        RESTORECONFIGURATION_FAIL: '配置恢復失敗！',
        FACTORYRESET: '恢復出廠設置',
        DOFACTORYRESET: '您即將重置您的配置為出廠默認設置！',
        FACTORYRESET_SUCESS: '恢復出廠設置成功！',
        FACTORYRESET_FAIL: '恢復出廠設置失敗！',
        REFRESHALL: "全部刷新",
        REFRESHALL_SUCESS: "全部刷新成功！",
        REFRESHALL_FAIL: "全部刷新失敗！",
        SYSTEMMANAGEMENT: "系統管理",
      },
    },
    POWER: {
      ENERGYSAVING: '能源管理',
      STARTTIME_MUST_BE_EARLIER: "開始時間必須早於結束時間",
      STARTDATE_MUST_BE_EARLIER: "開始日期不能晚於結束日期。",
      POWER_CONSUMPTION_SUMMARY: "電力消耗總覽",
      REAL_AVG_ENERGY: "實際平均能耗",
      NORMAL_STATE_ENERGY: "Normal 狀態能耗",
      ENERGY_CONSUMPTION_BY_POLICY: "依據策略的能耗",
      POWER_CONSUMPTION: "電力消耗",
      TX_POWER: "發射功率",
      NETWORK_USAGE: "網路使用量",
      UPLOAD: "上傳",
      DOWNLOAD: "下載",
      UE_COUNT: "連線裝置數",
      LOCATION: "位置",
      CURRENT_POLICY: "目前策略",
      POLICY_SETTING: "策略設定",
      ENERGY_POLICY: "節能策略",
      MILD_SLEEP_DESCRIPTION: "在保持裝置全功能運作的同時降低發射功率。",
      MODERATE_SLEEP_DESCRIPTION: "降低耗電並暫時關閉無線電以節省更多能源。",
      WAKEABLE_DEEPSLEEP_DESCRIPTION: "逐步關閉裝置電源，但在需要時可自動喚醒。",
      DEEPSLEEP_DESCRIPTION: "完全關閉裝置，需手動喚醒才能恢復運作。",
      SCHEDULE_SETTING: "排程設定",
      POLICY_LIST: "策略清單",
      DURATION: "時長",
      ENERGY: "耗電量",
      POWER_CONSUMPTION_PER_DEVICE: "每台設備的電力消耗",
      DL_PRB_LOADING: "下行 PRB 使用率",
      UL_PRB_LOADING: "上行 PRB 使用率",
      TOTAL_POWER_CONSUMPTION: "總電力消耗",
      NAME: "名稱",
      SCHEDULE: "排程",
      CONDITION: "條件",
      ACTIVE: "啟用中",
      NO_POLICIES_FOUND: "未找到任何策略。",
      NO_POLICY_CONFIGURED: "此裝置尚未設定節能策略。",
      ENABLE_TO_ADD_POLICIES: "啟用節能控制後即可新增策略。",
      ENERGY_SAVING_NOT_ENABLED: "尚未啟用節能控制。",
      NO_POLICY_SETTINGS_AVAILABLE: "此裝置無可用的策略設定。",
      ENABLE_TO_CONFIGURE_POLICY: "請啟用節能控制以設定或檢視策略內容。",
      ENERGY_MODE: "節能模式",
      TRAFFIC_LOADING: "流量負載",
      UE_CONTEXT: "UE 連線上下文",
      POLICY_DISABLE_TITLE: "停用省電模式？",
      POLICY_DISABLE_TEXT: "停用後，裝置將恢復一般耗電設定，是否確定要關閉？",
    }
  }
}
