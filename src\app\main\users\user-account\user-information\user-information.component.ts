import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewEncapsulation, Input, ViewChild, AfterViewChecked, ChangeDetectorRef,ElementRef } from '@angular/core';
import { AuthenticationService } from 'app/auth/service';
import { AccountsService } from '../user-account.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { ImageCompressionUploadService } from 'app/main/commonService/image-compression-upload.service';
import { TranslateService } from '@ngx-translate/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { GridSystemService } from 'app/main/commonService/grid-system.service';

@UntilDestroy()
@Component({
  selector: 'app-user-information',
  templateUrl: './user-information.component.html',
  styleUrls: ['./user-information.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class UserInformationComponent extends Unsubscribe implements OnInit, AfterViewChecked {
  @ViewChild('modalOTPurlInformation') modalOTPurlInformation;
  @ViewChild('qrcodeInput') qrcodeInput: ElementRef;
  @Input() accessor: any;
  public data: any = {};
  public emailData = {
    writable: true,
    email: ''
  }
  public loading: boolean = false;
  public allowedFileTypes: string = '.png, .jpeg, .jpg, .gif';
  private maxImgSize = 100 * 1024;
  public mfaData: any = {
    mfaEnabled: false
  }
  public otpUrl: any;
  public currentQRStep = 1;
  public currUserIsAdmin: boolean = false;
  public currUserRole: any;
  public enterCodeString: string;
  public error: any;
  public componentWidth: any;
  constructor(
    private _authenticationService: AuthenticationService,
    private _accountsService: AccountsService,
    private _httpClient: HttpClient,
    private toastr: ToastrUtilsService,
    private translateService: TranslateService,
    private _imageCompressionUploadService: ImageCompressionUploadService,
    private modalService: NgbModal,
    private _gridSystemService: GridSystemService,
    private cdr: ChangeDetectorRef,
  ) {
    super();
    this.data = _authenticationService.currentUserValue;
    if (!this.data.avatar) {
      this.data.avatar = ''
    }
    this.emailData.email = this.data.email
    this.currUserIsAdmin = this.data.isAdmin;
    this.currUserRole = this.data.role;
    this.mfaData.mfaEnabled = this._authenticationService.mfaEnabled;
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
      this.ERROR = this.translateService.instant('DEVICES.ACTION.ERROR');
      this.WARNING = this.translateService.instant('CONFIRM.WARNING');
      this.IMAGEOVERSIZE = this.translateService.instant('CONFIRM.IMAGEOVERSIZE');
      this.ROLEREQ = this.translateService.instant('CONFIRM.ROLEREQ');
      this.AVATARSUCC = this.translateService.instant('CONFIRM.AVATARSUCC');
      this.AVATARFAIL = this.translateService.instant('CONFIRM.AVATARFAIL');
      this.EMAILSUCC = this.translateService.instant('CONFIRM.EMAILSUCC');
      this.EMAILFAIL = this.translateService.instant('CONFIRM.EMAILFAIL');
      this.INVALIDFILETYPE = this.translateService.instant('COMMON.INVALIDFILETYPE');
    })
  }

  public SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
  public ERROR = this.translateService.instant('DEVICES.ACTION.ERROR');
  public WARNING = this.translateService.instant('CONFIRM.WARNING');
  public IMAGEOVERSIZE = this.translateService.instant('CONFIRM.IMAGEOVERSIZE');
  public ROLEREQ = this.translateService.instant('CONFIRM.ROLEREQ');
  public AVATARSUCC = this.translateService.instant('CONFIRM.AVATARSUCC');
  public AVATARFAIL = this.translateService.instant('CONFIRM.AVATARFAIL');
  public EMAILSUCC = this.translateService.instant('CONFIRM.EMAILSUCC');
  public EMAILFAIL = this.translateService.instant('CONFIRM.EMAILFAIL');
  public INVALIDFILETYPE = this.translateService.instant('COMMON.INVALIDFILETYPE');

  /**
 * Upload Image
 *
 * @param event
 */
  uploadImage(event: any) {
    if (event.target.files && event.target.files[0]) {
      this.loading = true;
      let file = event.target.files[0];
      const allowedTypesArray = this.allowedFileTypes.split(',').map(t => t.trim());
      if (!allowedTypesArray.includes(file.type)) {
        // const allowTypes = this.allowedFileTypes.replace(/image\//g, '');
        this.toastr.showWarningMessage(this.WARNING, `${this.INVALIDFILETYPE}${allowTypes}.`);
        event.target.value = '';
        return;
      }
      this.checkImageSize(file).then(res => this.saveAvatar(res), err => {
        this.toastr.showWarningMessage(this.WARNING, this.IMAGEOVERSIZE);
        this.loading = false;
      })
    }
  }

  checkImageSize(file, compress = true): Promise<any> {
    if (file.size > this.maxImgSize) {
      if (file.type !== 'image/gif' && compress) {
        return this._imageCompressionUploadService.compressionFile(file, 0.7).then(res => this.checkImageSize(res, false))
      } else {
        return Promise.reject()
      }
    } else {
      return this._imageCompressionUploadService.fileToDataURL(file)
    }
  }

  saveAvatar(file) {
    this.updateAvatar(file).then(dataUrl => {
      if (dataUrl) {
        this.data.avatar = dataUrl;
        this._authenticationService.avatar = dataUrl;
        this._accountsService.updateCurUser({
          name: this.data.userName,
          avatar: dataUrl
        });
        this.toastr.showSuccessMessage(this.SUCCESS, this.AVATARSUCC);
      } else {
        this.toastr.showErrorMessage(this.ERROR, this.AVATARFAIL);
      }
    }, err => {
      this.toastr.showErrorMessage(this.ERROR, err?.error || this.AVATARFAIL);
    }).finally(() => this.loading = false);
  }

  deleteAvatar() {
    this.updateAvatar(null).then(() => {
      this.data.avatar = null;
      this._authenticationService.avatar = null;
      this._accountsService.updateCurUser({
        name: this.data.userName,
        avatar: null
      });
      this.toastr.showSuccessMessage(this.SUCCESS, this.AVATARSUCC);
    }, () => {
      this.loading = false;
      this.toastr.showErrorMessage(this.ERROR, this.AVATARFAIL);
    });
  }

  updateAvatar(dataUrl) {
    return new Promise<string>((resolve, reject) => {
      let update: any = {
        avatar: dataUrl
      }
      this._httpClient.put('nbi/common/user', update, { 'responseType': 'text' }).pipe(untilDestroyed(this)).subscribe({
        next: (response: any) => {
          resolve(response ? dataUrl : null);
        },
        error: reject
      });
    });
  }

  saveEmail(e) {
    let source = e.source;
    let value = e.value;
    new Promise<string>((resolve, reject) => {
      let update: any = {
        email: value
      }
      this._httpClient.put('nbi/common/user', update, { 'responseType': 'text' }).pipe(untilDestroyed(this)).subscribe({
        next: (response: any) => {
          resolve(response ? value : null);
        },
        error: reject
      });
    })
      .then(res => {
        this.emailData.email = value
        source.value = value
        source.isEdit = false
        source.isEditValue = false
        this._accountsService.updateCurUser({
          name: this.data.userName,
          email: value
        });
        this._authenticationService.email = value;
        this.toastr.showSuccessMessage(this.SUCCESS, this.EMAILSUCC);
      })
      .catch(err => {
        this.toastr.showErrorMessage(this.ERROR, this.EMAILFAIL);
      })
  }

  /**
    * openModal
    * @param modalChangePassword
    */
  openChangePasswordModal(modalChangePassword) {
    this.modalService.open(modalChangePassword, {
      backdrop: 'static',
      size: 'sm'
    });
  }

  mfaEnabledChange(e) {
    if (this.mfaData.mfaEnabled) {
      this._authenticationService.getQRCode().then((res: any) => {
        this.otpUrl = res
        this.currentQRStep = 1
        this.modalService.open(this.modalOTPurlInformation, {
          backdrop: 'static',
          size: 'sm',
          modalDialogClass: 'modal-twenty',
          scrollable: true,
          centered: true
        });
      })
    } else {
      this.mfaEnabledControl(false)
    }
  }

  next() {
    this.currentQRStep = 2
  }

  verify() {
    this._authenticationService.loginWithMFA({ "user": this.data.userName, "passCode": this.enterCodeString }).then(res => {
      if (res == 'Success.') {
        this.mfaEnabledControl(true)
      } else {
        this.mfaData.mfaEnabled = false
        this.error = res
      }
    }).catch(err => {
      this.error = 'Your code does not match our recodes. Please try again.'
      this.mfaData.mfaEnabled = false
    })
  }

  mfaEnabledControl(mfaEnabled) {
    return new Promise((resolve, reject) => {
      this._httpClient.put('nbi/common/user', { "mfaEnabled": mfaEnabled }).pipe(untilDestroyed(this)).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    }).then((res: any) => {
      this.toastr.showSuccessMessage(this.SUCCESS, '');
      this.mfaData.mfaEnabled = mfaEnabled
      this._authenticationService.mfaEnabled = mfaEnabled
      this._accountsService.mfaEnabled = mfaEnabled
      this._accountsService.refresh = true
      this._accountsService.updateCurUser({
        name: this.data.userName,
        mfaEnabled: mfaEnabled
      });
      this.modalService.dismissAll()
    }, err => {
      this.mfaData.mfaEnabled = false
      this.toastr.showErrorMessage(this.ERROR, err.error);
    });
  }

  isNumberKey(event) {
    // 检查按下的键是否是 Enter
    if (event.key === 'Enter') {
      this.verify();  // 调用 verify() 方法
    }
    // 获取字符编码
    const charCode = event.which ? event.which : event.keyCode;
    // 检查是否为数字或退格键
    const isNumeric = (charCode >= 48 && charCode <= 57) ||
      (charCode >= 96 && charCode <= 105) ||
      charCode === 8;
    // 如果不是数字或退格键，阻止默认行为
    if (!isNumeric) {
      event.preventDefault();
    }
  }
  getColumnWidth() {
    if (this.componentWidth > 0) {
      let coefficient = this.componentWidth / 335; // widget寬度/每項預留的寬度
      let maxColumns = Math.min(Math.floor(coefficient), 3); // 最大列數vs.資料數=>取最小的
      return `${100 / maxColumns}%`;
    }
  }
  verifyDsiable() {
    return !this.enterCodeString || this.enterCodeString.length < 6;
  }

  ngAfterViewChecked() {
    if (this._accountsService.refresh && !this._authenticationService.mfaEnabled) {
      this.mfaData.mfaEnabled = this._accountsService.mfaEnabled
      this._accountsService.refresh = false
      this.cdr.detectChanges()
    }
  }

  ngAfterViewInit(): void {
    this.customSubscribe(this._gridSystemService.onGridsterItemComponentInfoChanged, res => {
      this.componentWidth = res['UsersModule/UserProfileComponent'] ? res['UsersModule/UserProfileComponent']['width'] : 0
      // console.log(this.componentWidth)
    })
    this.qrcodeInput.nativeElement.focus(); // 在视图初始化后聚焦输入框
  }

  ngOnInit() {
    this.error = ''
    this.enterCodeString = ''
  }

  ngOnDestroy(): void {
    this._accountsService.refresh = true
  }
}
