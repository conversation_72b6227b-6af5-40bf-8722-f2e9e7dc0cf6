export const locale = {
  lang: 'fr',
  data: {
    MENU: {
      HOME: 'Accueil',
      SAMPLE: 'Exemple'
    },
    DASHBOARD: {
      TITLE: 'Tableau de bord',
      AVGSESSIONS: {
        TITLE: 'Sessions moyennes',
        VIEWDETAILS: 'Voir les détails',
        LAST1DAY: 'Dernières 24 heures',
        LAST7DAYS: '7 derniers jours',
        LAST15DAYS: '15 derniers jours',
        LAST28DAYS: '28 derniers jours',
        LAST30DAYS: '30 derniers jours',
        LASTMONTH: 'Le mois dernier',
        LASTYEAR: 'L’année dernière',
      },
      MAP: 'Emplacement',
      SESSIONDURATION: 'Durée de session',
      SESSIONDURATIONDESCRIPTION: "Graphique historique de la durée moyenne des sessions pour tous les appareils à intervalles réguliers. Durée de session : Le temps total passé dans une session entre l'appareil et AMP.",
      SESSIONRATE: 'Taux de session',
      SESSIONRATEDESCRIPTION: "Graphique historique de la fréquence des requêtes CWMP pour les appareils en ligne.",
      LATENCY: 'Latence des requêtes',
      LATENCYDESCRIPTION: "Graphique historique de la latence moyenne des requêtes pour tous les appareils à intervalles réguliers. Latence des requêtes : Le temps total passé dans une requête entre l'appareil et AMP.",
      REGISTERED_COUNT_DISTRIBUTION: 'Appareils enregistrés',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution des appareils enregistrés pour chaque produit.',
      PROVISIONINGTYPEDISTRIBUTION: "Répartition du type de fourniture",
      PROVISIONINGTYPEDISTRIBUTION_DESCRIPTION: "Répartition de tous les types de configuration sélectionnés pour chaque produit enregistré.",
      ONLINE_COUNT_DISTRIBUTION: 'Appareils en ligne',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: "Graphique de distribution du nombre d'appareils en ligne pour chaque produit.",
      HISTORYONLINEDEVICE: 'Appareils en ligne',
      ONLINEDEVICEDESCRIPTION: "Graphique historique du nombre d'appareils en ligne pour chaque produit.",
      SOFTWARE_VERSION_DISTRIBUTION: 'Version du logiciel',
      SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution des versions de logiciels pour les appareils en ligne.',
      PROVISIONING_CODE_DISTRIBUTION: 'Code de provisioning',
      PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution du code de provisioning pour les appareils en ligne.',
      XMPP_STATUS_DISTRIBUTION: 'Statut XMPP',
      XMPP_STATUS_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution du statut XMPP pour les appareils en ligne.',
      IMS_STATUS_DISTRIBUTION: 'Distribution du statut IMS',
      IMS_STATUS_DISTRIBUTION_DESCRIPTION: "Graphique de distribution du statut d'enregistrement IMS pour les appareils en ligne.",
      SIM_STATUS_DISTRIBUTION: 'Statut SIM',
      SIM_STATUS_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution du statut de connexion SIM pour les appareils en ligne.',
      IPSEC_STATUS_DISTRIBUTION: 'Statut IPSec',
      IPSEC_STATUS_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution du statut de connexion du tunnel IPSec pour les appareils en ligne.',
      TOTAL: 'Total',
      ONLINE: 'En ligne',
      ONLINE_DEVICE: 'Appareils en ligne',
      ONLINE_DEVICE_DESCRIPTION: 'Nombre total d’appareils en ligne.',
      GROUPS_COUNT: 'Groupes',
      GROUPS_COUNT_DESCRIPTION: 'Nombre total de groupes.',
      ONLINE_USERS: 'Utilisateurs en ligne',
      ONLINE_USERS_DESCRIPTION: 'Nombre total d’utilisateurs connectés au cours de la dernière demi-heure.',
      UE_COUNT: 'UE',
      UE_COUNT_DESCRIPTION: "Nombre total d'équipements utilisateurs (UE) connectés à la petite cellule.",
      ALARMS_TOTAL: 'Alarmes totales',
      ALARMS_TOTAL_DESCRIPTION: "Nombre total d'alarmes signalées par l'appareil.",
      ALARMS_SERVERITY: 'Alarmes',
      ALARMS_SERVERITY_DESCRIPTION: "Nombre total d'alarmes avec différentes sévérités telles que Critique, Majeure, Mineure et Avertissement.",
      GROUP_LIST: 'Liste des groupes',
      GROUP_LIST_DESCRIPTION: "Liste des groupes créés avec des informations détaillées telles que le nombre d'appareils et les alarmes avec différentes sévérités.",
      COVERMAP: 'Carte de couverture',
      COVERMAP_DESCRIPTION: 'Affiche les emplacements et la couverture radio des appareils du groupe.',
      ALARM_LIST: 'Liste des alarmes',
      ALARM_LIST_DESCRIPTION: "Liste de toutes les alarmes signalées par l'appareil, y compris les alarmes effacées et non effacées, avec la sévérité, l'heure de l'événement et la cause probable.",
      SYSTEM_EVENT_LIST: 'Événements système',
      SYSTEM_EVENT_LIST_DESCRIPTION: "Liste de tous les événements enregistrés liés aux échecs de communication et d'accès.",
      STSTEM_INFORMATIONS: 'Informations système',
      STSTEM_INFORMATIONS_DESCRIPTION: 'Informations système AMP ou contenu du rapport du serveur.',
      TOTAL_CLIENTS: 'Distribution du RSSI WiFi total',
      TOTAL_CLIENTS_DESCRIPTION: 'Graphique de distribution des clients WiFi avec différents niveaux de RSSI.',
      TOTAL_CLIENTS_COUNT: 'Clients WiFi',
      TOTAL_CLIENTS_COUNT_DESCRIPTION: 'Nombre total de clients WiFi connectés aux points d’accès WiFi.',
      EXCELLENT_CLIENTS: 'Excellent',
      GOOD_CLIENTS: 'Bon',
      POOR_CLIENTS: 'Mauvais',
      STATISTICSOFTOTALCLIENTS: 'Enregistrements de distribution RSSI WiFi total',
      STATISTICSOFTOTALCLIENTS_DESCRIPTION: 'Graphique historique des clients WiFi avec différents niveaux de RSSI.',
      GROUPNAME: 'Nom du groupe',
      PRODUCTNAME: 'Nom du produit',
      MANAGEMENTSCOPE: 'Portée de la gestion',
      REGION: 'Région',
      LOCATION: 'Emplacement',
      CONFIGURATION: 'Configuration',
      APS: 'Total des points d’accès',
      TOTALCLIENTS: 'Total des clients',
      EXCELLENTCLIENTS: 'Clients excellents',
      GOODCLIENTS: 'Clients bons',
      POORCLIENTS: 'Clients mauvais',
      EXCELLENT: 'Excellent',
      GOOD: 'Bon',
      POOR: 'Mauvais',
      EXCELLENT_DESCRIPTION: 'RSSI > -65dBm',
      GOOD_DESCRIPTION: '-65dBm < RSSI < -80dBm',
      POOR_DESCRIPTION: 'RSSI < -80dBm',
      TOTALCLIENTSTABLE: 'Table des clients',
      CLIENTS: 'Informations sur les clients WiFi',
      ONLINEAPS: 'Points d’accès en ligne',
      TAGS: 'Balises',
      GROUPSLOCATION: 'Emplacement des groupes',
      GROUPSLOCATION_DESCRIPTION: "Affiche les emplacements et les informations de base de tous les groupes sur la carte.",
      DEVICESLOCATION: "Emplacement des appareils",
      DEVICESLOCATION_DESCRIPTION: "Afficher les emplacements et les informations de base de tous les appareils sur la carte.",
    },
    DEVICES: {
      WIFICLIENT: 'Clients WiFi',
      WIFIAPNAME: 'Nom du point d\'accès WiFi',
      WIFIAPROLE: 'Rôle du point d\'accès WiFi',
      WIFIAPCONFVERSION: 'Version de config. du point d\'accès WiFi',
      WIFIAPNCONFVERSION: 'Version de config. WiFi APN',
      TAGS: 'Balises',
      LIST: 'Liste des appareils',
      SERIAL_NUMBER: 'Numéro de série',
      MODEL_NAME: 'Nom du modèle',
      FIRMWARE: 'Firmware',
      LABEL: 'Étiquette',
      GROUP: 'Groupe',
      PRODUCT: 'Produit',
      LAST_CONNECTED: 'Dernière connexion',
      LAST_EVENT: 'Dernier événement',
      UPTIME: 'Temps de fonctionnement',
      TIME_ZONE: 'Fuseau horaire',
      ACTIVE: 'Actif',
      BAND: 'Bande',
      CHANNEL: 'Canal',
      BANDWIDTH: 'Bande passante',
      UTILIZATION: 'Utilisation',
      RECEIVED: 'Reçu',
      SENT: 'Envoyé',
      DOWNLINK_RATE: 'Débit de liaison descendante',
      UPLINK_RATE: 'Débit de liaison montante',
      MODE: 'Mode',
      CONNECTTIME: 'Temps de connexion',
      ERRORCODE: "Code d'erreur",
      ERRORDESCRIPT: "Description de l'erreur",
      ERRORTIME: "Heure de l'erreur",
      DAILYSENT: 'Quotidien envoyé',
      DAILYRECEIVED: 'Quotidien reçu',
      ACCESSCOUNT: "Nombre d'accès",
      UNINSTALLEDTIME: 'Heure de désinstallation',
      DATASIZE: 'Taille des données',
      CACHESIZE: 'Taille du cache',
      SERVICEDISCOVERYSERVER: 'Serveur de découverte de service',
      ACTICE_BCG_SERVERS: 'Serveurs BCG actifs',
      POWERCONSUMPTION: "Consommation d'énergie",
      STATE: 'État',
      CONTAINERVERSION: 'Version du conteneur',
      APPLICATIONVERSION: "Version de l'application",
      ENABLE: 'Activer',
      CELL_RESERVED_FOR_OPERATOR_USE: "Cellule réservée à l'usage de l'opérateur",
      EUTRA_CARRIER_ARFCN: 'ARFCN de la porteuse EUTRA',
      BLACKLISTED: 'Sur liste noire',
      VENDORCLASSID: 'ID de classe du vendeur',
      EARFCNDOWNLOAD: 'Téléchargement EARFCN',
      DOWNLOADBANDWIDTH: 'Bande passante de téléchargement',
      UPLOADBANDWIDTH: "Bande passante d'upload",
      REFERENCESIGNALPOWER: 'Puissance du signal de référence',
      SECURITY: 'Sécurité',
      SEVERITY: 'Gravité',
      ALARMID: "ID d'alarme",
      EVENTTYPE: "Type d'événement",
      EVENTTIME: "Heure de l'événement",
      PROBABLECAUSE: 'Cause probable',
      SPECIFICPROBLEM: 'Problème spécifique',
      ACKUSER: 'Utilisateur ACK',
      ACKTIME: 'Heure ACK',
      ADDITIONALTEXT: 'Texte supplémentaire',
      ADDITIONALINFORMATION: 'Informations supplémentaires',
      PEER: 'Pair',
      DURATION: 'Durée',
      CONNECT: 'Connecter',
      START: 'Démarrer',
      END: 'Fin',
      UPLOAD: 'Téléverser ',
      DOWNLOAD: 'Télécharger',
      DOWNLOADDATAMODEL: 'Télécharger le modèle de données complet',
      DOWNLOADALL: 'Tout télécharger',
      DOWNLOADSELECT: 'Télécharger le contenu sélectionné',
      TIME: 'Temps',
      UPLOADRESULT: 'Résultat du test de téléchargement',
      DOWNLOADRESULT: 'Résultat du test de téléchargement',
      EVENT: 'Événement',
      LOGLEVEL: 'Niveau de journalisation',
      REQUEST: 'Demandes',
      CREATED: 'Créé',
      IMEI: 'IMEI',
      MAC: 'MAC',
      IP: 'IP',
      RSSI: 'RSSI',
      SSID: 'SSID',
      BSSID: 'BSSID',
      APSN: 'AP S/N',
      APNAME: 'AP Name',
      APMAC: 'AP MAC',
      APIP: 'AP IP',
      LISTDESCRIPTION: "Liste tous les dispositifs autorisés avec des informations communes telles que le numéro de série, le MAP et l'adresse IP.",
      SMALLCELL_LIST: 'Liste des petites cellules',
      SMALLCELL_LISTDESCRIPTION: "Liste toutes les petites cellules autorisées appartenant au réseau d'accès radio avec des informations spécifiques telles que UEs, PCI et GNB ID.",
      WIFI_AP_LIST: 'Liste des AP WiFi',
      WIFI_AP_LISTDESCRIPTION: "Liste tous les AP WiFi autorisés appartenant au réseau AP WiFi avec des informations spécifiques telles que les clients, le canal et l'utilisation du canal.",
      WIFI_MESH_LIST: 'Liste des mailles WiFi',
      WIFI_MESH_LISTDESCRIPTION: "Liste tous les AP mailles WiFi autorisés appartenant au réseau mailles WiFi avec des informations spécifiques telles que les clients, le canal et l'utilisation du canal.",
      CURRENTNUMBERS: 'Nombres d’alarme actuels.',
      ALARMMGMTDESCRIPTION: "Liste toutes les alarmes signalées par le dispositif, y compris celles réglées, non réglées avec gravité, heure de l'événement et cause probable.",
      REGISTERDEVICE: 'Enregistrer le dispositif',
      REGISTERSMALLCELLDEVICE: 'Enregistrer le dispositif de petite cellule',
      REGISTERAPDEVICE: 'Enregistrer le dispositif AP WiFi',
      REGISTERNESHDEVICE: 'Enregistrer le dispositif mailles WiFi',
      LIVEUPDATE: 'Mise à jour en direct',
      SPEEDTEST: 'Test de vitesse',
      SPEEDTESTDESCRIPTION: "L'appareil utilise TR-143 pour effectuer des tests de vitesse en téléchargement et en téléversement, mesurant les performances du réseau pour garantir des taux de transfert optimaux.",
      FIVECORE: 'Cœur 5G',
      FIVECORENETWORK: 'Réseau central 5G',
      FIVECORENETWORK_DESCRIPTION: "Fournir un lien vers le cœur 5G, configuré dans le système/réglages avec l'URL du cœur 5G.",
      CELL_THROUGHPUT: 'Débit de cellule',
      CELL_THROUGHPUT_DESCRIPTION: "Informations sur le débit de téléchargement et de téléversement des petites cellules.",
      UE_LIST: 'Liste des UE',
      UE_LIST_DESCRIPTION: "UE (équipement utilisateur) connecté et déconnecté au réseau central 5G avec des informations détaillées telles que l'état, IMSI, IMEI, GNB ID et adresse IP.",
      UE_5QI_PACKET: 'Paquet UE 5QI',
      UE_5QI_PACKET_DESCRIPTION: 'Liste des paquets 5QI de l’UE, y compris diverses métriques de trafic et taux de perte pour le lien montant et descendant.',
      BULKDATAPROFILE_DESCRIPTION: "Liste des profils de données en masse avec des informations détaillées telles que l'alias, le statut, l'URL, les paramètres et le type codé.",
      SOFTWAREMODULES_DESCRIPTION: "Liste des modules logiciels avec des informations détaillées telles que UUID, alias, nom, URL et date de dernière mise à jour.",
      ONLINE_COUNT_DISTRIBUTION: 'Dispositifs en ligne',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution des comptes de dispositifs en ligne pour chaque produit.',
      REGISTERED_COUNT_DISTRIBUTION: 'Dispositifs enregistrés',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: 'Graphique de distribution des comptes de dispositifs enregistrés pour chaque produit.',
      CONNECTIVITYTEST: 'Test de connectivité',
      REBOOT: 'Redémarrer',
      FACTORYRESET: 'Appliquer la réinitialisation d’usine',
      UPLOADLOG: 'Télécharger le journal',
      UPGRADEFIRMWARE: 'Mettre à jour',
      GENERATEREPORT: 'Générer un rapport',
      ADDFILE: 'Ajouter un pointeur de fichier',
      SETTING: 'Gérer les préférences',
      GENERAL: 'Général',
      GENERALSTATUS: 'État général',
      OPERATION: 'Affecter des opérations',
      PROTOCOL: 'Protocole',
      SELECTPROTOCOL: 'Sélectionner le protocole',
      NETCONFAUTH: "NETCONF prend en charge l'authentification par mot de passe et par clé privée, saisissez soit un mot de passe, soit une clé privée.",
      REGISTER: "S'inscrire",
      CONNECTIONREQ: 'Demande de connexion',
      TELEMETRY: 'Télémétrie',
      INFO: 'Info',
      MAP: 'Carte',
      FAPCONNECTEDCLIENTS: 'Clients FAP connectés',
      FAPCONNECTEDCLIENTSDESCRIPTION: 'Clients FAP connectés.',
      GENERALINFO: 'Informations générales',
      GENERALINFODESCRIPTION: "Informations générales sur le dispositif telles que le numéro de série, le nom du modèle et la version du logiciel.",
      CELLULARSTATUS: 'État cellulaire',
      CELLULARSTATUSDESCRIPTION: "Informations cellulaires telles que l'état du service, la technologie d'accès, la bande, RSRP, RSRQ et RSRI",
      WORKFLOWLIST: 'Liste des flux de travail',
      WORKFLOWLISTDESCRIPTION: 'Liste des flux de travail appliqués à ce dispositif actuellement.',
      DATAUSAGE: 'Utilisation des données cellulaires',
      DATAUSAGEDESCRIPTION: "Graphique historique de l'utilisation des données cellulaires.",
      CLIENTS: 'Clients',
      CLIENTDESCRIPTION: 'Clients tels que les appareils mobiles, les ordinateurs portables, les nombres de tablettes.',
      UE: 'UE',
      UEDESCRIPTION: 'Nombre total d’UE connectés à la petite cellule.',
      SIMCARDINFO: 'Informations sur la carte SIM',
      SIMCARDINFODESCRIPTION: 'Informations sur la carte SIM telles que statut, ICCID, IMSI, IMPI et IMPU',
      WIFISTATUS: 'État radio WiFi',
      WIFISTATUSDESCRIPTION: "Paramètres et état de l'actuel radio de toutes les bandes/interface(s) WiFi disponibles.",
      WIFICHANNELUTLILIZATION: 'Utilisation du canal WiFi',
      WIFICHANNELUTLILIZATIONDESCRIPTION: "Chargement du canal à l'air du trafic de toutes les bandes disponibles.",
      CONNECTEDHOSTS: 'Hôtes connectés',
      CONNECTEDHOSTSDESCRIPTION: 'Informations sur les hôtes connectés au dispositif.',
      REGSTATUS: "État de l'enregistrement",
      REGSTATUSDESCRIPTION: "Informations d'enregistrement telles que la dernière heure d'enregistrement, la dernière heure de déconnexion et le dernier motif de déconnexion.",
      ERRORSTATUS: 'État des erreurs',
      ERRORSTATUSDESCRIPTION: "Liste des erreurs signalées par le dispositif telles que la description de l'erreur et l'heure de l'erreur.",
      SPECTRUMDESCRIPTION: "Informations sur le mode de déploiement de la petite cellule avec un spectre spécifique, tel que NR Band, ARFCN et créneau horaire TDD.",
      APPLIST: 'Liste des applications',
      APPLISTDESCRIPTION: 'Informations sur les applications pour le dispositif.',
      SERVICEPRO: 'Fournisseur de services',
      SERVICEPRODESCRIPTION: 'Liste des fournisseurs de services pour le dispositif.',
      SPECTRUM: 'Spectre',
      PLMNNEIGHBORLIST: 'Liste des voisins / PLMN',
      NEIGHBORLIST: 'Liste des voisins',
      NEIGHBORLISTDESCRIPTION: 'Informations sur la liste des voisins ANR, la liste des transferts et la liste PLMN.',
      HANDOVERLIST: 'Liste de voisins configurée',
      ANRNEIGHBORLIST: 'Liste des voisins ANR',
      UTRANEIGHBORLIST: 'Liste des voisins Utra',
      PLMNLIST: 'Liste PLMN',
      APPSTATUS: 'État des applications du dispositif',
      APPSTATUSDESCRIPTION: "Consommation d'énergie des applications pour le dispositif.",
      LXCSTATUS: 'État LXC',
      LXCSTATUSDESCRIPTION: 'État du conteneur Linux pour le dispositif.',
      SUBSCRIPTION: 'USP Abonnement',
      SUBSCRIPTIONDESCRIPTION: "Liste des éléments d’abonnement via USP avec des informations détaillées telles que l'alias, le statut, le type de notification et le destinataire.",
      BULKDATAPROFILE: 'Profil de données en vrac',
      SOFTWAREMODULES: 'Modules logiciels',
      CONTROLLERTRUSTROLE: 'Rôle de confiance du Contrôleur',
      CONTROLLERTRUSTROLEDESCRIPTION: "liste les rôles de Controller Trust contenant des détails tels que les alias, les statuts, les autorisations, le nombre d'entrées, etc.",
      FIRMWAREIMAGES: 'mage du firmware',
      FIRMWAREIMAGESDESCRIPTION: 'liste les images du firmware contenant des détails tels que alias, Status, bootfailurelog, etc.',
      BOOTFAILURELOG: "Journal des échecs de démarrage",
      AVAILABLE: "Disponible",
      DEVICEACTION: 'Action du dispositif',
      CURRENTALARMLIST: 'Liste des alarmes actuelles',
      ALARMLIST: 'Liste des alarmes',
      ACKALARM: "Accuser réception de l'alarme",
      ALARMMGMT: 'Gestion des alarmes',
      ALARMMGMDESCRIPTION: "Liste de toutes les alarmes signalées par le dispositif, y compris celles réglées, non réglées avec gravité, heure de l'événement et cause probable.",
      CLEAREDTIME: "Heure réglée",
      HISTORYALARM: 'Gestion de l’historique des alarmes',
      CURRENTYALARM: 'Liste des alarmes actuelles',
      DATAMODEL: 'Modèle de données',
      SELECTDATAMODEL: 'Sélectionner le modèle de données',
      DATANODE: 'Nœud de données',
      DATANODEDESCRIPTION: "Affiche tous les nœuds de paramètres signalés par le dispositif à AMP dans une structure arborescente.",
      PARAMETERDATADESCRIPTION: "Informations détaillées sur le nœud de paramètre sélectionné, telles que les nœuds enfants, le nom du paramètre, l'attribut, le chemin et la valeur.",
      PARAMETERDATA: 'Données de paramètres',
      SELECTDATANODE: 'Veuillez sélectionner un paramètre à partir du nœud de données',
      LOGS: 'Journaux',
      LOG: 'Journal',
      SESSIONLOG: 'Liste des journaux de session',
      SESSIONLOGDESCRIPTION: 'Liste des journaux de session entre AMP et le dispositif.',
      SESSIONLOGRATE: 'Taux de rapport',
      SESSIONLOGRATEDESCRIPTION: "Statistiques du taux de rapport périodique de l'appareil (compte) à AMP au cours des dernières 24 heures.",
      SESSLOG: 'Journal de session',
      PENDINGLOG: "Liste des journaux d'opérations en attente",
      PENDINGLOGPENDINGLOGS: "Liste des tâches d'opérations en attente d'attente de réception du dispositif.",
      OPERATIONLOGS: "Liste des journaux d'opérations",
      OPERATIONLOGSDESCRIPTION: "Liste des tâches d'opérations assignées par AMP que le dispositif a exécutées, ainsi que les résultats signalés.",
      CALLLOG: "Liste des journaux d’appels",
      CALLLOGDESCRIPTION: 'Liste des enregistrements d’appels tels que pair, type et durée.',
      SPEEDTESTHISTORY: 'Historique du test de vitesse',
      CONNECTIVITYTESTHISTORY: 'Historique du test de connectivité',
      CONNECTIVITYTESTHISTORYDESCRIPTION: 'Historique du test de connectivité.',
      DEVICEREPORTLIST: 'Liste des rapports de dispositifs',
      DEVICEREPORTLISTDESCRIPTION: 'Liste des rapports résumés générés avec les paramètres clés pour le dispositif.',
      PMLOG: 'Journaux KPI PM',
      PMLOGDESCRIPTION: 'Liste des enregistrements de données KPI signalées par la petite cellule.',
      ADVANCE: 'Avancé',
      CBSDSTATUS: 'État CBSD',
      CBSDSTATUSDESCRIPTION: "Informations sur le dispositif de service citoyen à large bande telles que le fournisseur SAS, l'ID CBSD, l'état GPS et l'état de la subvention.",
      CBSDCONDIGS: 'Configurations CBSD',
      CBSDCONDIGSDESCRIPTION: 'Configuration du dispositif de service citoyen à large bande, comme le numéro de série CBSD, le modèle et la version du logiciel.',
      TERMINAL: 'Terminal',
      TERMINALDESCRIPTION: "Fournir des dispositifs qui prennent en charge l'opération à distance en temps réel via le terminal.",
      COMMANDXML: 'XML de commande',
      COMMANDXMLDESCRIPTION: 'XML de commande de dispositif.',
      ACSSTATUS: 'État ACS',
      DEVICESTATUS: 'État du dispositif',
      SASTATUS: 'État de la cellule',
      RFCONTROL: 'Contrôle RF de la cellule',
      RFCONTROLDESCRIPTION: 'Fournir des paramètres et des commutateurs liés au RF.',
      ANTENNABEAM: 'Faisceau d’antenne',
      BEAMMENU: 'Menu de faisceau',
      ANTENNABEAMDESCRIPTION: "Fournir un changement de l'angle du faisceau de l'antenne de la petite cellule.",
      BEAMID: 'ID de faisceau',
      GPSANTENNA: 'Antenne GPS',
      GPSANTENNAPATH: 'Chemin de l’antenne GPS',
      ANTENNAPATH: 'Chemin de l’antenne',
      GPSANTENNADESCRIPTION: 'Fournir la sélection du chemin de l’antenne GPS comme externe ou interne.',
      DEPLOYMENTMODE: 'Mode de déploiement',
      DEPLOYMENTMODEDESCRIPTION: 'Mode de déploiement du dispositif.',
      HWMONITOR: 'Diagnostic machine',
      HWMONITORDESCRIPTION: "Activer les diagnostics de la petite cellule, tels que CPU, température et consommation d'énergie.",
      RECONNECT: 'Reconnecter',
      DISCONNECT: 'Déconnecter',
      DOWNLOADLOG: 'Télécharger le journal',
      REPEATLASTCMD: 'Répéter la dernière commande',
      CLEARSCROLLBACK: 'Effacer le défilement',
      CELLULARDIAGNOSTIC: "Demander un diagnostic de l'interface cellulaire",
      WIFIDIAGNOSTIC: "Demander un diagnostic de l'interface WIFI",
      SYSHEALTHREBOOT: "Demander un redémarrage de la santé du système",
      DIFFRENTPROTOCOLNOTALLOWED: "L'enregistrement en masse de deux ou plusieurs dispositifs de protocole différents n'est pas autorisé",
      DEVICEERRCODE1: "L'ajout d'appareils à des produits qui n'existent pas n'est pas autorisé",
      DEVICEERRCODE2: "L'appareil est déjà enregistré",
      DEVICEERRCODE3: "Le numéro de série n'est pas légal",
      INVALIDPRODUCT: 'Produit invalide',
      DOFURBISHMENT: " sera remis à neuf et toutes les données de l'appareil seront supprimées. Voulez - vous continuer?",
      CONFURBISHMENT: 'Processus de rénovation',
      MAXUES: 'Nombre maximal d’UE simultanés :',
      OSMERROR: "Erreur de chargement de la carte. Veuillez vérifier la connexion Internet ou réessayer plus tard.",
      GOOGLEMAPERROR: "Échec du chargement de l'API Google Maps. Veuillez vérifier la clé API Google Maps ou la connexion Internet.",
      PERMISSIONNUMBEROFENTRIES: "Nombre d'entrées",
      ACTION: {
        ASSIGN_OPERATION: 'Opération Assignée',
        ASSIGN_OPERATION_SUCC: "L'opération a été assignée avec succès.",
        ASSIGN_OPERATION_FAIL: "Assignation de l'opération a échoué.",
        ASSIGN_OPERATION_SUCCESS_MESSAGE: "L'opération est réussie.",
        ASSIGN_OPERATION_WARNING_MESSAGE1: 'En attente',
        ASSIGN_OPERATION_WARNING_MESSAGE2: "pour obtenir l'opération.",
        ASSIGN_OPERATION_FAIL_MESSAGE: "L'assignation de l'opération a échoué.",
        CONFIRM_LIVE_UPDATE: 'Confirmer Mise à Jour En Direct',
        DO_LIVE: 'Voulez-vous faire une mise à jour en direct ',
        DO_LIVE_UPDATE: 'Voulez-vous faire une mise à jour en direct sur les appareils sélectionnés?',
        LIVESUCCESS: 'Mise à Jour En Direct Réussie!',
        LIVEFAIL: 'Échec de la Mise à Jour En Direct!',
        CONFIRM_REBOOT: 'Redémarrer ',
        DOREBOOT: 'Pendant le redémarrage, tous les services fournis par l\'appareil (y compris la gestion à distance) seront temporairement suspendus pendant plusieurs minutes.',
        SELECTREBOOT: 'Pendant le redémarrage, tous les services fournis par les appareils sélectionnés (y compris la gestion à distance) seront temporairement suspendus pendant plusieurs minutes.',
        ABOUT_TO_REBOOT: 'Sur le point de redémarrer...',
        REBOOT_SUCCESS: 'Redémarrage Réussi!',
        WAIT_REBOOT: 'En attente du redémarrage...',
        REBOOTFAIL: 'Échec du Redémarrage!',
        REBOOT_TIMED_OUT: 'Le délai de redémarrage a expiré !',
        SHUTDOWN_SUCCESS: 'Fermer avec succès!',
        CONFIRMFACTORYRESET: 'Réinitialisation d\'usine ',
        DOFACTORYRESET: 'L\'appareil sera restauré à l\'état de fabrication d\'origine, et tous les paramètres provisionnés par l\'utilisateur seront effacés.',
        SELECTFACTORYRESET: 'Les appareils sélectionnés seront restaurés à l\'état de fabrication d\'origine, et tous les paramètres provisionnés par l\'utilisateur seront effacés.',
        ACTION_CONFIRM: 'Voulez-vous toujours continuer?',
        ABOUT_TO_FACTORYRESET: "Sur le point de réinitialiser les paramètres d'usine...",
        FACTORYRESETSUCC: 'Réinitialisation d’Usine Réussie!',
        FACTORYRESETFAIL: 'Échec de la Réinitialisation d’Usine!',
        FACTORYRESET_TIMED_OUT: "Le délai de réinitialisation d'usine a expiré !",
        CONFIRMUPLOADLOG: 'Télécharger le journal',
        DOUPLOADLOG: 'Demander à l\'appareil de télécharger son journal système sur une URL distante pour un dépannage supplémentaire. L\'URL de téléchargement peut être configurée dans la section Fichiers des paramètres de la page Produit et Système. ?',
        SELECTUPLOADLOG: 'Demander aux appareils sélectionnés de télécharger leur journal système sur une URL distante pour un dépannage supplémentaire. L\'URL de téléchargement peut être configurée dans la section Fichiers des paramètres de la page Produit et Système.',
        UPLOADLOG: "Télécharger le journal",
        UPLOADLOGSUCC: "Téléchargement du journal réussi !",
        UPLOADLOGSUCCMESSAGE: "L'opération sur l'appareil a été créée avec succès !",
        UPLOADLOGFAIL: 'Échec du Téléchargement du Journal!',
        ABOUT_TO_UPLOADLOG: 'Sur le point de télécharger le journal...',
        UPLOADLOG_TIMED_OUT: 'Le téléchargement du journal a expiré !',
        CONFIRMFIRMWARE: 'Mettre à jour ',
        SUCCESSFIRMWARE: 'Mise à jour réussie !',
        UPGRADE_BEING_DOWNLOADED: 'Téléchargement...',
        ASSIGN_UPGRADE_OPERATION: "Attribuer l'opération de mise à niveau",
        UPGRADE_STATUS_INSTALLING: 'Installation...',
        UPGRADE_BEING_ACTIVATED: 'Activation...',
        UPGRADE_DEVICE_BEING_RESTARTED: "L'appareil redémarre...",
        WAIT_UPGRADE: "En attente d'informations...",
        UPGRADING: 'Mise à niveau...',
        UPGRADE_COMPLETE_REBOOTING: 'Mise à niveau terminée, redémarrage',
        UPGRADE_TIMED_OUT: 'La mise à niveau a expiré!',
        UPGRADEFIRMWARE: 'Mettre à jour ',
        UPGRADEFIRMWARESUCCMESSAGE: "L'opération sur l'appareil pour la mise à niveau du firmware a été créée avec succès !",
        FAILFIRMWARE: 'Échec de la mise à jour!',
        FAILFIRMWAREPENDING: 'Journal des opérations en attente de mise à jour.',
        REBOOTPENDING: 'Redémarrage en Attente dans le Journal des Opérations.',
        FACTORYRESETPENDING: 'Réinitialisation d’Usine en Attente dans le Journal des Opérations.',
        UPLOADLOGPENDING: 'Téléchargement du Journal en Attente dans le Journal des Opérations.',
        DOFIRMWARE: 'Pendant le processus de mise à jour du firmware, tous les services fournis par l\'appareil (y compris la gestion à distance) seront temporairement suspendus pendant plusieurs minutes.',
        SELECTFIRMWARE: 'Pendant le processus de mise à jour du firmware, tous les services fournis par les appareils sélectionnés (y compris la gestion à distance) seront temporairement suspendus pendant plusieurs minutes.',
        CONFIRM_GENERATE: 'Générer ',
        CONFIRM_REPORT: ' Rapport',
        CONFIRM_GENERATE_REPORT: 'Générer le rapport',
        DO_GENERATE_REPORT: 'Générer un rapport récapitulatif avec les paramètres clés de l\'appareil. Le rapport peut être trouvé dans le widget Liste des rapports de l\'appareil sur la page Journal.',
        SELECT_GENERATE_REPORT: 'Générer un rapport récapitulatif avec les paramètres clés des appareils sélectionnés. Le rapport peut être trouvé dans le widget Liste des rapports de l\'appareil sur la page Journal.',
        SUCCESSGENE: 'Génération du Rapport Réussie!',
        FAILGENE: 'Échec de la Génération du Rapport!',
        CONFIRM_DELETE: 'Confirmer la Suppression',
        DO_DELETE: 'Voulez-vous supprimer les appareils sélectionnés?',
        CONFIRM_APPLY: 'Confirmer l\'application',
        DO_APPLY_CONFIGRUATION: 'Voulez-vous appliquer cette configuration ?',
        IMPORT_JSON_FILE_MESSAGE: 'Veuillez importer le fichier JSON de configuration.',
        APPLY_SUCC: 'Application réussie !',
        APPLY_FAIL: 'Échec de l\'application !',
        PLESESELECT: 'Veuillez sélectionner un Appareil',
        CONFIRMReset: 'Confirmer la Réinitialisation',
        SUREReset: 'Êtes-vous sûr de vouloir réinitialiser?',
        RESETPERSONALTHEME: 'Êtes-vous sûr de vouloir réinitialiser le thème personnel pour toutes les pages?',
        RESETCURRENTPERSONALTHEME: 'Êtes-vous sûr de vouloir réinitialiser le thème personnel de votre page actuelle?',
        RESETALL: 'Réinitialisez toutes les pages',
        RESETCURRENTPAGE: 'Réinitialisez la page actuelle',
        RESETSuccess: 'Réinitialisation de la Disposition du Widget réussie',
        CONFIRMSave: 'Confirmer la Sauvegarde',
        SURESave: 'Êtes-vous sûr de vouloir sauvegarder l’édition en cours?',
        SAVESuccess: 'Sauvegarde de la Disposition du Widget réussie',
        DODELETE: 'Voulez-vous supprimer ',
        DELETESUCCESS: 'Suppression Réussie',
        DELETEFAIL: 'Échec de la Suppression!',
        CONFIRMBAN: 'Confirmer le Bannissement',
        BANSUCCESS: 'Bannissement Réussi!',
        BANFAIL: 'Échec du Bannissement!',
        DOBAN: 'Voulez-vous bannir ',
        BANSELECT: 'Voulez-vous bannir les appareils sélectionnés?',
        CONFIRMRegister: 'Confirmer l’Enregistrement',
        ONLYVALID: 'Cocher préserve automatiquement les appareils légitimes',
        SUCCESSRegister: 'Enregistrement de l’Appareil Réussi!',
        FAILRegister: 'Échec de l’Enregistrement de l’Appareil!',
        DORegister: 'Voulez-vous enregistrer ',
        FAIL: 'Échec!',
        CONFIRMOPER: 'Confirmer la Suppression du Journal des Opérations',
        OPERSELECT: 'Voulez-vous supprimer le journal des opérations sélectionnées?',
        CONNECTIVITYTESTSELECT: "Voulez-vous supprimer l'historique des tests de connectivité sélectionnés?",
        CONNECTIVITYTEAllCONFIRM: 'Confirmer la Suppression de l’Historique des Tests de Connectivité?',
        CONNECTIVITYTEAll: 'Voulez-vous effacer l’historique des tests de connectivité?',
        SPEEDTESTSTSELECT: 'Voulez-vous supprimer l\'historique de test de vitesse sélectionné ?',
        SPEEDTESTAllCONFIRM: 'Confirmez-vous le nettoyage de l\'historique des tests de vitesse ?',
        SPEEDTESTAll: 'Voulez-vous nettoyer l\'historique des tests de vitesse ?',
        PLESEOPER: 'Veuillez sélectionner le Journal des Opérations Complétées',
        DOTAG: 'Voulez-vous supprimer l’étiquette ',
        TAGSUCC: 'Suppression des Étiquettes Réussie!',
        TAGFAIL: 'Échec de la Suppression des Étiquettes!',
        ADDTAGSUCC: 'Ajout des Étiquettes Réussi!',
        ADDTAGFAIL: 'Échec de l’Ajout des Étiquettes!',
        UPDAGEDEVICE: 'Mettre à jour l’appareil!',
        CONFIRMCANCEL: 'Confirmer l’annulation de l’opération',
        DOCANCEL: 'Voulez-vous annuler ',
        CANCELSUCC: 'Annulation Réussie!',
        CANCELFAIL: 'Échec de l’Annulation!',
        SELECTCANCEL: 'Voulez-vous annuler les journaux des opérations en attente sélectionnés?',
        PLEASECANCEL: 'Veuillez sélectionner le Journal des Opérations en Attente',
        CONFIRMREPORT: 'Confirmer la suppression du rapport',
        SELECTREPORT: 'Voulez-vous supprimer les journaux de rapport sélectionnés?',
        PLEASEREPORT: 'Veuillez sélectionner le Journal de Rapport',
        CONFIRMSESSION: 'Confirmer la Suppression du Journal de Session',
        SELECTSESSION: 'Voulez-vous supprimer le journal de session sélectionné?',
        PLEASESESSION: 'Veuillez sélectionner le Journal de Session',
        CONFIRMACK: 'Confirmer l’Accusé de Réception de l’Alarme?',
        SELECTACK: 'Voulez-vous accuser réception de toutes les alarmes sélectionnées?',
        DOACK: 'Voulez-vous accuser réception de l’alarme ',
        CONFIRMSAVELOCA: 'Confirmer la Sauvegarde de l’Emplacement',
        DOSAVELOCA: 'Voulez-vous sauvegarder l’emplacement?',
        CONFIRMRESETLOCA: 'Confirmer la Réinitialisation de l’Emplacement',
        DORESETLOCA: 'Voulez-vous réinitialiser l’emplacement?',
        SAVESUCC: 'Sauvegarde Réussie!',
        SAVEFAIL: 'Échec de la Sauvegarde!',
        RESETSUCC: "Réinitialisation réussie !",
        RESETFAIL: "Échec de la réinitialisation !",
        ACTIVESUCC: 'Activation de l’Appareil Réussie!',
        ACTIVEFAIL: 'Échec de l’Activation de l’Appareil!',
        BANDEVSUCC: 'Bannissement de l’Appareil Réussi!',
        BANDEVFAIL: 'Échec du Bannissement de l’Appareil!',
        WAITOTHER: 'En attente de l’arrivée de l’autre partie à la session...',
        ACSTROUBL: 'ACS rejoint la session de dépannage',
        WAITCPE: 'En attente de l’entrée du CPE dans la session de dépannage',
        ACSCONNECT: 'ACS est en train de se connecter à la session de dépannage',
        ACSSETTING: 'En attente de la configuration de la session de dépannage par ACS',
        CPEJOIN: 'CPE rejoint la session de dépannage',
        SESSESTABLISH: 'La session est établie',
        CONFIRMTROUB: 'Confirmer la déconnexion du dépannage',
        DODISCONNECT: 'Voulez-vous déconnecter la session ',
        CONFIRMUSER: 'Confirmer la suppression de l’utilisateur?',
        DOUSER: 'Voulez-vous supprimer l’utilisateur',
        SUCCESS: 'Succès!',
        ERROR: 'Erreur!',
        CONFLOGENTRY: 'Confirmer la suppression de l’entrée du journal',
        UPLOAD_CONNECTIVITYTEST: 'Téléchargement du Test de Connectivité, veuillez patienter ...',
        DOWNLOAD_CONNECTIVITYTEST: 'Téléchargement du Test de Connectivité, veuillez patienter ...',
        COMPLETE_CONNECTIVITYTEST: ' Test de Connectivité terminé !',
        CONNECTIVITYTEST_URL: 'URL du Fichier de Test',
        UPLOAD_SPEEDTEST: 'Test de vitesse de téléchargement, veuillez patienter...',
        DOWNLOAD_SPEEDTEST: 'Test de vitesse de téléchargement, veuillez patienter...',
        COMPLETE_SPEEDTEST: 'Test de vitesse terminé !',
        SPEEDTEST_URL: 'URL du fichier de test',
        REQUESTUPDATEFAIL: 'Échec de la Demande de Mise à Jour',
        CONFVERSION_NOTCHANGED: "La version de la configuration n'a pas changé.",
      },
      OPERATION_ACTION: {
        SELECT_OPERATION: 'Sélectionnez les profils',
        SELECT_WORKFLOW: 'Sélectionner Workflow',
        SELECT_Action_OR_WORKFLOW: "Sélectionnez Profils ou Flux de travail",
        ADD_OPERATION: "Ajouter aux profils",
        OPERATION_COUNT: 'Compter',
        EDIT_OPERATION: 'Modifier l’Opération',
        EDIT_CONFIGURATION: 'Modifier la configuration',
        OPERATION_DETAILS: 'Détails de l’Opération',
        SETUP_OPERATION: 'Détails des profils de configuration',
        ADD_OPERATION_TO: 'Ajouter une opération aux profils',
        OPERATION_ACTIONS: "Profils d'opération",
        ENTER_OPERATION_ACTIONS: "Entrez vos profils d'exploitation",
        OPERATION_TYPE: 'Type d’Opération',
        SELECT_OPERATIONTYPE: 'Sélectionner le Type d’Opération',
        EDIT_ACTIONS: "Modifier les profils d'opération",
        MANUAL_OPERATION: 'Fichier de configuration manuelle',
        MANUAL_WORKFLOW: 'Workflow manuel',
        COMPLETION_RATE: "Taux d'achèvement"
      },
      AMP: 'AMP',
      GO: 'Go',
      COLLAPSE: 'Réduire',
      COLLAPSEALL: 'Réduire tout',
      EXPAND: 'Étendre',
      EXPANDALL: 'Étendre tout',
      DISCOVER: 'Découvrir',
      DISCOVERDATAMODEL: 'Découvrir : Demander à l’appareil de mettre à jour les paramètres sélectionnés',
      PATH: 'Chemin',
      REQUIRED: 'Requis',
      OPTIONALPARAM: "Il n'y a pas de paramètres optionnels",
      SENDRESP: 'Envoyer Resp',
      ACCESSLIST: "Liste d'accès",
      SELECTACCESSLIST: 'Sélectionner la liste d’accès',
      REPORTEDVALUE: 'valeurRapportée',
      INPUTLIST: "Liste d'entrée",
      ADDEDITTAG: 'Ajouter/Modifier des balises',
      ADDTAGS: 'Ajouter des balises',
      ADDTAG: 'Ajouter des balises',
      INPUTTAGNAME: "Entrer le nom de l'balises",
      TAGSLIST: 'Liste des balises',
      EXISTTAG: 'Possède déjà la même balises!',
      ADDEDITLABEL: 'Ajouter/Modifier une étiquette',
      INPUTLABELNAME: "Nom de l'étiquette d'entrée",
      LOCATION: 'Localisation',
      HEALTHYSTATUS: 'État de santé',
      INFORMHISTORY: 'Historique des notifications',
      SESSIONLOGINTERVAL: 'Intervalle des journaux de session',
      SESSIONLOGINTERVALDESCRIPTION: "Diagramme historiques de l'intervalle de rapport périodique de l'appareil vers l'AMP au cours des dernières 24 heures.",
      TIMEINTERVAL_BETWEEN_CONSECUTIVE_INFORMS: "Intervalles de temps entre les Informs consécutifs",
      LOCATIONDESCRIPTION: "Afficher la position de l'appareil sur la carte.",
      ONLINERATE: 'Taux en ligne',
      ONLINERATEDESCRIPTION: "Le diagramme radar en pentagone affiche l'état de santé de l'appareil.",
      RESET: 'Réinitialiser',
      EDITCOMPONENT: 'Ajouter un widget',
      BULKLOGLIST: 'Liste des journaux en vrac',
      EDITFILE: 'Modifier le fichier',
      FILTERNODE: 'Filtrer les nœuds',
      SELECTACTION: 'Action sur les appareils sélectionnés',
      PRODUCTINFO: 'Informations sur le produit',
      CONNHISTORY: 'Historique des connexions',
      LAST24: '(Dernières 24 heures)',
      WIFIUSAGE: 'Utilisation du canal WiFi',
      WIFIANALYZER: 'Analyseur WiFi',
      WIFIANALYZERDESCRIPTION: 'Diagramme RSSI WiFi des AP voisins à partir des canaux/bandes disponibles sur cet appareil.',
      CELLSTATUS: 'État de la petite cellule',
      CELLSTATUSDESCRIPTION: "État de la petite cellule, tel que le statut du service radio, PC, gNB ID, MCC, MNC et TAC.",
      CELLHANDOVERSTATUS: 'État de transfert de la petite cellule',
      CELLHANDOVERSTATUSDESCRIPTION: 'État de transfert de la petite cellule.',
      COVERMAP: 'Carte de couverture',
      COVERMAPDESCRIPTION: "Affiche les emplacements et la couverture radio de l'appareil dans le groupe.",
      TOTALALARMSDESCRIPTION: "Nombre total d'alarmes signalées par l'appareil.",
      ALARMSDESCRIPTION: "Nombre total d'alarmes avec différentes gravités, telles que critique, majeure, mineure et alerte.",
      CREATEMAP: 'Créer',
      SAVEMAP: 'Sauvegarder',
      SELECTIMAGE: 'Sélectionner une image',
      IMAGEPRE: "Aperçu de l'image",
      MAPX: 'Dimensions de la carte (X)',
      MAPY: 'Dimensions de la carte (Y)',
      EDITMAP: 'Éditer',
      M: 'm',
      ENGMODE: 'Mode ingénieur',
      CLIMODE: 'Mode CLI',
      CONFIG: 'Configuration',
      PROVISIONING: 'Provisionnement',
      DEVICEALARM24HRS: 'Alarme dans les 24 heures',
      PMALARM24HRS: 'Anomalies détectées dans les données PM au cours des dernières 24 heures.',
      PMSTATUS_GOOD: 'Aucune donnée PM anormale au cours des dernières 24 heures.',
      PMSTATUS_BAD: 'Les données PM présentent des anomalies au cours des dernières 24 heures.',
      PMSTATUS_NORMAL: 'Aucune donnée PM disponible au cours des dernières 24 heures.',
      PMSTATUS_NOPERMISSION: "L'accès aux données PM est restreint.",
      ADDTOGROUP: 'Rejoindre un groupe',
      ADDDEVTOGROUP: 'Ajouter un appareil au groupe',
      SELECTDEVICE: 'Appareil sélectionné',
      RESTARTNOW: 'Redémarrer maintenant pour que les changements prennent effet.',
      TAGSDESCRIPTION: "Tags personnalisés sur l'appareil pour une gestion plus facile des dispositifs.",
      BATCHSAVE: 'Sauvegarde par lot',
      CPUUSAGEDESCRIPTION: "Pourcentage d'utilisation CPU de l'appareil.",
      MEMORYUSAGEDESCRIPTION: "Pourcentage d'utilisation de la mémoire de l'appareil.",
      CPUUSAGECHART_DESCRIPTION: "Diagramme historique du pourcentage d'utilisation CPU de l'appareil.",
      MEMORYUSAGECHART_DESCRIPTION: "Diagramme historique de l'utilisation de la mémoire de l'appareil.",
      KPIDESCRIPTION: "Diagramme historique des KPI de la petite cellule, tels que RRC, contexte UE et débit.",
      PMPARAMDESCRIPTION: "KPI de la petite cellule, tels que RRC, contexte UE et débit.",
      ALARM_CURRENTNUMBERS: 'Nombre total d’alarmes signalées par l’appareil.',
      PRODUCTMODEL: 'Modèle de produit',
      PRODUCTMODELDESCRIPTION: 'Indique à quel produit appartient l’appareil actuel.',
      DEVICEOFFLINETIPS: 'L’appareil est actuellement hors ligne, l’AMP conserve les informations signalées par l’appareil.',
      DEVICENOTREGISTERTIPS: 'L’appareil est un nouvel appareil ajouté et n’a jamais été en ligne/provisionné.',
      ONLINESTATUS: 'Statut en ligne',
      ONLINESTATUSDESCRIPTION: "Diagramme historique du statut en ligne de l'appareil.",
      REGISTERSINGLEDEVICE: "Enregistrement d'un seul appareil",
      REGISTERBATCHDEVICE: "Enregistrez l'équipement de traitement par lots",
      DOWNLOADSESSIONGCSV: 'Télécharger CSV (Tous les champs)',
      DOWNLOADSESSIONGJSON: 'Télécharger JSON (Contenu complet)',
      DOWNLOADLATESTSESSIONGJSON: 'télécharger les derniers journaux de session',
      TOTALCLIENTHISTORY: "Clients WiFi associés",
      TOTALCLIENTHISTORY_DESCRIPTION: "Graphique historique présentant une vue d'ensemble du nombre de clients associés au WiFi de cet appareil.",
      CLIENTSPERSSID: "Clients WiFi par SSID",
      CLIENTSPERSSID_DESCRIPTION: "Graphique historique présentant une vue d'ensemble du nombre de clients WiFi associés classés par SSID de cet appareil.",
      CLIENTSPERRADIO: "Clients WiFi par Radio",
      CLIENTSPERRADIO_DESCRIPTION: "Graphique historique présentant une vue d'ensemble du nombre de clients WiFi associés classés par radio/bande de cet appareil.",
      TRAFFICPERSSID: "Trafic WiFi par SSID",
      TRAFFICPERSSID_DESCRIPTION: "Graphique historique présentant une vue d'ensemble du trafic WiFi classé par SSID de cet appareil.",
      TRAFFICPERRADIO: "Trafic WiFi par Radio",
      TRAFFICPERRADIO_DESCRIPTION: "Graphique historique présentant une vue d'ensemble du trafic classé par radio/bande de cet appareil.",
      VIEWCHART: 'Voir le graphique',
      LOGDETAIL: 'Détail du journal',
      TXPOWER: 'Puissance de Transmission',
      BEACONPERIOD: 'Période de Beacon',
      SSIDSWITHCLIENTS: 'Répartition des SSID WiFi',
      SSIDSWITHCLIENTS_DESCRIPTION: 'Répartition des SSID des clients WiFi associés sur ce réseau d\'appareil.',
      RSSIDISTRIBUTION: 'Distribution WiFi RSSI',
      RSSIDISTRIBUTION_DESCRIPTION: 'Répartition du RSSI des clients WiFi associés sur cet appareil.',
      WIFIRADIOTHROUGHPUT: 'Utilisation des données radio WiFi',
      WIFIRADIOTHROUGHPUT_DESCRIPTION: "Pour afficher le protocole actuel et l'utilisation des données de la radio sur toutes les bandes/interfaces WiFi disponibles.",
      OPERATINGCHANNELBANDWIDTH: 'Bande Passante',
      BYTESSENT: 'Octets Envoyés',
      BYTESRECEIVED: 'Octets Reçus',
      PACKETSSENT: 'Paquets Envoyés',
      PACKETSRECEIVED: 'Paquets Reçus',
      ERRORSSENT: 'Paquets d\'Erreur Envoyés',
      ERRORSRECEIVED: 'Paquets d\'Erreur Reçus',
      SSIDLIST: 'Liste des SSID WiFi',
      SSIDLIST_DESCRIPTION: "ÉStatut actuel du WiFi concernant l'activation, le WMM, la bande passante et l'utilisation des données sur les SSID prédéfinis de cet appareil.",
      MAXCLIENTS: 'Clients Max',
      WMM: 'WMM',
      UTILIZATION_GREEN: 'Vert',
      UTILIZATION_YELLOW: 'Jaune',
      UTILIZATION_RED: 'Rouge',
      UTILIZATION_GREEN_DESCRIPTION: 'Utilisation ≤ 30%',
      UTILIZATION_YELLOW_DESCRIPTION: '30% < Utilisation ≤ 60%',
      UTILIZATION_RED_DESCRIPTION: 'Utilisation > 60%',
      MCS: 'MCS',
      STATUS: 'Statut',
      INACTIVE: 'Inactif',
      DISABLE: 'Désactiver',
      WHITELISTED: 'Sur liste blanche',
      REFURBISHMENT: 'Rénovation',
      NETWORKTOPOLOGY: "Topologie réseau",
      NETWORKTOPOLOGY_DESCRIPTION: "Affichage en arborescence de toutes les informations de nœuds associées.",
      core_5g: {
        ue_info: 'Info UE',
        ue_info_DESCRIPTION: "L'Info UE fournit des informations détaillées sur l'équipement utilisateur (UE) connecté au réseau 5G Core.",
      },
      healthyStatus: {
        alarm_description: 'Pour chaque alarme critique qui se produit dans les 24 heures, une déduction de 10 points sera appliquée, et pour chaque alarme majeure, une déduction de 5 points sera appliquée, et ainsi de suite.',
        session_log_rate_description: "Le rapport entre le cycle de fonctionnement normal de l'appareil et les rapports soumis à AMP au cours des 24 dernières heures.",
        online_rate_description: "Le taux de disponibilité de l'équipement au cours des 24 dernières heures indique le pourcentage de temps pendant lequel l'équipement a été opérationnel et connecté.",
        service_quality_description: "L'équipement WiFi applique une déduction de 5 points pour chaque client de mauvaise qualité actuellement connecté, tandis que la Small Cell mesure le taux RRC moyen sur 24 heures pour évaluer la qualité du service.",
        service_active_description: 'Pour chaque redémarrage (bootstrap) de l’appareil au cours des 24 dernières heures, une déduction de 20 points est appliquée.'
      },
      cableMedemInfo: {
        cable_medem_info: 'Statut DOCSIS',
        cable_medem_info_description: "Afficher les informations du modem câble à partir du réseau d'accès par câble"
      },
      ONTMediaInfo: {
        ont_media_info: 'Statut des médias ONT',
        ont_media_info_description: 'Afficher les informations sur les médias octiques à partir du périphérique ONT'
      },
      batteryStatus: {
        battery_information: 'État de la batterie',
        battery_description: 'Informations sur la batterie telles que l’état, la température et le niveau',
      },
      WIFIAPINFO: 'Informations sur le point d\'accès WiFi',
      WIFIAPINFO_DESCRIPTION: '',
      WIFIAPINFO_APCONFIGVERSION: 'Configuration AP',
      APCONFIG: 'Configuration AP',
      APNCONFIG: 'Configuration APN',
      CONFIGVER: "Version de configuration",
      WIFIAPINFO_APNAME: 'Nom de l’AP',
      WIFIAPINFO_APROLE: 'Rôle de l’AP',
      WIFIAPINFO_APNCONFIGVERSION: 'Configuration APN',
      EDITAPNAME: 'Modifier le nom de l’AP',
      WIFIAPINFODESCRIPTION: 'Informations sur le point d\'accès WiFi, y compris le rôle de l’AP, le nom de l’AP, la version de la configuration de l’AP et la version de la configuration de l’APN.',
      FIVEGLICENSE: 'Licences logicielles modulaires',
      FIVEGLICENSEDESCRIPTION: "Informations sur la licence de la petite cellule, telles que la date d'expiration et les éléments pris en charge.",
      OPERATION_LOCATION: 'Emplacement du profil',
      OPERATION_SETUPLOCATION: 'Configurer l’emplacement du profil',
      SEARCH_KEYWORD: "Mot-clé de recherche",
      SERVICE_STATUS: "Statut du service",
      NCI: 'NCI',
      GNB_ID: 'GNB ID',
      GNB_ID_LENGTH: 'GNB ID Length',
      MCC: 'MCC',
      MNC: 'MNC',
      AMF_IP_CONTROL_PLANE: 'AMF IP-Control-Plane',
      CELL_ID: 'Cell ID',
      TAC: 'TAC',
      NSSAI: 'NSSAI',
      PCI: 'PCI',
      PARAMETER: 'Paramètre',
      REPORTINTERVAL: 'Intervalle de rapport',
      TIMEREFERENCE: 'Référence temporelle',
      NUMBEROFRETAINEDFAILEDREPROTS: "Nombre de rapports d'échec de rétention",
      ENCODINGTYPE: "Type de codage",
      REQUESTURIPARAMETER: "Demander le paramètre Uri",
      NOTIFTYPE: 'Type de notification',
      REFERENCELIST: 'Liste de référence',
      RECIPIENT: 'Conteneur',
      COMPLETED_DESCRIPTION: "Appareil a terminé les opérations assignées par AMP.",
      FAIL_DESCRIPTION: "Toutes les opérations exécutées par l'appareil échouent.",
      PARTIAL_DESCRIPTION: "Certaines opérations exécutées par l'appareil échouent.",
      CANCEL_DESCRIPTION: "Les opérations assignées par AMP sont annulées avant exécution.",
      PENDING_DESCRIPTION: "En attente que l'appareil reçoive les opérations assignées par AMP.",
      INPROCESS_DESCRIPTION: "En attente que l'appareil rapporte le résultat des opérations exécutées à AMP.",
      LOADMAPFAIL: 'Échec du chargement des tuiles de la carte. Veuillez réessayer plus tard.',
      MISMATCHPROFILE: 'Les paramètres "conGlobalProfile" et "conDeviceSpecificProfile" ne correspondent pas à la configuration.',
      ENERGYSAVING: "Gestion de l'énergie",
      STARTTIME_MUST_BE_EARLIER: "L'heure de début doit être antérieure à l'heure de fin",
      STARTDATE_MUST_BE_EARLIER: "La date de début ne peut pas être postérieure à la date de fin.",
    },
    GROUPS: {
      WIFICLIENT: 'Clients WiFi',
      TITLE: 'Groupes',
      TOTAL: 'Total des groupes',
      LIST: 'Liste des groupes',
      LISTDESCRIPTION: 'Unité distincte contenant des appareils au sein d’AMP, gérant des fonctions spécifiques telles que les opérations par lot et la surveillance de l’état du réseau.',
      ADDGROUP: 'Ajouter un groupe',
      EDITGROUP: 'Modifier le groupe',
      NAME: 'Nom du groupe',
      ACCEPT: 'Accepter',
      MEMBERSTITLE: 'Membres',
      MEMBERS: 'Appareils',
      PENDINGLOGS: 'Liste des logs en attente',
      PENDINGLOGSDESCRIPTION: 'Liste des tâches d’opération en attente de réception par les appareils.',
      OPERATIONLOGS: "Liste des logs d'opération",
      OPERATIONLOGSDESCRIPTION: 'Liste des tâches d’opération assignées par AMP aux appareils du groupe, avec les résultats rapportés.',
      IMPORTTYPE: "Sélectionner le type d'importation",
      SELECTGROUP: 'Sélectionner le nom du groupe',
      CREATE: 'Créer',
      ADDDEVICE: 'Ajouter un appareil',
      INPUTTYPE: "Sélectionner le type d'entrée",
      INPUTVALUE: 'Valeur entrée',
      GROUPUPDATE: 'Mise à jour du groupe',
      FILTERING: 'Éliminer les équipements illégaux',
      ADDTOGROUP: "Ajouter au groupe d'appareils",
      SELECTGROUPTYPE: 'Sélectionner le type de groupe',
      SEARCH: 'Recherche',
      PENDINGOPER: 'Opération en attente',
      PARAMNOTIFICATION: 'Notification de paramètre',
      SELECTPARAMNOTIFICATION: 'Sélectionner la notification de paramètre',
      NONEXISTINGMEN: 'Filtrer automatiquement les appareils non existants',
      BYINPUT: 'Par entrée',
      BYIMPORTFILE: 'Par fichier importé',
      ADDMEMBER: 'Ajouter un appareil',
      FILTERMEMBERIN: "Conditions de filtration dans les produits",
      ENTERKEYWORDS: 'Entrer les mots clés',
      BYPRODUCT: 'Par produit',
      CONFIRM: 'Confirmer',
      DEVICECOUNT: 'Appareils',
      ONLINERATE: 'Taux en ligne',
      SERVICEAVAILABILITY: 'Disponibilité du service',
      CREATEBY: 'Créé par',
      CREATETIME: 'Date de création',
      ALARMCOUNT: 'Alarmes',
      ALARMCOUNTDESCRIPTION: 'Nombre total d’alarmes de différentes sévérités telles que Critique, Majeure, Mineure et Avertissement.',
      DRAGANDDROP: "Glisser et déposer les opérations sélectionnées pour organiser la séquence d'exécution",
      GROUPACT: 'Action de groupe',
      ADDMEMBY: 'Ajouter un appareil par importation',
      ADDMEMINPUT: 'Ajouter un appareil par entrée',
      NETWORKTOPOLOGY: 'Topologie réseau',
      CANTDOOPERATION: "Aucun appareil n'existe dans le groupe",
      ASSIGNOPERATION: "Exécuter l'opération",
      REASSIGNOPERATION: "Réexécuter l'opération",
      DIFFRENTPROTOCOLNOTALLOWED: "L'ajout de deux appareils ou plus avec des protocoles différents dans un même groupe n'est pas autorisé",
      CONFIRMADDTOGROUPMEMBER: "Confirmer l'ajout au groupe",
      ONLYVALID: 'Seuls les appareils légitimes seront ajoutés',
      DEVICEERRCODE1: 'Le produit ne contient pas cet appareil',
      DEVICEERRCODE2: "L'appareil existe déjà dans le Groupe",
      DEVICEERRCODE3: "L'appareil est illégal",
      UE: 'UE',
      UEDESCRIPTION: 'Nombre total d’UE attachées à la petite cellule incluse dans ce groupe.',
      ONLINEDEVICESTITLE: 'Appareils en ligne',
      ONLINEDEVICESDESCRIPTION: 'Nombre total d’appareils en ligne inclus dans le groupe.',
      WIFICLIENTDESCRIPTION: 'Nombre total de clients WiFi inclus dans le groupe.',
      ALARMSDESCRIPTION: 'Nombre total d’alarmes signalées par les appareils inclus dans le groupe.',
      KPIDESCRIPTION: 'Graphique historique du KPI de la petite cellule tel que RRC, UE Context et débit inclus dans ce groupe.',
      DEVICESLISTDESCRIPTION: 'Liste de tous les appareils inclus dans ce groupe.',
      GROUPSDELETED: 'Les groupes sélectionnés ont été supprimés avec succès.',
      RFMAP_MAX_WARNING: "La carte de couverture ne prend en charge que jusqu'à 50 appareils.",
      SSIDLIST: 'Liste des WiFi SSID',
      SSIDLIST_DESCRIPTION: 'Liste de tous les SSID inclus dans ce groupe.',
      SSID: 'SSID',
      SECURITY: 'Sécurité',
      CAPTIVEPORTAL: 'Portail captif',
      MACACL: 'ACL MAC',
      ATF: 'Activation ATF',
      ATFPERCENTAGE: 'ATF',
      BEAMFORMING: 'Formation de faisceaux',
      MAXCLIENTS: 'Clients max',
      WMM: 'WMM',
      BAND: 'Bande',
      BANDWIDTHCONTROL: 'Contrôle de la bande passante',
      DOWNLOAD: 'Envoyé',
      UPLOAD: 'Reçu',
      FOREIGNAPSLIST: 'Liste des AP étrangers',
      FOREIGNAPSLIST_DESCRIPTION: 'Liste des AP WiFi qui ne sont pas inclus dans ce groupe.',
      VLAN: 'VLAN',
      BSSID: 'BSSID',
      CHANNEL: 'Canal',
      RSSI: 'RSSI',
      NEARBYAPS: 'AP à proximité',
      TIME: 'Temps',
      SSIDSWITHCLIENTS: 'Top 6 WiFi SSID avec le plus de clients',
      SSIDSWITHCLIENTS_DESCRIPTION: 'Le WiFi SSID de ce groupe avec les 6 connexions de clients les plus importantes.',
      APSWITHCLIENTS: 'Top 6 WiFi AP avec le plus de clients',
      APSWITHCLIENTS_DESCRIPTION: 'Les 6 AP WiFi les plus connectés par les clients.',
      APSWITHTRAFFIC: 'Top 6 WiFi AP avec le trafic le plus élevé',
      APSWITHTRAFFIC_DESCRIPTION: 'Les valeurs numériques totales des données envoyées et reçues des WiFi AP, en prenant les six AP avec les valeurs les plus élevées.',
      CLIENTSPERSSID: "WiFi Clients par SSID",
      CLIENTSPERSSID_DESCRIPTION: "Graphique historique d'une vue d'ensemble des nombres de connexions clients classées par WiFi SSID pour les appareils inclus dans le groupe.",
      CLIENTSPERRADIO: "WiFi Clients par Radio",
      CLIENTSPERRADIO_DESCRIPTION: "Graphique historique d'une vue d'ensemble des nombres de connexions clients classées par fréquence radio pour les appareils inclus dans le groupe.",
      TRAFFICPERSSID: "WiFi Trafic par SSID",
      TRAFFICPERSSID_DESCRIPTION: "Graphique historique d'une vue d'ensemble du trafic classé par SSID pour les appareils inclus dans le groupe.",
      TRAFFICPERRADIO: "WiFi Trafic par Radio",
      TRAFFICPERRADIO_DESCRIPTION: "Graphique historique d'une vue d'ensemble du trafic classé par fréquence radio pour les appareils inclus dans le groupe.",
      BYTESRECEIVED: 'Reçu',
      BYTESSENT: 'Envoyé',
      ENABLE: 'Activer',
      DISABLE: 'Désactiver',
      TAGSGROUPDESCRIPTION: 'Tags personnalisés sur le groupe pour faciliter la gestion des appareils.',
      COUNTRY: 'Pays',
      SELECTCOUNTRY: 'Sélectionner un pays',
      STREETADDRESS: 'Adresse',
      CITY: 'Ville',
      STATE: 'État',
      ZIPCODE: 'Code postal',
      TAGS: 'Balises',
      INPUTTAGNAME: "Entrer le nom de l'balises",
      ROAD: 'Rue/Voie',
      HOUSE: 'Numéro du bâtiment',
      GROUPDETAILS: 'Détails du groupe',
      GROUPLOCATION: 'Emplacement du groupe',
      GROUPLOCATIONDESCRIPTION: 'Affiche l’emplacement du groupe sur la carte.',
      EDITLOCATION: 'Modifier l’emplacement',
      INPUTLAT: 'Entrer la latitude',
      INPUTLNG: 'Entrer la longitude',
      RESET: 'Réinitialiser',
      LATLNG: 'Latitude et Longitude',
      LAT: 'Latitude',
      LNG: 'Longitude',
      LOCATION: 'Emplacement',
      VIEWLOCATION: 'Voir l’emplacement du groupe',
      VIEWCOVERAGEMAP: 'Voir la carte de couverture',
      VIEWLOCATIONDESCRIB: 'Lorsque le widget Emplacement du groupe est ouvert, cliquer sur l’icône dans la colonne permet à l’utilisateur de voir l’emplacement et les informations du groupe.',
      VIEWCOVERAGEMAPDESCRIB: 'Lorsque le widget Carte de couverture est ouvert, cliquer sur l’icône dans la colonne permet à l’utilisateur de voir la distribution et les informations des appareils inclus dans le groupe.',
      WIDGETNAME: 'Nom, classe ou sous-classe du widget',
      COVERMAP: 'Carte de couverture',
      COVERMAPDESCRIPTION: 'Affiche les emplacements et la couverture radio de tous les appareils appartenant au groupe d’appareils.',
      TOTALALARMSDESCRIPTION: 'Nombre total d’alarmes signalées par les appareils inclus dans le groupe.',
      ALARMMGMT: 'Gestion des alarmes',
      ALARMMGMTDESCRIPTION: 'Liste toutes les alarmes signalées par les appareils inclus dans le groupe, y compris les alarmes effacées, non effacées avec la sévérité, l’heure de l’événement et la cause probable.',
      NETWORK: 'Réseau'
    },
    PRODUCTS: {
      LIST: 'Liste de produits',
      LISTDESCRIPTION: 'Une unité distincte contenant des appareils dans AMP, gérant des fonctions spécifiques telles que le contrôle de la liste d\'accès, le paramètre de provisionnement par défaut et le champ de gestion du compte utilisateur. Elle est nécessaire pour l\'enregistrement et l\'intégration de l\'appareil.',
      NETWORKRADIOACCESSLIST: 'Liste des réseaux d\'accès radio',
      NETWORKRADIOACCESSLISTDESCRIPTION: 'Une unité distincte pour gérer des appareils spécifiques tels que les petites cellules. Un groupe du même nom sera également créé à des fins de gestion du réseau.',
      NETWORKWIFIAPLIST: 'Liste des réseaux WiFi AP',
      NETWORKWIFIAPLISTDESCRIPTION: 'Une unité distincte pour gérer des appareils spécifiques tels que les points d\'accès WiFi. Un groupe du même nom sera également créé à des fins de gestion du réseau.',
      NETWORKWIFIMESHLIST: 'Liste des réseaux WiFi Mesh',
      NETWORKWIFIMESHLISTDESCRIPTION: 'Une unité distincte pour gérer des appareils spécifiques tels que les points d\'accès WiFi Mesh. Un groupe du même nom sera également créé à des fins de gestion du réseau.',
      ACTION: 'Action sur le produit',
      ADD: 'Ajouter un produit',
      ADDRADIOACCESSNETWORK: 'Ajouter un réseau d\'accès radio',
      ADDWIFIAPNETWORK: 'Ajouter un réseau WiFi AP',
      ADDWIFIMESHNETWORK: 'Ajouter un réseau WiFi Mesh',
      PERMISSION: 'Permission',
      DEVICEPERMISSION: 'Permission de l’appareil',
      ADDPERMISSION: 'Liste autorisée',
      PERMITTEDTYPE: "Contrôle d'accès",
      PROVISIONINGTYPE: 'Type de réglage ',
      SELECTTYPE: "Sélectionner le Contrôle d'accès",
      ALLOWALL: 'Tout autoriser',
      WHITELIST: 'Liste autorisée',
      CREATEWHITELIST: 'Créer une liste autorisée',
      LABEL: 'Nom de l’étiquette',
      LABELREQUIRED: 'Ce nom d’étiquette est requis!',
      NAMEREQUIRED: 'Ce nom est requis!',
      PRODUCTTYPE: 'Type de produit',
      SELECTPRODUCTTYPE: 'Sélectionner le type de produit',
      PRODUCTPICTURE: 'Image du produit',
      PRODUCTNAME: 'Nom du produit',
      ISPRODUCTNAME: 'Ce nom de produit est requis!',
      ISPRODUCTCLASS: 'Cette classe de produit est requise!',
      TARGETPRODUCT: 'Nom du produit cible',
      OBJECTNAME: 'Nom de l’objet',
      ENTEROBJECTNAME: 'Entrer le nom de l’objet',
      PRODUCTDETAILS: 'Détails du produit',
      DETAILS: 'Détails',
      CHOOSEFILE: 'Choisir un fichier',
      PERMITEDDEVICE: "Appareils autorisés",
      DEVICELIMITS: 'Limites de l’appareil',
      UPDATEDTIME: 'Heure de la mise à jour',
      EDITPRODUCT: 'Modifier le produit',
      EDITRAN: 'Modifier le réseau d’accès radio',
      EDITAPN: 'Modifier le réseau WiFi AP',
      EDITMESH: 'Modifier le réseau WiFi Mesh',
      PRODUCTMODEL: 'Modèle du produit',
      PRODUCTMODELDESCRIPTION: 'Modèle du produit, description et image',
      BYDEFAULT: 'Sélectionner une image',
      BYUPLOAD: 'Télécharger une image',
      DATACOLLECT: 'Collecte de données',
      DATACOLLECTDESCRIPTION: "Activer pour commencer la collecte de données, le widget Graphique KPI associé à l'appareil peut être créé dans la page Infos sur l'appareil.",
      DATAEXPORTDESCRIPTION: "Activez cette option pour exporter les indicateurs de performance vers un système de télémétrie tiers.",
      PRODUCTNOTEXIST: 'Le produit actuel n’existe pas',
      DEVICETEXIST: 'L’appareil existe déjà',
      DEVICEERRCODE1: 'le projet contient des chaînes illégales',
      DEVICEERRCODE2: "Delitem n'existe pas",
      DEVICEERRCODE3: "Cet appareil existe déjà dans le produit：",
      LABELORPATHEXISTS: "Nom de l'étiquette ou chemin de paramètre existe déjà",
      PARSINGFAILED: "L'analyse du fichier a échoué.",
      FILETYPEERROR: "Seuls les fichiers csv peuvent être téléchargés",
      EXPORTINITDEFAULTTOJSON: "Télécharger json (définir les valeurs par défaut)",
      EXPORTINITDEFAULTTOCSV: "Télécharger le CSV (exemple d'enregistrement en masse)",
      EDITPROVISIONINGDEFAULTVALUE: "Modifier définir les valeurs par défaut ",
      TAGS: 'Balises',
      LOCATION: 'Emplacement',
      REGISTERDEVICE: 'Enregistrer l\'appareil',
      NAME: 'Nom',
      NAMETOOLTIP: 'Entrer un nom de produit défini par l’utilisateur',
      NETWORKNAME: 'Entrer un nom de réseau défini par l’utilisateur',
      PICTURE: 'Image',
      UEFORPRODUCT: 'Nombre de UE par produit',
      OUI: 'Entrer le OUI correct conformément à l’appareil',
      DESCRIPTION: 'Description',
      NETWORKDESCRIPTION: 'Entrer les caractéristiques ou les attributs propriétaires des appareils appartenant au réseau',
      PRODUCTDESCRIPTION: 'Entrer les caractéristiques ou les attributs propriétaires des appareils appartenant au produit',
      PRODUCTCLASS: 'Entrer la classe de produit correcte conformément à l’appareil',
      PROCPELIMIT: 'Entrer la capacité du produit',
      NETCPELIMIT: 'Entrer la capacité du réseau',
      OUIDESCRIPTION: 'Le OUI doit être correct pour qu’AMP puisse vérifier l’appareil accédant',
      ALLOWALLDESCRIPTION: 'Le OUI et la classe de produit de l’appareil doivent être vérifiés',
      PROALLOWLISTDESCRIPTION: 'Le OUI, la classe de produit et le numéro de série de l’appareil doivent être vérifiés, l’enregistrement de l’appareil par numéro de série est requis après la création du produit',
      NETALLOWLISTDESCRIPTION: 'Le OUI, la classe de produit et le numéro de série de l’appareil doivent être vérifiés, l’enregistrement de l’appareil par numéro de série est requis après la création du réseau',
      PRODUCTCLASSDESCRIPTION: 'La classe de produit doit être correcte pour qu’AMP puisse vérifier l’appareil accédant. Ex: Femtocell_5G_SA, EAP, etc.',
      PRODEVICELIMITDESCRIPTION: 'Nombre de capacité des appareils dans le produit',
      NETDEVICELIMITDESCRIPTION: 'Nombre de capacité des appareils dans le réseau',
      PROVISIONINGTYPEDESCRIPTION: 'Le type d’approvisionnement doit être correct pour qu’AMP puisse vérifier l’appareil accédant'
    },
    ALARMS: {
      TOTAL: "Total d'alarmes",
      ALARMCOUNT: 'Compte Critique/Majeur/Mineur/Avertissement',
      CRITICAL: 'Critique',
      MAJOR: 'Majeur',
      WARNING: 'Avertissement',
      INDETERMINATE: 'Indéterminé',
      MINOR: 'Mineur',
      CURRENTNUMBERS: "Nombre d'alarmes actuelles",
      SYSTEM: 'Évènements du système',
      LIST: 'Liste des évènements du système',
      DEVICE: 'Alarmes de l’appareil',
      DEVICELIST: 'Liste des alarmes de l’appareil',
      ACKALARM: 'Acquitter l’alarme',
      ERRORLIST: "Liste d'erreurs de l'appareil",
      DEVICEEVENTTRACKING: "Suivi des événements de l'appareil",
      DEVICEEVENTTRACKINGDESCRIPTION: 'Liste des événements importants suivis par AMP liés aux appareils tels que le redémarrage, la mise hors ligne et la réinitialisation.',
      NOTIFICATION: 'Notification',
      NOTIFICATIONLIST: 'Liste des notifications',
      NOTIFICATIONLISTDESCRIPTION: 'Liste de règles créées par les utilisateurs pour recevoir des notifications via SMTP ou SNMP Trap dans des conditions spécifiques en fonction des besoins.',
      FORK: 'Fourchette',
      FORKNOTIFICATION: 'Notification fourchette',
      CLONE: 'Cloner',
      CLONENOTIFICATION: 'Cloner la notification',
      EDITNOTIFICATION: 'Modifier la notification',
      SETUPDETAILS: 'Détails (Détails de la configuration)',
      ENTERDETAILS: 'Entrez vos détails',
      GENERIC_TRAP_TYPE: "Type de piège générique",
      SPECIFIC_TRAP_OID: "OID de piège spécifique",
      VARIABLE_BINDINGS: 'Liens variables (facultatifs)',
      ALIASVALUE: 'Valeur d\'alias',
      OlDNAME: 'Ancien/Nom',
      VARIABLE_BINDINGS_DESCRIPTION: 'Les valeurs des varbinds sont associées à la liste d\'alias configurée dans l\'étape.',
      TARGET: 'Cible',
      EDITTARGET: 'Modifier la cible',
      SELECTTARGET: 'Sélectionner la cible',
      ENTERTARGET: 'Entrez votre cible',
      TARGETREQUIRED: 'Le nom du fichier cible est requis!',
      TARGETTYPE: 'Type de cible',
      TARGETDEVICESN: 'Numéro de série de l’appareil cible',
      ISTARGETDEVICESN: 'Ce numéro de série de l’appareil cible est requis!',
      SCHEDULE: 'Programme',
      EDITSCHEDULE: 'Modifier le programme',
      ENTERSCHEDULE: 'Entrez votre programme',
      SCHEDULEDOWNLOAD: 'Détails du téléchargement du programme',
      STARTDATE: 'Date de début',
      ENDDATE: 'Date de fin',
      STARTTIME: 'Heure de début',
      ENDTIME: 'Heure de fin',
      ENTEROPERATION: 'Entrez votre opération',
      TRIGGER: 'Type de déclencheur',
      SELECTTRIGGER: 'Sélectionnez le type de déclencheur',
      INFORMPARAM: 'Informer les paramètres',
      CONDITION: 'Condition',
      SELECTCONDITION: 'Sélectionner la condition',
      BUILDCONDITION: 'Construire la condition',
      PARAMCONDITION: 'Condition du paramètre',
      SELECTPARAMCONDITION: 'Sélectionner la condition du paramètre',
      ATTACHED: 'Messages attachés',
      ADDITIONALPARAMINFO: 'Informations supplémentaires sur les paramètres',
      ADDITIONALPARAMINFO_DESCRIPTION: 'Informations plus détaillées contenant une valeur de paramètre spécifique dans le message notifié.',
      NODE: 'Nœud',
      ENTERNODE: 'Entrez le nœud',
      SELECTNODE: 'Sélectionner le nœud',
      VIEWNODE: 'Voir le nœud NetConf',
      REFERNODE: 'Référencer le nœud',
      REFERNODEREQUIRED: 'Ce nœud de référence est requis!',
      PARENTNODE: 'Nœud parent',
      SELECTPARENTNODE: 'Sélectionner le nœud parent',
      CHILDNODE: 'Nœud enfant',
      ADDCHILDNODE: 'Ajouter un nœud enfant',
      SELECTCHILDNODE: 'Sélectionner le nœud enfant',
      CHILDCONTENT: 'Contenu enfant',
      CONTENT: 'Contenu',
      ENTERCONTENT: 'Entrez le contenu',
      CONFIG: 'Configuration',
      NAMESPACE: 'Espace de nom',
      ENTERNAMESPACE: 'Entrez l’espace de nom',
      ALIAS: 'Alias',
      ENTERALIAS: 'Entrez l’alias',
      ADDATTACHED: 'Ajouter un message attaché',
      ADDDEVICEFALUT: 'Ajouter des paramètres de défaut de l’appareil',
      SELECTDEVICEFAULTNAME: 'Sélectionner le nom du défaut de l’appareil',
      BUILD: 'Construire l’opération (Facultatif)',
      BUILDREQUIRED: 'Opération',
      PROGRESS: 'Progression',
      ACTIONS: 'Actions',
      REPEAT: 'Répéter',
      REPEATTYPE: 'Type de répétition',
      UPLOADNOTIFI: 'Télécharger les notifications d’alarmes',
      DROPFILE: 'Déposez les fichiers ici ou cliquez',
      UPLOADFORMATSARESUPPORTED: 'Prend uniquement en charge le téléchargement au format .tar .tar.gz .tgz .zip .gzip.',
      UPLOADALL: 'Télécharger tout',
      UPLOAURL: 'URL de téléchargement du journal de l’appareil',
      BANDWIDTH: 'Bande passante de téléchargement',
      QUEUEPROGRESS: 'Progression de la file d’attente',
      PARAMLIST: 'Liste des paramètres',
      SELECT: 'Sélectionner',
      ENTERSELECT: 'Entrez la sélection (expression XPath)',
      SOURCE: 'Source',
      SELECTSOURCE: 'Sélectionner la source',
      FILTERSTATE: 'État du filtre',
      SELECTFILTERSTATE: 'Sélectionner l’état du filtre',
      FILTERTYPE: "Type de filtre",
      SELECTFILTERTYPE: "Sélectionner le type de filtre",
      REMOVEALL: 'Tout retirer',
      REMOVE: 'Retirer',
      UPDATEUSER: 'Mettre à jour l’utilisateur',
      UPDATEDBY: 'Mis à jour par',
      LASTACTIVE: 'Dernière activité',
      UPDATELOG: 'Mettre à jour le journal',
      NOTIF: 'Notification',
      ONLYONCE: 'Une seule fois',
      ALLSTATE: 'Tous les états',
      ALLSEVERITY: 'Toutes les gravités',
      ALLGROUPS: 'Tous les groupes',
      SEVERITY: 'Gravité',
      STATE: 'État',
      CLEAREDTIME: 'Heure de réinitialisation',
      CLEARED: 'Effacé',
      NOTCLEARED: 'Non effacé',
      CLEAREDALARM: 'Alarme effacée',
      UNCLEAREDALARM: 'Alarme non effacé',
      CHANGEDALARM: 'Alarme modifiée',
      NEWALARM: 'Nouvelle alarme',
      NOTIFICATIONTYPE: "Type de notification",
      PROBABLECAUSE: "Cause probable",
      SPECIFICPROBLEM: "Problème spécifique",
      ADDITIONALTEXT: "Texte supplémentaire",
      ADDITIONALINFORMATION: "Informations supplémentaires",
      ALARMID: 'ID d’alarme',
      EVENTTYPE: 'Type d’évènement',
      EVENTTIME: 'Heure de l’évènement',
      ACKUSER: 'Utilisateur acquittant',
      ACKTIME: 'Heure d’acquittement',
      ERRORCODE: 'Événements',
      DEVICEFAULTPARAM: 'Paramètres de défaut de l’appareil',
      ATTACHEDMES: 'Message attaché',
      BYTAG: 'Par Tags',
      RECEIVERLIST: 'Liste des destinataires',
      RECEIVERCCLIST: 'Liste de destinataires en copie conforme',
      RECEIVETAGSTOOLTIP: "Ajouter des destinataires avec des tags spécifiques",
      RECEIVERCCTAGSTOOLTIP: 'Ajouter un CC avec des tags spécifiques',
      EMAILSUBJECT: 'Sujet de l’email',
      EMAILCONTENT: 'Contenu de l’email',
      WITHACTIVEHOURS: 'Avec des heures actives',
      PRIPHONENUM: 'Numéro de téléphone principal',
      SECPHONENUM: 'Numéro de téléphone secondaire',
      TEXTMESSAGE: 'Message texte',
      ACK: 'Confirmer',
      EDIT_STAGE_NOTIFICATIONS: 'Construire une notification avec étape / notifications',
      TOTALALARMSDESCRIPTION: 'Nombre total d\'alarmes signalées par le système.',
      ALARMMGMT: 'Gestion des alarmes',
      ALARMMGMTDESCRIPTION: 'Liste de toutes les alarmes signalées par le système, y compris les alarmes effacées et non effacées, avec la gravité, l\'heure de l\'événement et la cause probable.',
      ALARMCOUNTDESCRIPTION: 'Nombre total d’alarmes de différentes gravités telles que Critique, Majeure, Mineure et Avertissement.',
      TARGETDEVICETAG: "Balises de l'appareil cible",
    },
    PROVISIONING: {
      COLLAPSIBLE: 'Provisionnement',
      WORKSFLOW: 'Flux de travail',
      WORKSFLOWLIST: 'Liste des flux de travail',
      WORKSFLOWLISTDESCRIPTION: 'Liste des règles créées par les utilisateurs pour effectuer des opérations sur des appareils spécifiques selon des conditions spécifiques basées sur des exigences.',
      CONFIGURATIONS: 'Configuration',
      CONFIGURATIONLIST: 'Liste de configuration',
      CONFIGURATIONLISTDESCRIPTION: 'Liste des règles créées automatiquement ou par les utilisateurs pour effectuer des configurations de provisionnement sur des appareils sous des conditions spécifiques en fonction des besoins.',
      POLICYLIST: 'Politique énergétique',
      POLICYLISTDESCRIPTION: "Listez les règles d'économie d'énergie créées automatiquement. Ces règles effectuent des réglages de configuration sur les appareils lorsque le mode économie d'énergie est activé.",
      CLONEPOLICY: 'Politique de clonage',
      POLICYS: 'Politiques',
      FROMWORKSFLOW: 'Du flux de travail',
      FROM: 'De',
      VALIDFROM: 'Valide à partir de',
      CLONEWORKSFLOW: 'Cloner le flux de travail',
      CLONECONFIGURATION: 'Configuration du clone',
      EDITWORKSFLOW: 'Éditer le flux de travail',
      FORKWORKSFLOW: 'Dériver le flux de travail',
      UPLOADWORKSFLOW: 'Charger le flux de travail',
      UPLOADQUEUE: 'File d’upload',
      OPERATIONS: 'Opérations',
      PROFILES: 'Profils',
      ACTIONS: 'Actions',
      CLONEOPERATIONS: "Cloner des profils",
      FORKOPERATIONS: 'Dériver l’opération',
      EDITOPERATIONS: 'Éditer les opérations',
      OPERATIONLIST: 'Liste des profils',
      OPERATIONLISTDESCRIPTION: 'Liste des règles créées par les utilisateurs pour effectuer des opérations sur des appareils spécifiques selon des conditions spécifiques basées sur des exigences.',
      DUOPERATION: 'Ajouter opération DU',
      PARAPATH: 'Chemin du paramètre',
      ENTERPARAPATH: 'Entrer le chemin du paramètre',
      ISPARAPATH: 'Le chemin du paramètre est requis.',
      NEXTLEVEL: 'Niveau suivant',
      PRODUCT: 'Produit',
      SCRIPTS: 'Scripts',
      SCRIPTLIST: 'Liste des scripts',
      EDITSCRIPT: 'Éditer le script',
      SCRIPTNAME: 'Nom du script',
      FILES: 'Fichiers',
      FILELIST: 'Liste des fichiers',
      FILELISTDESCRIPTION: 'Liste créée par les utilisateurs, contenant les informations nécessaires lorsque des appareils effectuent des téléchargements ou des téléchargements de fichiers, telles que le type de fichier, l’URL et l’authentification.',
      FILETYPE: 'Type de fichier',
      SELECTFILETYPE: 'Sélectionner le type de fichier',
      ENTERFILETYPE: 'Entrer le type de fichier',
      ISFILETYPE: 'Le type de fichier est requis!',
      ISURL: 'L’URL est requis!',
      DELAYSECONDS: 'Délai en secondes',
      TARGETNAME: 'Nom du fichier cible',
      ENTERTARGETNAME: 'Entrer le nom du fichier cible',
      FILESIZE: 'Taille du fichier',
      DESCRIPTION: 'Description',
      SUBSCRIBE: 'S’abonner',
      SUBSCRIBETOPIC: 'Sujet d’abonnement',
      SELECTTOPIC: 'Sélectionner le sujet d’abonnement',
      VENDORFILE: 'Fichiers spécifiques au fournisseur',
      VENDORFILEDESCRIPTION: 'Liste créée par les utilisateurs, contenant des informations spécifiques au fournisseur lorsque des appareils effectuent des téléchargements ou des téléchargements de fichiers, telles que le type de fichier, l’URL et l’authentification.',
      ADDVENDORFILE: 'Ajouter des fichiers spécifiques au fournisseur',
      EDITVENDORFILE: 'Éditer les fichiers spécifiques au fournisseur',
      LATESTFIRMWARE: 'Dernier firmware',
      AVAILABLEFILES: 'Fichiers disponibles',
      SETUPDETAILS: 'Détails de configuration',
      CODEDISTRIBUTION: 'Distribution du code de provisionnement',
      OPERATENAME: 'Nom de l’opération',
      ENTEROPERATENAME: 'Entrer le nom de l’opération',
      ADDINFORM: 'Ajouter un informateur de paramètre',
      PUBLICTOPIC: 'Sujet public',
      SELECTPUBLICTOPIC: 'Sélectionner le sujet public',
      ISDATAMODEL: 'Ce modèle de données est requis!',
      CPELIMIT: 'Limites CPE',
      ISCPELIMIT: 'La limite CPE est requise!',
      SUMMARYACTION: 'Résumé de l’action',
      ADDSUMMARYREPORT: 'Ajouter un rapport résumé',
      SUMMARYREPORT: 'Rapport résumé',
      SUMMARYREPORTSETTING: 'Configuration du rapport résumé',
      PLEASESELECTTEMPLATE: 'Veuillez sélectionner un modèle avant de continuer',
      PLEASEFILLPARAMS: "Veuillez remplir le nom de l'étiquette et le chemin des paramètres avant de continuer",
      INFORMLIST: 'Liste des informateurs de paramètre',
      SELECTTRIGGERREQ: 'Sélectionner le déclencheur (Requis)',
      DEVICEPARAMLIST: 'Liste des paramètres de l’appareil',
      ADDSTAGE: 'Étape',
      ENTERSTAGENAME: 'Entrer le nom de l’étape',
      SELECTFILE: 'Sélectionner le fichier',
      SUCCESSURL: 'URL de succès',
      FAILURL: 'URL d’échec',
      NOTIFYTYPE: 'Type de notification',
      SELECTNOTIFYTYPE: 'Sélectionner le type de notification',
      NOTIFYPARAMS: 'Paramètres de notification',
      SHOWDETAILS: 'afficher les détails',
      NOTIFYTYPEREQU: 'Sélectionner le type de notification (Requis)',
      EDITNOTIFYPARA: 'Éditer les paramètres de notification',
      OBJECTPATH: 'Chemin de l’objet',
      ENTEROBJECTPATH: 'Entrer le chemin de l’objet',
      ALLOWPAR: 'Autoriser partiel',
      ADDCREATEOBJ: 'Ajouter un objet à créer',
      ISOBJECTNAME: 'Le nom de l’objet est requis!',
      FILETARGET: 'Cible du fichier',
      SELECTSN: 'Sélectionner SN',
      GENERSUMMARYREPORT: 'Générer un rapport résumé',
      SCRIPT: 'Script',
      SELECTSCRIPT: 'Sélectionner le script',
      ACTIONSLIST: 'Liste des actions',
      PARAMTYPE: 'Type de paramètre',
      ADDITIONALCON: 'Conditions de déclenchement',
      ADDCONDITION: 'Ajouter une condition supplémentaire',
      EDITCONDITION: 'Éditer la condition supplémentaire',
      DEVICEPARAMTRIGGER: 'Paramètre de Déclenchement de l’Appareil',
      INFORM: 'Informer',
      DURATION: 'Durée',
      FIELD: 'Champ',
      TRIGGEREVENTS: 'Événements déclencheurs',
      TRIGGERTYPE: 'Type de déclencheur',
      INFORMEVENT: 'Événement d’information',
      SELECTINFORMEVENT: 'Sélectionner l’événement d’information',
      EVENTNAME: 'Nom de l’événement',
      ENTEREVENTNAME: 'Entrer le nom de l’événement',
      PARAMETERSKEY: 'Clé du paramètre',
      ENTERPARAMETERSKEY: 'Entrer la clé du paramètre',
      PARAMETERSVALUE: 'Valeur du paramètre',
      ENTERPARAMETERSVALUE: 'Entrer la valeur du paramètre',
      PROTOCOLVER: 'Versions du protocole',
      SOURCEURL: 'URL source',
      TARGETURL: 'URL cible',
      SESSIONID: 'ID de session',
      OPERATEMODE: 'Mode d’opération',
      SELECTOPERATEMODE: 'Sélectionner le mode d’opération',
      INPUTURLFORMATE: 'Formats supportés: http / https / ftp / ftps / sftp',
      HISTORY: 'Histoire',
      WORKFLOWHISTORY: 'Historique du flux de travail',
      CONFIGURATIONHISTORY: 'Historique de configuration',
      POLICYHISTORY: 'Historique des stratégies',
      TRIGGERTIME: "Temps de déclenchement",
      TARGETPRODUCT: 'Produit cible',
      TARGETGROUP: 'Groupe ciblé',
      TARGETSOFTWAREVERSION: 'Version du logiciel cible',
      TARGETSN: 'Numéro de série du périphérique cible',
      TARGETSV: 'Version du logiciel de l’appareil cible',
      SUPPORTEDPRODUCT: 'Produit pris en charge',
      MANAGEDEDPRODUCT: 'Produit géré',
      ALWAYSACTIVE: "Toujours actif",
      SELECTACTIVEDATERANGE: "Sélectionner la plage de dates et d'heures active",
      DAYOFWEEK: 'Avec jour de la semaine',
      WITHONLYONCE: 'Avec une seule fois',
      ACTIVEDATERANGE: 'Plage de dates active',
      ACTIVETIMERANGE: 'Plage de temps d’activité en jour',
      EVERYDAY: 'Tous les jours',
      SUNDAY: 'Dimanche',
      MONDAY: 'Lundi',
      TUESDAY: 'Mardi',
      WEDNESDAY: 'Mercredi',
      THURSDAY: 'Jeudi',
      FRIDAY: 'Vendredi',
      SATURDAY: 'Samedi',
      EXECUTIONSTATUS: "État d'exécution",
      EXECUTIONTIME: "Temps d'exécution",
      EDITACTIONS: 'Modifier les actions',
      DOWNLOADASMULTI: 'Télécharger sous forme de fichiers multiples',
      DOWNLOADASONE: 'Télécharger en un seul fichier',
      LASTEXECUTIONTIME: 'Heure de la dernière exécution',
      SEARCHSN: 'Rechercher SN',
      ACTIVATE: 'Activer',
      DEACTIVATE: 'Désactiver',
      LOADING: 'Chargement',
      STATETYPE: 'Type de statut',
      STAGE: 'Étape',
      EDIT_STAGE_OPERATIONS_DESCRIPTION: 'Construire le flux de travail avec l\'étape / opérations',
      EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION: 'Construire une configuration avec l\'étape / opérations',
      EDIT_POLICY_STAGE_OPERATIONS_DESCRIPTION: 'Élaborer une politique avec Stage/Opérations',
      TRIGGERCONDITIONS: 'Conditions de déclenchement',
      BUILD: 'Construire',
      SETUP_SCHEDULE_DESCRIPTION: 'Configurer la plage horaire active',
      INVALID_VALUE_MESSAGE: 'Il existe une valeur invalide, vérifiez le formulaire.',
      RESET_TOOLTIP: 'Réinitialiser à la valeur par défaut',
      RESET_CONFIRM: 'Voulez - vous réinitialiser toutes les valeurs de configuration initiales?',
      COUNT: "Nombre d'exécutions",
      COMPLETEDCOUNT: 'Terminé',
      PARTIALFAILEDCOUNT: 'Partiellement échoué',
      CANCELEDCOUNT: 'Annulation',
      INPROGRESS: 'Dans le pot',
      FAILCOUNT: "L'échec",
      ADDTAGFAIL: 'Échec de l\'ajout des étiquettes!',
      ADDTAGSUCC: 'Ajout des étiquettes réussi!',
      DELTAGFAIL: 'Échec de la suppression des étiquettes!',
      DELTAGSUCC: 'Suppression des étiquettes réussie!',
      STEPAFTERSUCCESS: "En cas d'échec, arrêter les tâches suivantes",
      WORKFLOWOPERATIONLOG: 'Journal des opérations du workflow',
      OPERATIONLOGS: 'Journal des opérations'
    },
    USERS: {
      ACCOUNT: 'Comptes',
      ONLINEUSERS: 'Utilisateurs en ligne',
      ONLINEUSERSDESCRIPTION: "Le nombre total d'utilisateurs qui sont actuellement en usage ou qui se sont connectés dans la dernière demi-heure",
      PROFILE: 'Profil',
      PROFILEDESCRIPTION: 'Informations détaillées sur l’utilisateur actuellement connecté.',
      STATUS: 'Statut',
      ALLSTATUS: 'Tous les statuts',
      ALLTYPE: 'Tous les types',
      ROLE: 'Rôle',
      ROLELIST: 'Liste des rôles',
      ROLELISTDESCRIPTION: 'Liste des rôles utilisés pour le contrôle des autorisations des utilisateurs.',
      CANNOTFINDROLE: "Impossible de trouver la liste des autorisations de rôle",
      CHANGE: 'Modifier',
      ACCOUNTLIST: 'Liste des comptes',
      ACCOUNTLISTDESCRIPTION: 'Liste des comptes que l’utilisateur actuellement connecté peut gérer.',
      EDITUSER: 'Modifier l’utilisateur',
      EXPIRATION: 'Date d’expiration',
      DEPLOYEXPIRATION: 'Expiration du déploiement',
      CONTROLLIST: 'Liste de contrôle d’accès',
      ALLEDITABLE: 'Tous modifiables',
      EDITABLE: 'Modifiable',
      ALLREADONLY: 'Tous en lecture seule',
      READONLY: 'Lecture seule',
      ALLDISABLED: 'Tous handicapés',
      DISABLED: 'Handicapé',
      SUBMIT: 'Soumettre',
      AMPNODE: 'Nœud AMP',
      SUPERADMINPASSWORD: 'Super mot de passe Admin',
      ACTIVITIES: 'Journal des comptes',
      ACTIVITIESLIST: 'Liste des activités des comptes',
      ACTIVITIESLISTDESCRIPTION: 'Consigner tous les événements de demande pour les comptes que l’utilisateur actuellement connecté peut gérer.',
      DATERANGE: 'Sélectionner la plage de dates',
      BEGINTIMEDATERANGE: 'Sélectionner la date de début',
      ENDTIMEDATERANGE: 'Sélectionner la date de fin',
      DASHPERMISSION: 'Permission du tableau de bord',
      CONFIRMPASSWORD: 'Confirmer le mot de passe',
      NOTMATCH: 'Le mot de passe et la confirmation du mot de passe ne correspondent pas.',
      NOTMATCH2: 'Le nouveau mot de passe et la confirmation du mot de passe ne correspondent pas.',
      ISPASSWORD: 'Le mot de passe est requis.',
      ISUSERNAME: 'Le nom d’utilisateur est obligatoire.',
      ADDUSER: 'Ajouter un utilisateur',
      USERROLE: 'Rôle de l’utilisateur',
      CONFIRMPSW: 'Les mots de passe doivent comporter de 8 à 128 caractères et contenir au moins les éléments suivants : lettres, chiffres et symboles.',
      SPECIALSYMBOLS: 'Seuls 1-32 caractères de lettres, chiffres et symboles spéciaux (@ ! # ? $ / \ _ - .) sont autorisés.',
      SPECIALSYMBOLS_NO_DASH: 'Seuls 1-128 caractères de lettres, chiffres et symboles spéciaux (@ ! # ? $ / \ _ .) sont autorisés.',
      ADDNEWUSER: 'Ajouter un nouvel utilisateur',
      USERACTION: 'Action de l’utilisateur',
      LANGUAGE: 'Langue',
      AUTHORITYLIST: 'Liste d\'autorité de la classe Widget',
      CHANGEPASSWORD: 'Changer le mot de passe',
      APIDOCUMENT: 'Document API',
      USERMANUAL: 'Manuel de l’utilisateur',
      OLDPASSWORD: 'Ancien mot de passe',
      NEWPASSWORD: 'Nouveau mot de passe',
      PREVIOUSTIME: 'Dernière heure de connexion',
      PREVIOUSLOCATION: 'Dernier emplacement de connexion',
      LASTLOGINLOCATION: 'Dernier lieu de connexion',
      CURRENTTIME: 'Heure actuelle de connexion',
      CURRENTLOCATION: 'Emplacement actuel de connexion',
      EDITROLE: 'Modifier le rôle',
      ADDNEWROLE: 'Ajouter un nouveau rôle',
      AUTHORITY: 'Modèle de rôle',
      EMAIL: 'E-mail',
      ACTIONTYPE: 'Type d’action',
      EMAILERROR: 'Format incorrect de la boîte E-mail',
      ISMAIL: 'E-mail est nécessaire.',
      TAGHASCREATED: "Ce tag a été créé par un autre utilisateur",
      DEVICEADMIN_TITLE: 'Administration des appareils',
      DEVICEADMIN_DESCRIPTION: 'Autorité pour modifier/lire les widgets liés aux appareils classés dans la classe Appareil/sous-classe Admin appareils. Widgets inclus : Liste des appareils, Mise à jour en direct, Redémarrage, Mise à niveau du firmware.',
      PRODUCTADMIN_TITLE: 'Administration des produits',
      PRODUCTADMIN_DESCRIPTION: 'Autorité pour modifier/lire les widgets liés aux produits classés dans la classe Appareil/sous-classe Admin produits. Widgets inclus : Appareils enregistrés, Liste des produits.',
      GROUPADMIN_TITLE: 'Administration des groupes',
      GROUPADMIN_DESCRIPTION: 'Autorité pour modifier/lire les widgets liés aux groupes classés dans la classe Appareil/sous-classe Admin groupes. Widgets inclus : Liste des groupes, Ajouter un appareil au groupe, Ajouter une opération.',
      GENERALDATA_TITLE: 'Données générales',
      GENERALDATA_DESCRIPTION: 'Autorité pour modifier/lire les widgets de données générales des appareils classés dans la classe Appareil/sous-classe Données générales. Widgets inclus : Appareils en ligne, Groupes, Modèle de produit, Informations générales, KPI.',
      ALARMMANAGEMENT_TITLE: 'Gestion des alarmes',
      ALARMMANAGEMENT_DESCRIPTION: 'Autorité pour modifier/lire les widgets de gestion des alarmes sur un appareil spécifique classés dans la classe Appareil/sous-classe Gestion des alarmes. Widget inclus : Gestion des alarmes.',
      REMOTETROUBLESHOOTING_TITLE: 'Dépannage à distance',
      REMOTETROUBLESHOOTING_DESCRIPTION: 'Autorité pour modifier/lire les widgets avancés de dépannage à distance classés dans la classe Appareil/sous-classe Dépannage à distance. Widgets inclus : Terminal, Commande XML.',
      DATAMODEL_TITLE: 'Modèle de données',
      DATAMODEL_DESCRIPTION: 'Autorité pour modifier/lire les widgets liés au modèle de données classés dans la classe Appareil/sous-classe Modèle de données. Widget inclus : Noeud de données, Données de paramètres.',
      NETWORKLOCATION_TITLE: 'Emplacement réseau',
      NETWORKLOCATION_DESCRIPTION: 'Autorité pour modifier/lire les widgets liés à l\'emplacement réseau classés dans la classe Appareil/sous-classe Emplacement réseau. Widgets inclus : Emplacement, Topologie réseau, Carte de couverture.',
      LOGCOLLECTION_TITLE: 'Collecte de journaux',
      LOGCOLLECTION_DESCRIPTION: 'Autorité pour modifier/lire les widgets de collecte de journaux classés dans la classe Appareil/sous-classe Collecte de journaux. Widgets inclus : Liste des journaux de session, Générer un rapport, Liste des journaux d\'opérations.',
      STATISTICALANALYSIS_TITLE: 'Analyse statistique',
      STATISTICALANALYSIS_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'analyse statistique classés dans la classe Appareil/sous-classe Analyse statistique.',
      WIFISPECIFIC_TITLE: 'WiFi spécifique',
      WIFISPECIFIC_DESCRIPTION: 'Autorité pour modifier/lire les widgets WiFi spécifiques classés dans la classe Appareil/sous-classe WiFi spécifique. Widgets inclus : Clients WiFi, Statut de la radio WiFi, Analyseur WiFi, Liste des voisins WiFi.',
      CELLULARSPECIFIC_TITLE: 'Cellulaire spécifique',
      CELLULARSPECIFIC_DESCRIPTION: 'Autorité pour modifier/lire les widgets cellulaires spécifiques classés dans la classe Appareil/sous-classe Cellulaire spécifique. Widgets inclus : UE, État de la cellule, Faisceau d\'antennes, Liste des voisins/PLMN.',
      PMKPICOUNTER_TITLE: 'PM KPI compteur',
      PMKPICOUNTER_DESCRIPTION: 'Autorisations pour modifier/lire le Widget PM KPI Counter classé dans la Sous - classe device class/PM KPI Counter. Widgets inclus: diagramme PM, paramètres pm.',
      FAPSPECIFIC_TITLE: 'FAP spécifique',
      FAPSPECIFIC_DESCRIPTION: 'Autorité pour modifier/lire les widgets FAP spécifiques classés dans la classe Appareil/sous-classe FAP spécifique.',
      APPSPECIFIC_TITLE: 'App spécifique',
      APPSPECIFIC_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'applications spécifiques classés dans la classe Appareil/sous-classe App spécifique. Widgets inclus : Liste des applications, Fournisseur de services, État des applications de l\'appareil.',
      YANGMODULE_TITLE: 'Module Yang',
      YANGMODULE_DESCRIPTION: 'Autorité pour modifier/lire les widgets de module Yang classés dans la classe Appareil/sous-classe Module Yang.',
      POWERSAVING_TITLE: "Économie d'énergie",
      POWERSAVING_DESCRIPTION: "Autorisations pour modifier/lire le Widget Économie d'énergie classé dans la Sous - classe device class/Économie d'énergie. Widgets inclus: Gestion de l'énergie.",
      DOCSIS_TITLE: 'Docsis Spécifique',
      DOCSIS_DESCRIPTION: 'Autorisations pour modifier/lire le Widget Docsis Spécifique classé dans la Sous - classe device class/Docsis Spécifique. Widgets inclus: Statut DOCSIS.',
      DEVICEALARM_TITLE: 'Alarme d\'appareil',
      DEVICEALARM_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'alarme d\'appareil classés dans la classe Alarme/sous-classe Alarme d\'appareil. Widgets inclus : Alarmes totales, Gestion des alarmes (Téléchargement/Acknowledgement), Suivi des événements de l\'appareil.',
      NOTIFICATIONMANAGMENT_TITLE: 'Gestion des notifications',
      NOTIFICATIONMANAGMENT_DESCRIPTION: 'Autorité pour modifier/lire les widgets de gestion des notifications classés dans la classe Alarme/sous-classe Gestion des notifications. Widget inclus : Liste des notifications',
      WORKFLOWSETUP_TITLE: 'Configuration du workflow',
      WORKFLOWSETUP_DESCRIPTION: 'Modifier/lire les autorisations du Widget Workflow/configuration des paramètres classées dans la Sous - classe configuration class/Workflow settings. Widgets inclus: liste de flux de travail/configuration, historique de flux de travail/configuration.',
      OPERATIONSETUP_TITLE: 'Configuration des opérations',
      OPERATIONSETUP_DESCRIPTION: 'Autorité pour modifier/lire les widgets de configuration des opérations classés dans la classe Provisionnement/sous-classe Configuration des opérations. Widget inclus : Liste des actions.',
      POLICYSETUP_TITLE: 'Configuration de la politique',
      POLICYSETUP_DESCRIPTION: 'Autorité pour modifier/lire les widgets de configuration de la politique classés dans la classe Provisionnement/sous-classe Configuration de la politique. Widget inclus : Politique énergétique.',
      SCRIPTSETUP_TITLE: 'Configuration des scripts',
      SCRIPTSETUP_DESCRIPTION: 'Autorité pour modifier/lire les widgets de configuration des scripts.',
      FILESETUP_TITLE: 'Configuration des fichiers',
      FILESETUP_DESCRIPTION: 'Autorité pour modifier/lire les widgets de configuration des fichiers classés dans la classe Provisionnement/sous-classe Configuration des fichiers. Widgets inclus : Liste des fichiers, Fichiers spécifiques au fournisseur.',
      ACCOUNTADMIN_TITLE: 'Administration des comptes',
      ACCOUNTADMIN_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'administration des comptes classés dans la classe Utilisateur/sous-classe Administration des comptes. Widgets inclus : Profil, Liste des comptes.',
      ACCOUNTLOG_TITLE: 'Journal des comptes',
      ACCOUNTLOG_DESCRIPTION: 'Autorité pour modifier/lire les widgets de journal des comptes classés dans la classe Utilisateur/sous-classe Journal des comptes. Widget inclus : Liste des activités.',
      ACCOUNTROLE_TITLE: 'Rôle des comptes',
      ACCOUNTROLE_DESCRIPTION: 'Autorité pour modifier/lire les widgets de rôle des comptes classés dans la classe Utilisateur/sous-classe Rôle des comptes. Widget inclus : Liste des rôles.',
      DEVICESTATISTICS_TITLE: 'Statistiques des appareils',
      DEVICESTATISTICS_DESCRIPTION: 'Autorité pour modifier/lire les widgets de statistiques des appareils classés dans la classe Analyse/sous-classe Statistiques des appareils. Widgets inclus : Appareils en ligne, Nouveaux appareils, Code d\'événement.',
      SYSTEMSTATISTICS_TITLE: 'Statistiques système',
      SYSTEMSTATISTICS_DESCRIPTION: 'Autorité pour modifier/lire les widgets de statistiques système classés dans la classe Analyse/sous-classe Statistiques système. Widgets inclus : État de la base de données, Durée des sessions des appareils, Taux de sessions des appareils, Mémoire libre.',
      PROVISIONINGSTATISTICS_TITLE: 'Statistiques de provisionnement',
      PROVISIONINGSTATISTICS_DESCRIPTION: 'Autorité pour modifier/lire les widgets de statistiques de provisionnement classés dans la classe Analyse/sous-classe Statistiques de provisionnement. Widgets inclus : Code de provisionnement, Version du logiciel, État de la SIM.',
      PMSTATISTICS_TITLE: 'Statistiques PM',
      PMSTATISTICS_DESCRIPTION: 'Autorité pour modifier/lire les widgets de statistiques PM classés dans la classe Analyse/sous-classe Statistiques PM. Widgets inclus : PM, État de PM, Rapport de performance.',
      SERVERSETTING_TITLE: 'Paramètres du serveur',
      SERVERSETTING_DESCRIPTION: 'Autorité pour modifier/lire les widgets de paramètres du serveur classés dans la classe Système/sous-classe Paramètres du serveur. Widgets inclus : Général, Mise à jour en direct, CWMP, Netconf, Service de performance.',
      SERVERPREFERENCE_TITLE: 'Préférences du serveur',
      SERVERPREFERENCE_DESCRIPTION: 'Autorité pour modifier/lire les widgets de préférences du serveur classés dans la classe Système/sous-classe Préférences du serveur. Widgets inclus : Notifications SMTP, Notifications SNMP Trap, Rapports, Statistiques, Journaux.',
      SERVERLICENSE_TITLE: 'Licence du serveur',
      SERVERLICENSE_DESCRIPTION: 'Autorité pour modifier/lire les widgets de licence du serveur classés dans la classe Système/sous-classe Licence du serveur. Widget inclus : Licence.',
      SERVERREPORT_TITLE: 'Rapport du serveur',
      SERVERREPORT_DESCRIPTION: 'Autorité pour générer/lire les widgets de rapport du serveur classés dans la classe Complément/sous-classe Exportation de rapport. Widget inclus : Générer un rapport récapitulatif.',
      SYSTEMEVENTS_TITLE: 'Événements système',
      SYSTEMEVENTS_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'événements système classés dans la classe Système/sous-classe Événements système. Widget inclus : Événements système, Journal d\'inscription.',
      SYSTEMNODES_TITLE: 'Nœuds système',
      SYSTEMNODES_DESCRIPTION: 'Autorité pour modifier/lire les widgets de nœuds système classés dans la classe Système/sous-classe Nœuds système. Widget inclus : Nœuds.',
      PERSONALTHEME_TITLE: 'Thème personnel',
      PERSONALTHEME_DESCRIPTION: 'Autorité pour modifier/lire les widgets de thème personnel classés dans la classe Complément/sous-classe Thème personnel.',
      REPORTEXPORT_TITLE: 'Exportation de rapport',
      REPORTEXPORT_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'exportation de rapport classés dans la classe Complément/sous-classe Exportation de rapport. Widget inclus : Générer un rapport récapitulatif.',
      SYSTEMINFORMATION_TITLE: 'Informations système',
      SYSTEMINFORMATION_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'informations système AMP classés dans la classe Système/sous-classe Informations système. Widget inclus : Informations système.',
      FURBISHMENTSTATISTICS_TITLE: '翻新统计',
      GENERAL_TITLE: 'Général',
      GENERAL_DESCRIPTION: 'Autorité pour modifier/lire les widgets généraux classés dans la classe 5G Core/sous-classe générale.',
      UE_TITLE: 'UE',
      UE_DESCRIPTION: 'Autorité pour modifier/lire les widgets UE classés dans la classe 5G Core/sous-classe UE.',
      CELL_TITLE: 'Cellule',
      CELL_DESCRIPTION: 'Autorité pour modifier/lire les widgets Cellule classés dans la classe 5G Core/sous-classe Cellule.',
      ALARM_TITLE: 'Alarme',
      ALARM_DESCRIPTION: 'Autorité pour modifier/lire les widgets d\'alarme classés dans la classe 5G Core/sous-classe Alarme.',
      SYSTEM_TITLE: "Système",
      SYSTEM_DESCRIPTION: "Autorisation de modifier/lire les actions et widgets liés au système 5GC.",
      TOTALREQUESTS_TITLE: 'Demandes Totales',
      TOTALREQUESTS_DESCRIPTION: 'Nombre de demandes totales au cours des dernières 24 heures',
      EXTERNAL_DEVICE_TITLE: 'Équipement',
      EXTERNAL_DEVICE_DESCRIPTION: 'Autorisations pour modifier/lire les autorisations NBI de la classe Externe/sous-classe de Équipement.',
      CBSD_DEVICE_TITLE: 'Équipement',
      CBSD_DEVICE_DESCRIPTION: 'Autorisations pour modifier/lire les autorisations NBI de la classe CBSD/sous-classe de Équipement.',
      REQUESTSHISTORY_TITLE: "Historique des demandes",
      REQUESTSHISTORY_DESCRIPTION: "Graphique historique du total des demandes au cours des dernières 24 heures",
      IPREQUESTDISTRIBUTION: "carte de répartition des requêtes IP",
      IPREQUESTDISTRIBUTION_DESCRIPTION: "Les 5 IP ayant effectué le plus de requêtes au cours des dernières 24 heures",
    },
    ANALYSIS: {
      COLLAPSIBLE: 'Analyse',
      SYSTEM: 'Statistiques Système',
      SESSIONDURATION: 'Durée des Sessions de Dispositifs',
      SESSIONRATE: 'Taux des Sessions de Dispositifs',
      LATENCY: 'Latence des Requêtes de Dispositifs',
      REQUESTRATE: 'Taux des Requêtes de Dispositifs',
      PARSING: 'Analyse des Requêtes de Dispositifs',
      MEMORY: 'Utilisation Mémoire',
      SPACEUSAGE: 'Utilisation de l\'Espace',
      CPUUTILIZE: 'Utilisation du CPU',
      MEMORYUSAGECHART: 'Graphique d\'Utilisation Mémoire',
      FREEMEMORY: 'Mémoire Libre',
      CPUUSAGE: 'Utilisation CPU',
      CPUUSAGECHART: 'Graphique d\'Utilisation CPU',
      FREEDISK: 'Disque Libre',
      DEVICE: 'Statistiques des Dispositifs',
      PM: 'Statistiques PM',
      TOTALDEVICE: 'Nombre Total de Dispositifs',
      NEWDEVICE: 'Nouveaux Dispositifs',
      SESSIONS: 'Sessions',
      EVENTCODE: 'Code d\'Événement',
      MEMORYUTILIZATION: 'Utilisation Mémoire',
      DISKUTILIZATION: 'Utilisation du Disque',
      MEMORYUTILIZATIONDESCRIPTION: 'Graphique Historique d\'Utilisation Mémoire.',
      DISKUTILIZATIONDESCRIPTION: 'Graphique Historique d\'Utilisation du Disque.',
      SESSIONDURATIONDESCRIPTION: 'Graphique historique de la durée moyenne des sessions pour tous les dispositifs à intervalles réguliers. Durée de la session : Le temps total passé dans une session entre le dispositif et AMP.',
      SESSIONRATEDESCRIPTION: 'Graphique historique du taux moyen des sessions pour tous les dispositifs à intervalles réguliers. Taux de session : Le nombre de sessions initiées par le dispositif vers AMP par seconde.',
      LATENCYDESCRIPTION: 'Graphique historique de la latence moyenne des requêtes pour tous les dispositifs à intervalles réguliers. Latence des requêtes : Le temps total passé dans une requête entre le dispositif et AMP.',
      REQUESTRATEDESCRIPTION: 'Graphique historique du taux moyen des requêtes pour tous les dispositifs à intervalles réguliers. Taux de requêtes : Le nombre de requêtes initiées par le dispositif vers AMP par seconde.',
      PARSINGDESCRIPTION: 'Graphique historique du temps moyen de parsing des requêtes pour tous les dispositifs à intervalles réguliers. Parsing des requêtes : Le temps total passé pour qu\'AMP analyse une requête initiée par le dispositif.',
      MEMORYDESCRIPTION: 'Graphique Historique de la Mémoire Libre.',
      CPUUSAGEDESCRIPTION: 'Graphique Historique d\'Utilisation du CPU.',
      FREEDISKDESCRIPTION: 'Graphique Historique de l\'Espace Libre sur le Disque.',
      TOTALDEVICEDESCRIPTION: 'Graphique historique du nombre total de dispositifs à intervalles réguliers.',
      ONLINEDEVICEDESCRIPTION: 'Graphique historique du nombre total de dispositifs en ligne à intervalles réguliers.',
      NEWDEVICEDESCRIPTION: 'Graphique historique du nombre total de nouveaux dispositifs enregistrés à intervalles réguliers.',
      SESSIONSDESCRIPTION: 'Graphique historique du nombre total de sessions pour chaque produit à intervalles réguliers.',
      EVENTCODEDESCRIPTION: 'Graphique historique du nombre total de codes d\'événements pour chaque produit à intervalles réguliers.',
      PROVISIONING: 'Statistiques de Provisioning',
      PMSTATISTICS: 'Statistiques PM',
      STATUSFORDEVICES: 'Statut des Dispositifs en Ligne',
      RATE: 'Taux',
      NUMBER: 'Nombre',
      VERSIONDISTRIBUTION: 'Répartition des Versions Logicielles',
      CODEDISTRIBUTION: 'Répartition des Codes de Provisioning',
      XMPPSTATUS: 'Statut XMPP',
      XMPPSTATUS_DESCRIPTION: 'Statut du service XMPP.',
      IMSSTATUS: 'Statut d\'Enregistrement IMS',
      SIMSTATUS: 'Statut de la SIM',
      IPSECSTATUS: 'Statut du Tunnel IPSec',
      STATUSFORDEVICE: 'Statut des Dispositifs Totals',
      DBSTATUS: 'Statut de la Base de Données',
      DBSTATUS_DESCRIPTION: 'Statut du service Base de Données.',
      SELECTDURATION: 'Sélectionner la Durée',
      PMSTATUS: 'Statut PM',
      PMSTATUS_DESCRIPTION: 'Statut du service PM.',
      REFURBISHMENETHISTORY: 'Historique de Réaménagement'
    },
    SYSTEM: {
      COLLAPSIBLE: 'Système',
      EXTERNALSERVICE: 'Service Externe',
      PERFORMANCESERVICE: 'Service de Performance',
      PERFORMANCESERVICEDESCRIPTION: 'Paramètre lié à la gestion des performances, tel que l’URL par défaut pour le téléchargement de fichiers KPI par les dispositifs.',
      PREFERENCE: 'Préférence',
      DBSERVER: 'Serveurs DB',
      SOURCES: 'Sources de Données',
      SNMPTRAP: 'Notifications SNMP Trap',
      SNMPTRAP_DESCRIPTION: 'Configuration des notifications via SNMP.',
      SMTP: 'Notifications SMTP',
      SMTP_DESCRIPTION: 'Configuration des notifications via SMTP.',
      SMS: 'Notifications SMS',
      STATISTICSPRE: 'Statistiques',
      STATISTICSPRE_DESCRIPTION: 'Paramètre de préférence concernant la rotation de la collecte de données dans AMP.',
      LOGPRE: 'Logs',
      LOGPRE_DESCRIPTION: 'Paramètre de rotation des logs pour les sessions et opérations des dispositifs.',
      FAULT: 'Alarmes',
      FAULT_DESCRIPTION: 'Paramètre de préférence pour les alarmes, comme la reconnaissance automatique et la rotation des alarmes.',
      REPORTS: 'Rapports',
      REPORTS_DESCRIPTION: 'Paramètre de rotation des rapports de synthèse des dispositifs.',
      SYSREPORT: 'Liste des Rapports Serveur',
      GENERATESYSREPORT: 'Générer un Rapport Serveur',
      CONFIRMDELETE: 'Confirmer la Suppression de Tous les Rapports Serveur',
      DOCONFIRMDELETE: 'Voulez-vous supprimer tous les Rapports Serveur ?',
      CONFIRMDELETESELECT: 'Confirmer la suppression des Rapports Serveur sélectionnés',
      DOCONFIRMDELETESELECT: 'Voulez-vous supprimer les Rapports Serveur sélectionnés ?',
      DOWNLOADCSV: 'Télécharger CSV',
      DOWNLOADTXT: 'Télécharger TXT',
      DOWNLOADXLSX: 'Télécharger XLSX',
      LICENSE: 'Licence',
      LICENSE_DESCRIPTION: 'Informations concernant la Licence AMP, telles que l’état, le type, le protocole pris en charge et l’expiration. Permet à l’utilisateur de mettre à jour la Licence pour prolonger l’expiration ou activer plus de fonctionnalités dans AMP.',
      LICENSETYPE: 'Versions',
      LICENSESTATE: 'État de la Licence',
      KEY_DESCRIPTION: "Fournit une fonction pour activer amp après la saisie de la clé, ainsi que des informations relatives à la clé.",
      CPENUM: "Capacité de l'équipement",
      SESSIONNUM: 'Numéro de Session',
      REMAININGTIME: 'Temps Restant',
      VALIDTO: 'Valide Jusqu\'à',
      DISPLAY: 'Afficher',
      GENERAL: 'Général',
      GENERAL_DESCRIPTION: 'Paramètre général du système, tel que le niveau de journalisation des sessions, le délai d\'expiration de déconnexion et le rapport serveur.',
      CWMP: 'CWMP',
      CWMP_DESCRIPTION: 'Paramètres liés au protocole CWMP, tels que le délai d\'expiration des sessions et les critères en ligne.',
      NETCONF: 'Netconf',
      NETCONF_DESCRIPTION: 'Paramètres liés au protocole NETCONF, tels que l’intervalle de réessai et l’intervalle de maintien de l’activité.',
      USP: 'USP',
      USP_DESCRIPTION: 'Paramètres liés au protocole USP, tels que via WebSocket ou MQTT.',
      LIVEUPDATE: 'Mise à Jour en Direct',
      LIVEUPDATE_DESCRIPTION: 'Fonctionnalités pour initier la communication entre AMP et un dispositif spécifique immédiatement.',
      MQTT: 'MQTT Système',
      FILES: 'Fichiers',
      FILES_DESCRIPTION: 'Paramètres de fichiers par défaut du système, tels que FOTA et URL de journalisation des dispositifs et authentification.',
      TELEMETRY: 'Télémetrie Système',
      SUMMARYREPORT: 'Rapport Sommaire',
      SUMMARYREPORT_DESCRIPTION: 'Paramètres liés à la carte, tels que la sélection des fournisseurs de données cartographiques et des sources.',
      EVENTS: 'Événements',
      SYSEVENTS: 'Événements Système',
      SYSEVENTS_DESCRIPTION: 'Liste de tous les événements liés à AMP avec des informations détaillées telles que la gravité, le problème spécifique, la cause probable et l’heure de l’événement.',
      REGISTRATIONLOG: "Journal d'inscription",
      REGISTRATIONLOG_DESCRIPTION: "Journal d'enregistrement USP MQTT",
      NODES: 'Nœuds',
      DEL_TITLE_NODES: 'Voulez-vous supprimer le nœud',
      NODES_DESCRIPTION: 'Liste des informations des nœuds AMP, telles que le nom du nœud, l’adresse IP, la version AMP et le temps de disponibilité.',
      ENTERLICENSEKEY: 'Entrer la Clé de Licence',
      LICENSEKEY: 'Clé de Licence',
      KEY: 'La clé',
      UNIQUEID: 'Identification unique',
      KEYVALIDITY: 'Validité de la clé',
      EDITURL: 'Modifier l’URL',
      VERIFYSMTP: 'Tester l’Envoi d’Email',
      VERIFYSNMP: 'Tester l’Envoi SNMP',
      SERVICESTATUSTRACKING: 'Suivi de l’État du Service',
      KPIFACTORS: 'Facteurs KPI',
      KPIFACTORSTRACKING: 'Facteurs KPI pour le Suivi',
      VERIFYXMPP: 'Tester XMPP',
      MAP_DESCRIPTION: 'Carte',
      PROCESS: 'Processus',
      NODEID: 'ID de Nœud',
      SEVERITY: 'Gravité',
      LOCATIONMAP: 'Carte de Localisation',
      LOCATIONMAP_DESCRIPTION: 'Paramètres liés à la carte, tels que la sélection des fournisseurs de données cartographiques et des sources.',
      FIVECORESERVICE: 'Service de Core 5G',
      FIVECORESERVICE_DESCRIPTION: 'Paramètres liés au service de core 5G, tels que le fournisseur de Core 5G et l’URL du serveur.',
      ENERGYMANAGEMENT: "Gestion de l'énergie",
      ENERGY_DESCRIPTION: "Paramètres liés à la gestion de l'énergie, tels que la limite de puissance et l'intervalle de veille.",
      NIDS: 'NIDS',
      NIDS_DESCRIPTION: 'Système de Détection d’Intrusion Réseau.',
      PROMETHEUS: 'Collecte de Métriques - Prometheus',
      PROMETHEUS_DESCRIPTION: 'Paramètres liés à la collecte de données utilisant Prometheus.',
      PROMETHEUS_PARAMETER_DESCRIPTION: {
        PULL_PATH: 'URLs à partir desquelles extraire les métriques.',
        USERNAME: 'Nom d\'utilisateur dans l\'en-tête Authorization pour chaque requête d\'extraction de métrique Prometheus.',
        PASSWORD: 'Mot de passe dans l\'en-tête Authorization pour chaque requête d\'extraction de métrique Prometheus.',
      },
      KAFKA: 'Collecte de Métriques - Kafka',
      KAFKA_DESCRIPTION: 'Paramètres liés à la collecte de données utilisant Kafka.',
      KAFKA_PARAMETER_DESCRIPTION: {
        BROKERS: 'URL des courtiers Kafka.',
        TOPIC: 'Sujet Kafka pour les messages des producteurs.',
        ROUTING_KEY: 'Mécanisme de routage des messages.',
        ACCESS_TOKEN: "Jeton pour l'authentification."
      },
      NODE_DESCRIPTION: {
        UPGRADE: 'Mise à niveau',
        ADDRESS: 'Adresse:',
        HASHKEY: "Clé de hachage",
        TYPE: 'Type de produit',
        USERNAME: 'Nom d’utilisateur',
        PASSWORD: 'Mot de passe',
        TARGEVERSION: 'Version Targe',
        COMPOENT: 'Composant',
        RUNTOOL: "Outil d'exécution",
        UPGRADESUCC: 'Mise à niveau réussie',
        UPGRADEFAIL: 'Échec de mise à niveau',
        WAITFORUPDATE: 'Télécharger les fichiers de mise à niveau',
        STARTUPDATING: 'Démarrer la mise à niveau...',
        BEINGUPDATED: 'Mise à niveau terminée et en attente de redémarrage',
        SERVERRESTART: 'Le serveur redémarre...',
        TIMEOUT: 'Délai de mise à niveau expiré !',
        ACSNODEREBOOT: 'Redémarrer ce nœud AMP peut causer une perte de données.',
        ACSNODESHUTDOWN: "L'arrêt de ce nœud AMP peut entraîner une perte de données.",
      },
      SETTING_DESCRIPTION: {
        GENERAL: {
          SESSIONTIMEOUT: "Ce paramètre permet de configurer le délai d'expiration pour une session CWMP.",
          TRANSACTIONTIMEOUT: "Le délai d'expiration de la transaction est le délai d'expiration de la demande et de la réponse.",
          REFRESHINTERVAL: "Intervalle de réessai Netconf",
          KEEPALIVEINTERVAL: "Intervalle de maintenance Netconf",
          SESSIONLOGLEVEL: "Ce paramètre influence la vue détaillée du journal de session du dispositif : si ORIGINAL, la session est présentée comme des enveloppes SOAP ; si RPC, comme des messages SOAP analysés.",
          DEVICE: "Les paramètres activent/désactivent l'envoi du Dispositif. Tous les paramètres suivants sont utilisés pour la méthode BootStrap initiale en ligne du dispositif.",
          DEVICE_XMPP_CONNECTION_1: "Les paramètres activent/désactivent l'envoi du Device.XMPP.Connection.1 pour les méthodes BootStrap.",
          DEVICE_MQTT_CLIENT_1: "Les paramètres activent/désactivent l'envoi du Device.MQTT.Client.1 pour les méthodes BootStrap.",
          DEIVCE_DEVICEINFO_XVENDOR_HOLD: "Les paramètres activent/désactivent l'envoi du Device.DeviceInfo.X_VENDOR.HOID pour les méthodes BootStrap.",
          DEVICE_MANAGEMENTSERVER_PERIODICINFORMTIME: "Les paramètres activent/désactivent l'envoi du Device.ManagementServer.PeriodicInformTime pour les méthodes BootStrap",
          TYPEOFINFORMRECEIVEDWITHIN: "Les paramètres sont utilisés pour déterminer si le dispositif est en ligne.",
          INTERVALOFINFORMRECEIVEDWITHIN: "Les paramètres configurent la période pour la condition de chute.",
          SERVERREPORTENABLED: "Les paramètres activent/désactivent la génération du rapport du serveur : Si activé, le rapport du serveur sera généré par le ACS. Si désactivé, le rapport du serveur ne sera pas généré par le ACS.",
          SERVERREPORTEMAILNOTIFICATION: 'Activez ou désactivez la fonction de notification par courriel des rapports système.',
          SERVERREPORTPERIOD: "Le paramètre configure la fréquence (en jours) de la génération du rapport du serveur.",
          SERVERREPORTCONTENT: 'Rapport système contenu optionnel, AMP prend en charge la collecte d’informations pour MONGODB, XMPP, MQTT et PM server.',
          PRIORITY: "Définit la priorité des sources de détection IP du CPE : RemoteAddress - L'IP est obtenue à partir du nœud de données spécifié. Eq.Device.DeviceInfo.X_Vendor_GlobalIPAddress ; X-Forwarded-For - L'IP est obtenue à partir de l'en-tête HTTP XForwarded-For ; Custom - L'IP est obtenue à partir de l'en-tête HTTP personnalisé, dont le nom est spécifié dans le paramètre [En-tête personnalisé].",
          CUSTOMHEADER: "Nom de l'en-tête HTTP personnalisé, qui contient l'adresse IP réelle du CPE.",
          IDLETIMEOUT: "Temps d'expiration de la session système",
          SWAGGER_ENABLED: "Activer/désactiver l'interface utilisateur de l'API reposante.",
          CLIENT_URL: " L'URL utilisée par le CPE pour se connecter à l'ACS en utilisant le protocole de gestion WAN du CPE."
        },
        CONNECTIONREQUEST: {
          USERNAME: "Nom d'utilisateur configurable pour le mécanisme CR.",
          PASSWORD: "Mot de passe configurable pour le mécanisme CR.",
          RETRYTIMEOUT: "Délai d'attente de réessai configurable pour le CR.",
          NUMBEROFRETRY: "Le nombre maximal de tentatives CR. ",
          TYPE: "Le type pour le CR.",
          // XMPPADDRESS:'',
          XMPPDOMAIN: "Le paramètre est un nom de domaine configurable, utilisé dans la génération automatique de JID.",
          XMPPPORT: "Le port du serveur XMPP. Valeur par défaut spécifiée pour le serveur ejabberd.",
          XMPPACSUSERNAME: "Nom d'utilisateur ACS configurable pour l'invocation XMPP CR.",
          XMPPACSPASSWORD: "Mot de passe ACS configurable pour les invocations XMPP CR.",
          XMPPADMINPORT: "Le port du serveur XMPP pour l'administrateur du serveur. Valeur par défaut spécifiée pour le serveur ejabberd.",
          XMPPADMINUSERNAME: "Le paramètre est un nom d'utilisateur administrateur XMPP configurable, utilisé dans l'enregistrement automatique des utilisateurs XMPP.",
          XMPPADMINPASSWORD: "Le paramètre est un mot de passe administrateur XMPP configurable, utilisé dans l'enregistrement automatique des utilisateurs XMPP.",
          XMPPRESOURCE: "Valeur de ressource configurable pour le XMPP CR.",
          XMPPUSETLS: "Les paramètres activent/désactivent l'utilisation de TLS pour le XMPP CR : Si activé, TLS est activé. Si désactivé, TLS est désactivé."
        },
        USP: {
          BINDING: 'Type de liaison, WebSocket ou MQTT.',
          ADDRESS: "Domaine du serveur MTP",
          PORT: "Port de connexion MTP",
          APIKEY: "Dans MQTT, une clé API est utilisée pour interroger le statut du serveur.",
          USERNAME: "Le nom d'utilisateur requis par le courtier, le cas échéant.",
          PASSWORD: "Le mot de passe requis par le courtier, le cas échéant.",
          USE_TLS: "MTP sur TLS",
          EXADDRESS: "adresse ou le domaine auquel l'appareil peut se connecter au service WebSocket/MQTT/CWMP/XMPP.",
          EXPORT: 'le périphérique peut se connecter au port du Service websocket/mqtt.',
          USETLS: 'Si le périphérique utilise TLS pour se connecter au service WebSocket/MQTT/CWMP/XMPP.',
          EXURL: "URL auquel l'appareil peut se connecter au service CWMP.",
        },
        FILES: {
          DOWNLOAD: {
            LATESTFIRMWARE: "Le paramètre permet à l'utilisateur de spécifier la dernière version du firmware pour les utilisateurs CSR.",
            FIRMWARESERVERURL: "Le chemin vers le serveur de fichiers est utilisé par les AP pour télécharger un firmware.",
            FIRMWARESERVERUSERNAME: "Identifiant pour s'authentifier auprès du serveur de fichiers.",
            FIRMWARESERVERPASSWORD: "Mot de passe pour s'authentifier auprès du serveur de fichiers.",
          },
          UPLOAD: {
            FILETYPE: "Type de fichier à télécharger du dispositif",
            INSTANCEPATH: 'Chemin vers lequel les utilisateurs peuvent télécharger des fichiers journaux dans le modèle de données.',
            LOGUPLOADURL: "L'URL où les utilisateurs CSR peuvent télécharger les fichiers journaux des AP. Cette fonctionnalité nécessite une configuration serveur supplémentaire.",
            USERNAME: "Nom d'utilisateur configurable pour le téléchargement de fichiers.",
            PASSWORD: "Mot de passe configurable pour le téléchargement de fichiers."
          },
          CONF_DOWNLOAD: {
            DEFAULT_FILE: 'Le fichier de configuration par défaut sélectionné à partir des fichiers (type : 3 Vendor Configuration File).',
            DEFAULT_FILE_URL: 'L’URL du fichier de configuration par défaut.',
            FILETYPE: 'Type de téléchargement du fichier de configuration de l’appareil.',
            CONFURL: 'URL permettant à l’appareil de télécharger le fichier de configuration depuis AMP.',
            USERNAME: 'Nom d’utilisateur configurable pour le téléchargement par l’appareil.',
            PASSWORD: 'Mot de passe configurable pour le téléchargement par l’appareil.'
          }
        },
        TELEMETRY: {
          TELEMETRYSERVERREDIRECTION: "Les paramètres activent/désactivent le bouton de lien vers le site web tiers sur la page d'informations sur le dispositif. Si activé, le bouton de lien vers le site web tiers est affiché. Si désactivé, le bouton de lien vers le site web tiers n'est pas affiché.",
          VENDOR: 'Fournisseur proposant une technologie, des services ou des produits spécifiques, avec des options telles que Druid, DNMM, HP et Open 5GC.',
          TYPE: "Type de système de détection d'intrusion réseau open source, les options incluent Suricata",
          SERVERURL: "L'URL du site web tiers.",
          SERVERUSERNAME: "Nom d'utilisateur configurable pour se connecter au site web tiers.",
          SERVERPASSWORD: "Mot de passe configurable pour se connecter au site web tiers.",
          KPIFACTORS: "Les facteurs KPI peuvent surveiller l'état des paramètres PM en fonction des règles définies par l'utilisateur.",
          UEINTERVAL: 'intervalle de temporisation UE configurable.',
          CELLINTERVAL: 'intervalle de temporisation de cellule configurable.',
          ALARMINTERVAL: "intervalle de temporisation d'alarme configurable.",
          COMMONINTERVAL: 'intervalle de minuterie commun configurable.',
          APIURL: "L'adresse de l'interface nord de Performance Service.",
          APIUSERNAME: "Le nom d'utilisateur pour l'identité de l'interface nord de Performance Service.",
          APIPASSWORD: "Le mot de passe pour l'identité de l'interface nord de Performance Service.",
        },
        MAP: {
          TYPE: 'Type de serveur de carte, AMP support Google Map et Open street Map. La valeur par défaut est Google map.',
          URL: 'Url du serveur de carte.',
          APIKEY: 'Mapper la clé api.',
        }
      },
      PREFERENCE_DESCRIPTION: {
        SMTP: {
          MAIL_HEALTHY: 'Surveillance de la santé du système, comme la charge CPU, l’utilisation du disque, les crashs et les licences.',
          MAIL_FORM: "Nom d'utilisateur de l'expéditeur du courriel",
          MAIL_HOST: 'L’adresse du serveur SMTP correspondant à la boîte aux lettres.',
          MAIL_USERNAME: 'Adresse email de l’expéditeur',
          MAIL_PASSWORD: 'Mot de passe de l’expéditeur du courriel',
          MAIL_PORT: 'Port de l’expéditeur du courriel',
          MAIL_TO: 'Adresse du destinataire',
          MAIL_SMTP_AUTH: 'Configuration liée au protocole SMTP, si une authentification est nécessaire.',
          MAIL_SMTP_SECURITY: 'Configuration liée au protocole SMTP.',
          MAIL_TRANSPORT_PROTOCOL: 'Actuellement non utilisé',
        },
        SNMPTRAP: {
          SNMPTRAP_TARGET: 'Adresse de destination SNMP Trap',
          SNMPTRAP_PORT: 'Port UDP pour envoyer des requêtes, par défaut 161.',
          SNMPTRAP_RETRIES: 'Nombre de fois pour renvoyer une requête, par défaut 1.',
          SNMPTRAP_TIMEOUT: 'Nombre de millisecondes à attendre pour une réponse avant de renvoyer ou de échouer, par défaut 5000.',
          SNMPTRAP_TRANSPORT: 'Spécifiez le transport à utiliser, peut être udp4 ou udp6, par défaut udp4.',
          SNMPTRAP_TRAPPORT: 'Port UDP pour envoyer des pièges et des informs, par défaut 162.',
          SNMPTRAP_VERSION: 'Soit snmp.Version1 ou snmp.Version2c',
          SNMPTRAP_BACKOFF: 'Le facteur par lequel augmenter le délai pour chaque nouvelle tentative, par défaut 1 pour aucune augmentation.',
          SNMPTRAP_COMMUNITY: "Utilisé pour garantir la sécurité de la communication et l'authentification.",
        },
        REPORTS: {
          DEVICE_REPORT_CLEANUP_ENABLE: 'Si activé, le paramètre active un nettoyage automatique du stockage des rapports de périphériques. Si désactivé, le nettoyage automatique est désactivé.',
          DEVICE_REPORT_RETENTION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver une entrée de rapport de périphérique dans le stockage.',
          SERVER_REPORT_CLEANUPZZ_ENABLE: 'Si activé, le paramètre active un nettoyage automatique du stockage des rapports du serveur. Si désactivé, le nettoyage automatique est désactivé.',
          SERVER_REPORT_RETNETION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver une entrée de rapport de serveur dans le stockage.'
        },
        STATISTICS: {
          CPU_COLLECTION_ENABLE: "Si elle est activée, la licence permet de collecter des métriques CPU. Si elle est désactivée, la licence n'autorise pas la collecte d'indicateurs CPU.",
          DISK_COLLECTION_ENABLE: "Si elle est activée, la licence autorise la collecte d'indicateurs de disque. Si désactivé, la licence n'autorise pas la collecte d'indicateurs de disque.",
          MEMORY_COLLECTION_ENABLE: "Si elle est activée, la licence autorise la collecte des indicateurs de mémoire. Si elle est désactivée, la licence n'autorise pas la collecte d'indicateurs de mémoire.",
          REPORT_ENABLE: "Si elle est activée, la licence autorise le reporting des statistiques. Si elle est désactivée, la licence n'autorise pas la Déclaration d'indicateurs statistiques.",
          REPORT_PERIOD: 'Le paramètre spécifie la fréquence de collecte des métriques.',
          PM_KPI_COLLECTION_RETENTION: 'Le paramètre spécifie le nombre de jours pendant lesquels conserver les données PM KPI DB.',
          PM_KPI_FILE_RETENTION: 'Le paramètre spécifie le nombre de jours pendant lesquels conserver les fichiers KPI PM.'
        },
        LOGCONFIG: {
          DEVICE_GROUP_OPERATION_LOG_CLEANUP_ENABLE: "Si elle est activée, la licence permet l'effacement automatique des données d'opération du Groupe de périphériques. Si elle est désactivée, la licence n'autorise pas l'effacement automatique des données d'exploitation du Groupe de périphériques.",
          DEVICE_GROUP_OPERATION_LOG_RETENTION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver les données des opérations de groupe dans les journaux.',
          DEVICE_OPERATION_LOG_CLEANUP_ENABLE: "Si elle est activée, la licence permet l'effacement automatique des données de fonctionnement de l'appareil. Si désactivé, la licence ne permet pas l'effacement automatique des données de fonctionnement de l'appareil.",
          DEVICE_OPERATION_LOG_RETENTION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver les données des opérations de périphérique dans les journaux.',
          SESSION_LOG_CLEANUP_ENABLE: "Si elle est activée, la licence permet l'effacement automatique des journaux d'événements. Si elle est désactivée, la licence n'autorise pas l'effacement automatique des journaux d'événements.",
          SESSION_LOG_RETENTION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver les enregistrements de session dans les journaux.',
          STATISTICS_LOG_CLEANUP_ENABLE: "Si elle est activée, la licence autorise le nettoyage automatique des journaux de statistiques. Si elle est désactivée, la licence n'autorise pas le nettoyage automatique des journaux de statistiques.",
          STATISTICS_LOG_RENTENTION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver les journaux statistiques.'
        },
        FAULTMANAGEMENT: {
          EVENT_ALARM_ACK_ENABLE: 'Si activé, le paramètre active une accusé de réception automatique sur les événements. Si désactivé, un accusé de réception manuel est nécessaire.',
          EVENT_ALARM_CLEANUP_ENABLE: 'Si activé, le paramètre active un nettoyage automatique des journaux d’événements. Si désactivé, le nettoyage automatique des journaux d’événements est désactivé.',
          EVENT_ALARM_EMAIL_ENABLE: 'Si activé, le paramètre active une notification par email automatique lors de la survenue d’événements. Si désactivé, aucune notification par email d’alarme.',
          EVENT_ALARM_RETENTION_PERIOD: 'Le paramètre spécifie le nombre de jours pour conserver les journaux d’événements.'
        }
      }
    },
    COMMON: {
      DEVICES: 'Appareils',
      DEVICE: 'Appareil',
      CLIENTS: 'Clients',
      CLIENT: 'Client',
      USERS: 'Utilisateurs',
      ALARMS: 'Alarmes',
      TOTALALARMS: 'Total des Alarmes',
      HISTORYALARMS: 'Historique des Alarmes',
      CRITICALALARMS: 'Alarmes Critiques',
      MAJORALARMS: 'Alarmes Majeures',
      WARNINGALARMS: 'Alarmes d’Avertissement',
      MINORALARMS: 'Alarmes Mineures',
      PRODUCTS: 'Produits',
      PRODUCTSDISTRIBUTION: 'Distribution de Produits',
      REGISTERDEVICECOUNT: "Nombre d'appareils enregistrés",
      REGISTERDEVICECOUNTDISTRIBUTION: "Graphique de distribution du nombre d'appareils par produit",
      ONLINEDEVICE: 'Appareils en Ligne',
      HISTORYONLINEDEVICE: "Historique des appareils en ligne",
      APPLY: 'Appliquer',
      DELETE: 'Supprimer',
      DELETEALL: 'Supprimer Tout',
      CANCEL: 'Annuler',
      OK: 'OK',
      CLOSE: 'Fermer',
      ADD: 'Ajouter',
      EDIT: 'Modifier',
      Fail: 'Échouer',
      SERIAL_NUMBER: 'Numéro de Série',
      PRODUCT_CLASS: 'Classe de Produit',
      ACTION: 'Profils',
      NEW: 'Nouveau',
      SELECTACTION: 'Sélectionner Action',
      IMPORT: 'Importer',
      DOWNLOAD: 'Télécharger',
      DOWNLOADLOG: 'Télécharger le journal',
      SAVE: 'Sauvegarder',
      DONTSAVE: 'Ne pas sauvegarder',
      UPLOAD: 'Téléverser ',
      NAME: 'Nom',
      ENTERNAME: 'Entrer le Nom',
      VERSION: 'Version',
      PRIORITY: 'Priorité',
      ENTERVERSION: 'Entrer la Version',
      SOFTVERSION: 'Version du Logiciel',
      TYPE: 'Type',
      SELECTTYPE: 'Sélectionner le Type',
      PREVIOUS: 'Précédent',
      NEXT: 'Suivant',
      USERNAME: 'Nom d’Utilisateur',
      PASSWORD: 'Mot de Passe',
      USERNAME1: 'Nom d’Utilisateur',
      PASSWORD1: 'Mot de Passe d’Utilisateur',
      ENTERUSERNAME: 'Entrer le Nom d’Utilisateur',
      ENTERPASSWORD: 'Entrer le Mot de Passe',
      UPDATE: 'Mettre à Jour',
      UNINSTALL: 'désinstaller',
      PARAMETERS: 'Paramètres',
      PARAMNAME: 'Chemin du paramètre',
      ENTERPARAMNAME: 'Entrez le chemin du paramètre',
      PARAMTYPE: 'Type de Paramètre',
      SELECTPARAMTYPE: 'Sélectionner le Type de Paramètre',
      PARAMVALUE: 'Valeur du Paramètre',
      ENTERPARAMVALUE: 'Entrer la Valeur du Paramètre',
      ADDPARAM: 'Ajouter Paramètre',
      EXECUTE: 'Exécuter',
      SIZE: 'Taille',
      CANCELALL: 'Annuler Tout',
      FIELDREQUIRED: 'Ce champ est obligatoire!',
      DETAILS: 'Détails',
      SELECTPRODUCTNAME: 'Sélectionner le Nom du Produit',
      SELECTPRODUCT: 'Sélectionner le Produit',
      AND: 'Et',
      EDITPARAM: 'Modifier le Paramètre',
      VALUE: 'Valeur',
      EXPANDCOLLROW: 'Développer/Réduire la Ligne',
      PORT: 'Port',
      HOST: 'Hôte',
      THECUSTOMIZE: 'Personnalisateur de Thème',
      CUSTOMIZEREALTIME: 'Personnaliser & Prévisualiser en Temps Réel',
      SKIN: 'Peau',
      LIGHT: 'Clair',
      BORDERED: 'Borduré',
      DARK: 'Sombre',
      RED: "Rouge",
      BLUE: "Bleu",
      SEMIDARK: 'Semi-Sombre',
      ROUTETRA: 'Transition de Route',
      FADEINLEFT: 'Apparition à Gauche',
      ZOOMIN: 'Zoom avant',
      FADEIN: 'Apparition',
      NONE: 'Aucun',
      MENULAYOUT: 'Disposition du Menu',
      VERTICAL: 'Vertical',
      HORIZONTAL: 'Horizontal',
      MENUCOLL: 'Menu Réduit',
      MENUHIDDEN: 'Menu Caché',
      NAVBARCOLOR: 'Couleur de la Barre de Navigation',
      NAVBARTYPE: 'Type de Barre de Navigation',
      MENYTYPE: 'Type de Menu',
      FLOATING: 'Flottant',
      STICKY: 'Collant',
      STATIC: 'Statique',
      FOOTERTYPE: 'Type de Pied de Page',
      WIDGETS: 'Personnaliser les Widgets',
      EDITMODE: "Mode d'Édition de Widget",
      CUSWIDGETS: 'Personnalisation des Widgets',
      LOGOUT: 'Déconnexion',
      RECENTNOTIF: 'Notifications Récentes',
      NOTIFICATIONS: 'Notifications',
      READMORE: 'Lire plus',
      TOTAL: 'total',
      SELECTED: 'sélectionné',
      CREATED: 'Créé le',
      SELECTCOLUMN: 'Sélectionner Colonne',
      ACTIVE: 'Actif',
      ALLOW: 'Permettre',
      YES: 'Oui',
      CLIENTLIST: 'Liste des Clients',
      WIFICLIENTLIST: 'Liste des Clients WiFi',
      WIFICLIENTLIST_DESCRIPTION: 'Liste de tous les clients WiFi inclus dans ce groupe.',
      WIFICLIENTLISTDESCRIPTION: 'Statut actuel du WiFi des clients associés sur les radios/bandes disponibles de cet appareil.',
      ONLINE: 'En Ligne',
      OFFLINE: 'Hors Ligne',
      EXPORT: 'Exporter',
      MQTT: 'MQTT',
      CWMP: 'CWMP',
      NETCONF: 'NETCONF',
      CURRENTNODE: 'Nœud Actuel',
      CHILDNODE: 'Nœud Enfant',
      EDITSTAGE: "Éditer le Nom de l'Étape",
      STAGENAME: "Nom de l'Étape",
      ENTERSTAGENAME: "Entrer le Nom de l'Étape",
      OPERATIONNAME: "Nom de l'Opération",
      ADDOPERATION: 'Ajouter une opération',
      ALLPROTOCOL: 'Tous les protocoles',
      ALLPRODUCTS: 'Tous les produits',
      ALLEVENT: 'Tous les événements',
      USER: 'Utilisateur',
      ALLFILETYPES: 'Tous les Types de Fichiers',
      ALLTARGETTYPES: 'Tous les Types de Cibles',
      TRANSMISSTIONTYPE: 'Type de Transmission',
      SELECTTRANSMISSTIONTYPE: 'Choisir le type de transfert',
      REMOVEFROMGROUP: 'Supprimer du Groupe',
      SHUTDOWN: "Temps d'arrêt",
      NOPERMISSION: "L'utilisateur actuel n'est pas autorisé à lire le contenu de cette page.",
      SEPARATED_BY_SEMICOLONS: 'Séparé par des points-virgules',
      SEPARATED_BY_COMMAS: 'Séparé par des virgules',
      MAIL_SEPARATED_BY_SEMICOLONS: 'Séparé par des points-virgules (<EMAIL>;<EMAIL>;)',
      SN_SEPARATED_BY_COMMAS: 'Séparé par des virgules (sn1,sn2)',
      WIDGETNAME: 'Entrez le nom du widget, la classe ou la sous-classe.',
      APNAMEEDITSUCC: 'Nom de l’AP modifié avec succès !',
      APNAMEEDITFAIL: 'Échec de la modification du nom de l’AP.',
      LOCATE: 'Localiser',
      RELOAD: "recharger",
      DATA: "Données",
      STATE: "État",
      REGISTER: 'S\'inscrire',
      GROUP: 'Groupe',
      SELECTCHARTTYPE: 'Sélectionner le type de graphique',
      OPEN_MAXIMIZE: "Ouvrir Maximiser",
      SELECTORENTER: 'Veuillez sélectionner ou saisir vos options',
      INVALIDFILETYPE: 'Type de fichier non valide. Veuillez sélectionner l’un des types suivants: ',
    },
    CONFIRM: {
      CONF: 'Confirmer ',
      REMOVAL: ' suppression?',
      REBOOT: " Redémarrer?",
      SHUTDOWN: " Temps d'arrêt?",
      ADDFAIL: 'Ajout échoué !',
      NAMEEXIST: 'le nom existe déjà !',
      ALARMNOTIF: 'Notification d’alarme mise à jour avec succès',
      CONFREMGROUP: 'Confirmer la suppression du groupe?',
      CONFGROUP: 'Confirmer la suppression du groupe?',
      CONFGROUPS: 'Confirmer la suppression des groupes ?',
      DOGROUP: 'Voulez-vous supprimer ',
      FROMGROUP: ' du groupe?',
      IMPORTSUCCESS: "Importation réussie !",
      IMPORTFAIL: 'Importation échouée !',
      FILEEMPTY: 'Le fichier est vide',
      NOTSUPPORT: 'Ce format de fichier n’est pas pris en charge',
      DODELETEGROUP: 'Voulez-vous supprimer le groupe ',
      DODELETEGROUPS: 'Voulez-vous supprimer ces groupes ?',
      GROUPOPER: 'Opérations de groupe de périphériques !',
      WORKFLOWOPER: 'Actions sur le flux de travail des appareils !',
      WORKFLOWOPERATION: 'Fonctionnement du flux de travail de l’appareil',
      WORKFLOWDOREMOVEALL: 'Voulez-vous supprimer toutes les entrées du journal des opérations de flux de travail?',
      WORKFLOWCLEANSUCC: 'Le journal des opérations de flux de travail a été nettoyé avec succès',
      WORKFLOWNOTCLEAN: 'Le journal des opérations de flux de travail n’a pas été nettoyé',
      SETGROUPOPERSUCC: 'Définissez le succès de l’opération du groupe!',
      RENAMESUCC: 'Renommage réussi',
      CONFNIT: 'Confirmer le téléchargement de la notification d’alarme?',
      DODOWNLOADNIT: 'Voulez-vous télécharger la notification d’alarme ',
      ALARMNIT: 'Notification',
      DOWNLOADSUCC: 'Téléchargement réussi !',
      PLESELECT: 'Veuillez sélectionner les notifications d’alarme d’abord !',
      CONFDOWNLOADNIT: 'Confirmer le téléchargement des notifications d’alarme sélectionnées?',
      DODOWNLOADSELECT: 'Voulez-vous télécharger les notifications d’alarme sélectionnées?',
      DOWNLOADSELECT: 'Téléchargement des notifications d’alarme sélectionnées réussi !',
      DOWANT: 'Voulez-vous ',
      THEALARMNIT: ' la notification d’alarme',
      SUCC: ' avec succès !',
      CONFDELETENIT: 'Confirmer la suppression de la notification d’alarme?',
      DODELETENIT: 'Voulez-vous supprimer la notification d’alarme',
      NITDELSUCC: 'Notification d’Alarme supprimée avec succès',
      NITID: 'Notification d’Alarme (ID: ',
      WANDEL: ') a été supprimée !',
      NITDELETEFAIL: 'Suppression de la notification d’alarme échouée',
      NOTDEL: ') n’a pas été supprimée !',
      SELECTFIRST: 'Veuillez sélectionner les notifications d’alarme d’abord !',
      STATEITEMS: 'Les notifications d’alarme sélectionnées contiennent un ou plusieurs éléments en état actif !',
      CONFSELECTNIT: 'Confirmer la suppression des notifications d’alarme sélectionnées?',
      DOSELECTNIT: "Voulez-vous supprimer les notifications d'alarme sélectionnées?",
      SELECTNITSUCC: "Suppression des notifications d'alarme sélectionnées réussie !",
      GROUPOPERATION: 'Opération de groupe de dispositifs ',
      CANCELSUCC: ' a été annulé avec succès',
      REMOVEDLOG: ' a été retiré du journal',
      CONFCLEANUP: 'Confirmer le nettoyage du journal',
      DOREMOVEALL: 'Voulez-vous retirer toutes les entrées du journal des opérations de groupe?',
      GROUPCLEANSUCC: 'Le journal des opérations de groupe a été nettoyé avec succès',
      GROUPNOTCLEAN: 'Les opérations du groupe n’ont pas été nettoyées',
      CONFPRODUCTREM: 'Confirmer la suppression du produit?',
      DOPRODUCT: 'Voulez-vous supprimer le produit ',
      CONFRANREM: 'Confirmer la suppression du réseau d’accès radio ?',
      CONFAPNREM: 'Confirmer la suppression du réseau WiFi AP ?',
      CONFMESHREM: 'Confirmer la suppression du réseau WiFi Mesh ?',
      DORAN: 'Voulez-vous supprimer ce réseau d’accès radio ',
      DOAP: 'Voulez-vous supprimer ce réseau WiFi AP ',
      DOMESH: 'Voulez-vous supprimer ce réseau WiFi Mesh ',
      CONFPRODUCTBAN: "Confirmer l'interdiction du produit",
      CONFRANBAN: 'Confirmer l\'interdiction du réseau d\'accès radio',
      CONFAPNBAN: 'Confirmer l\'interdiction du réseau WiFi AP',
      CONFMESHNBAN: 'Confirmer l\'interdiction du réseau WiFi Mesh',
      PRODUCTACCESS: '? Les appareils de ce produit ne pourront pas accéder au serveur.',
      PRODUCTSACCESS: '? Les appareils de ces produits ne pourront pas accéder au serveur.',
      RANSACCESS: '? Les appareils de ce réseau d\'accès radio ne pourront pas accéder au serveur.',
      APSACCESS: '? Les appareils de ce réseau WiFi AP ne pourront pas accéder au serveur.',
      MESHSACCESS: '? Les appareils de ce réseau WiFi Mesh ne pourront pas accéder au serveur.',
      CONFFILE: 'Confirmer la suppression du fichier?',
      DELFILESUCC: 'Suppression du fichier réussie',
      CONFSCRIPT: 'Confirmer la suppression du script?',
      DOSCRIPT: 'Voulez-vous supprimer le script ',

      CONFFLOW: 'Confirmer le téléchargement du flux de travail?',
      WORKFLOW: 'Flux de travail',
      DOWORKFLOW: 'Voulez-vous télécharger le flux de travail ',
      CONFSELECT: 'Confirmer le téléchargement des flux de travail sélectionnés?',
      DOSELECTFLOW: 'Voulez-vous télécharger les flux de travail sélectionnés sous forme de fichiers multiples ?',
      DOSELECTFLOWASONE: 'Souhaitez-vous télécharger les flux de travail sélectionnés dans un seul fichier ?',
      DOWNSELECTFLOW: 'Téléchargement des flux de travail sélectionnés réussi !',
      DOWNSELECTFILESUCCESS: 'Le fichier sélectionné a été téléchargé avec succès!',
      DELSELECTFLOW: 'Suppression des flux de travail sélectionnés réussie !',
      PLEASEFLOWS: "Veuillez sélectionner les flux de travail d'abord !",
      THEFLOW: ' le flux de travail ',
      CONFDELFLOW: 'Confirmer la suppression du flux de travail?',
      DODELFLOW: 'Voulez-vous supprimer le flux de travail',
      DELSUCC: 'Suppression du flux de travail réussie',
      FLOWID: 'Flux de travail (ID : ',
      FLOWDELFAIL: 'Échec de la suppression du flux de travail',
      FLOWITEM: 'Les flux de travail sélectionnés contiennent un ou plusieurs éléments en état actif',
      CONFSELECTFLOW: 'Confirmer la suppression des flux de travail sélectionnés?',
      DODELSELECTFLOW: 'Voulez-vous supprimer les flux de travail sélectionnés?',

      CONFCONFIGURATION: 'Confirmer le téléchargement du configuration?',
      CONFIGURATION: 'Configuration',
      DOWORKCONFIGURATION: 'Voulez-vous télécharger le configuration ',
      CONFSELECTCONFIGURATION: 'Confirmer le téléchargement des configuration sélectionnés?',
      DOSELECTCONFIGURATION: 'Voulez-vous télécharger les configuration sélectionnés sous forme de fichiers multiples ?',
      DOSELECTCONFIGURATIONASONE: 'Souhaitez-vous télécharger les configuration sélectionnés dans un seul fichier ?',
      DOWNSELECTCONFIGURATION: 'Téléchargement des configuration sélectionnés réussi !',
      DELSELECTCONFIGURATION: 'Suppression des configuration sélectionnés réussie !',
      PLEASECONFIGURATIONS: "Veuillez sélectionner les configuration d'abord !",
      THECONFIGURATION: ' le configuration ',
      CONFDELCONFIGURATION: 'Confirmer la suppression du configuration?',
      DODELCONFIGURATION: 'Voulez-vous supprimer le configuration',
      DELCONFIGURATIONSUCC: 'Suppression du configuration réussie',
      CONFIGURATIONID: 'Configuration (ID : ',
      CONFIGURATIONDELFAIL: 'Échec de la suppression du configuration',
      CONFIGURATIONITEM: 'Les configuration sélectionnés contiennent un ou plusieurs éléments en état actif',
      CONFDELSELECTCONFIGURATION: 'Confirmer la suppression des configuration sélectionnés?',
      DODELSELECTCONFIGURATION: 'Voulez-vous supprimer les configuration sélectionnés?',

      CONFPOLICY: 'Confirmer la politique de téléchargement ?',
      DOWORKPOLICY: 'Voulez-vous télécharger la politique',
      POLICYID: 'Politique (ID :',
      POLICYFLOWSUCC: 'Politique mise à jour avec succès',
      POLICY: 'Politique',
      POLICYCLONESUCCESS: 'Politique clonée avec succès !',
      CONFDELPOLICY: 'Confirmer la suppression de la politique ?',
      DODELPOLICY: 'Voulez-vous supprimer la politique ?',
      DELPOLICYSUCC: 'Politique supprimée avec succès',
      POLICYDELFAIL: 'Échec de la suppression de la politique',
      PLEASEPOLICYS: 'Veuillez d\'abord sélectionner des politiques !',
      DOSELECTPOLICY: 'Souhaitez-vous télécharger les politiques sélectionnées en tant que plusieurs fichiers ?',
      DOSELECTPOLICYSONE: 'Souhaitez-vous télécharger les politiques sélectionnées en tant qu\'un seul fichier ?',
      CONFSELEPOLICY: 'Confirmer le téléchargement des politiques sélectionnées ?',
      DOWNSELECTPOLICY: 'Les politiques sélectionnées ont été téléchargées avec succès !',
      POLICYITEM: 'Les politiques sélectionnées contiennent un ou plusieurs éléments d\'état actif',
      CONFDELSELECTPOLICY: 'Confirmer la suppression des politiques sélectionnées ?',
      DODELSELECTPOLICY: 'Souhaitez-vous supprimer les politiques sélectionnées ?',
      DELSELECTPOLICY: 'Les politiques sélectionnées ont été supprimées avec succès !',

      CONPROFILE: 'Télécharger la configuration?',
      PROFIL: 'Konfigurationsdatei',
      DOPROFILE: 'La configuration doit être téléchargée',
      CONOSELECT: 'Confirmer le téléchargement du fichier de profil sélectionné ?',
      DOSELECTPROFILE: 'Vous souhaitez télécharger le profil de travail sélectionné dans plusieurs fichiers?',
      DOSELECTPROFILEASONE: 'Vous souhaitez télécharger le profil de travail sélectionné en tant que fichier?',
      DOWNSELECTPROFILE: 'Téléchargez les profils sélectionnés avec succès !',

      PROVISIONINGFILE: "Confirmer le téléchargement du fichier ?",
      PROVISIONINGCONFILE: "Confirmer le téléchargement du fichier?",
      PROVISIONINGSELECT: "Confirmer le téléchargement du fichier sélectionné ?",
      PROVISIONINGDOSELECTFILE: "Voulez - vous télécharger le fichier sélectionné en plusieurs fichiers?",
      PROVISIONINGDOSELECTFILEONE: "Voulez-vous télécharger les fichiers sélectionnés en un seul fichier ?",
      PROVISIONINGCONOSELECT: "Confirmer que vous souhaitez télécharger le fichier sélectionné?",

      ENDGREATER: 'La date de fin doit être supérieure à la date actuelle!',
      STARTHAVEVALUE: 'La date de début ou la date de fin doit avoir une valeur!',
      ENDGREATERSTART: 'La date de fin doit être supérieure à la date de début!',
      STARTENDALUE: "L'heure de début ou l'heure de fin doit avoir une valeur!",
      EXACTLYEQUAL: 'Le temps ne peut pas être exactement égal!',
      CONFSTAGE: "Confirmer la suppression de l'étape?",
      BADREQ: 'Mauvaise demande',
      PARAMNEED: 'Les paramètres doivent être remplis avant de sauvegarder',
      FLOWSUCC: 'Mise à jour du flux de travail réussie',
      CONFIGURATIONSUCC: 'Mise à jour de configuration réussie',
      WASUPDATE: ') a été mis à jour!',
      CROSSCLICK: 'Clic croisé',
      FLOWADDSUCC: 'Flux de travail ajouté avec succès',
      WASADD: ') a été ajouté!',
      FORMFAIL: 'Échec de la validation du formulaire!',
      CONFOPER: "Confirmer la suppression de l'opération?",
      VERSIONERROR: 'Erreur de version!',
      ONLY16: 'Seuls 1-16 caractères de lettres, chiffres et symboles spéciaux ( _ - .) sont autorisés.',
      FORKSUCC: 'Duplication du flux de travail réussie!',
      WASFORK: ' était fourchu. Nouvelle priorité :',
      FLOWSPACE: 'Flux de travail ',
      OPERFORKSUCC: "Duplication de l'opération réussie!",
      OPERFLOWSPACE: 'Opération ',
      OPERATION: ' Opération',
      CONFDELOPER: "Confirmer la suppression des profils?",
      DODELOPER: "Voulez-vous supprimer les profils",
      DELSUCCESS: 'Suppression réussie!',
      PLEOPERFIRST: "Veuillez sélectionner les opérations d'abord!",
      CONFSELECTOPER: "Confirmez la suppression des profils sélectionnés?",
      DOSELOPER: "Voulez-vous supprimer les profils sélectionnés?",
      DELOPERSUCC: 'Supprimez les profils sélectionnés avec succès!',
      CONFACTIONRE: "Confirmer le retrait de l’opération?",
      STAGE: ' étape ?',
      ACTION: ' Profils?',
      OPERUPDATESUCC: "Profils mis à jour avec succès",
      OPERADDSUCC: 'Profils ajoutés avec succès',
      ALARMADDSUCC: "Notification d'alarme ajoutée avec succès",
      CONFLICT: 'Conflit',
      ALARMNOTNAME: "Notification d'alarme avec le nom ",
      ALREADYEXIST: ' existe déjà',
      CONTAINDATA: ' contient des données incohérentes',
      NAMEERROR: 'Erreur de nom!',
      ONLYNAMESYM: 'Seules les lettres, chiffres et caractères spéciaux de 1 à 64 caractères sont autorisés (-Space)',
      CLONENOTI: "Notification d'alarme clonée avec succès!",
      WANCLONE: " a été cloné. Nouvelle notification d'alarme : ",
      CONFIGUPDATESUCC: 'Mise à jour de la configuration réussie',
      SETOPERSUCC: "Réglage de l'opération du dispositif réussi!",
      SETOPERFAIL: "Échec du réglage de l'opération du dispositif!",
      TASKSUCC: 'Tâche ajoutée avec succès!',
      LABELSUCC: "Modification de l'étiquette réussie!",
      LABELFAIL: "Échec de la modification de l'étiquette!",
      UPDATEDEV: 'mise à jour réussie',
      CMDSENQUSUCC: "Commande mise en file d'attente avec succès!",
      FORKNOT: "Notification d'alarme dupliquée avec succès!",
      WANIMPORT: ") n'a pas été importé.",
      NOTIIMPORTFAIL: "Échec de l'importation de la notification d'alarme",
      IMPORTSUCC: 'importation réussie!',
      GROUPCREATESUCC: 'Groupe de dispositifs créé avec succès',
      IMPORTTOGROUP: ' les appareils sont importés dans le groupe ',
      GNAMEEXIST: 'Ce nom de groupe existe déjà',
      SAVESRSSUCC: 'Enregistrement des paramètres du rapport de synthèse réussi',
      PLEASECONF: 'Le sujet ne peut pas être modifié après le réglage, veuillez confirmer',
      ADDPRODSUCC: 'Ajout du produit réussi.',
      ADDPARAMSUCC: 'Ajout du paramètre réussi.',
      UPDATEPRODSUCC: 'Produit mis à jour avec succès.',
      UPDATERANSUCC: 'Réseau d’accès radio mis à jour avec succès.',
      UPDATEAPNSUCC: 'Réseau WiFi AP mis à jour avec succès.',
      UPDATEMESHSUCC: 'Réseau WiFi Mesh mis à jour avec succès.',
      UPDATEPARAMSUCC: 'Mise à jour du paramètre réussie.',
      PLEASEFILL: "Veuillez remplir les éléments requis marqués d'une étoile d'abord!",
      UPDATEPERM: 'Mise à jour du type de permission réussie',
      UPDATEPERMDEV: 'Mise à jour des appareils autorisés réussie',
      ADDSUCC: 'ajout réussi',
      UPDATESUCC: 'mise à jour réussie',
      DONE: 'fait',
      WARNING: 'Avertissement!',
      PARAMNAMEEXIST: 'Le nom du paramètre existe déjà!',
      SCRITEMPTY: 'La liste des scripts est vide!',
      USPGROUPEMPTY: 'La liste des groupes est vide!',
      GROUPNAMEREQ: 'Le nom du groupe est requis',
      OPERMODEREQ: "Le mode d'opération est requis",
      FLOWIMPORTFAIL: "Échec de l'importation du flux de travail",
      SYSSETSUCC: 'Enregistrement des paramètres du système réussi!',
      SYSSETFAIL: "Échec de l'enregistrement des paramètres du système!",
      DEVSETSUCC: 'Enregistrement des paramètres du dispositif réussi!',
      DEVSETFAIL: "Échec de l'enregistrement des paramètres du dispositif!",
      PROSETSUCC: 'Enregistrement des paramètres du produit réussi!',
      PROSETFAIL: "Échec de l'enregistrement des paramètres du produit!",
      SYSPRESUCC: 'Enregistrement des préférences système réussi!',
      SYSPREFAIL: "Échec de l'enregistrement des préférences système!",
      DELETEUSER: "Supprimer l'utilisateur ",
      CHANGEUSER: "Modifier l'utilisateur ",
      SPFAIL: ' a échoué',
      ACTIVESUSS: ' statut actif réussi',
      ACTIVEFAIL: ' statut actif échoué',
      IMAGEOVERSIZE: "L'image est trop grande.",
      PWDNOTSAME: 'Confirmer le mot de passe ne correspond pas.',
      PWDREQ: 'Le mot de passe est requis.',
      FORMATINCORRECT: 'Le format de la date d’expiration est incorrect.',
      MUSTADMIN: 'Le produit de l’utilisateur ADMIN doit être "ADMIN".',
      PRODUCTREQ: 'Le produit est requis.',
      SELECTADMIN: 'Seul le rôle ADMIN/CSR peut choisir "ADMIN".',
      ROLEREQ: 'Le rôle de l’utilisateur est requis.',
      AVATARSUCC: 'Mise à jour de l’avatar réussie.',
      AVATARFAIL: 'Échec de la mise à jour de l’avatar.',
      EMAILSUCC: 'Mise à jour de la boîte aux lettres réussie',
      EMAILFAIL: 'La mise à jour de la boîte aux lettres a échoué.',
      UPPERLIMIT: 'Le nombre de dispositifs pouvant être ajoutés a dépassé la limite supérieure!',
      MAXLIMIT: 'La limite maximale de dispositifs est de ',
      NOTSAVETITLE: 'Sauvegarder les modifications ou non?',
      NOTSAVECONTENT: 'Les modifications que vous avez apportées ne seront pas enregistrées.',
      CONFIRMROLE: 'Confirmer la suppression du rôle ?',
      DOROLE: 'Voulez-vous supprimer le rôle ?',
      DOSELECTROLE: 'Voulez-vous supprimer le rôle sélectionné ?',
      DELROLESUCC: 'Rôles supprimés avec succès.',
      ROLEUPSUCC: 'Rôle mis à jour avec succès.',
      ROLEADDSUCC: 'Rôle ajouté avec succès.',
      CHANGEWILLBELOSE: "Si vous n'enregistrez pas vos modifications, elles seront perdues.",
      SYSTEMRETURNLOGIN: "Système bientôt de retour à la page de connexion",
      SAVEEVENTTIP: 'Veuillez sélectionner informer l’événement avant d’enregistrer à cette étape !',
      SAVENOTIFYORDEVICEPARAMTIP: 'Veuillez ajouter le paramètre de notification ou la condition du paramètre de l’appareil avant de sauvegarder à ce stade !',
      SAVEDEVICEFAULTTIP: 'Veuillez ajouter une condition de paramètre de défaut d’appareil avant d’enregistrer à cette étape !',
      ADDMEMBERSUC: 'Ajout de membres au groupe avec succès !',
      ADDMEMBERFAIL: 'L’ajout de membres du groupe a échoué !',
      SMTPHEALTHY: 'Test de réussite de l’email.',
      SNMPHEALTHY: 'Testez la réussite de l’interruption SNMP.',
      XMPPHEALTHY: 'Testez le succès de XMPP.',
      GENSERVERREPORT: 'Générez avec succès le rapport du serveur.',
      WORKFLOWCLONESUCCESS: 'Clone de workflow réussi!',
      CONFIGURATIONWCLONESUCCESS: 'Configuration clone réussie!',
      HASBEENCLONED: 'A été cloné!',
      HASBEENFORKED: 'a été fourché !',
      APNAMEEDITFAIL: 'Échec de la modification du nom AP.',
      ADDAPNSUCC: 'Ajout du réseau WiFi AP réussi, et un groupe du même nom a été créé simultanément.',
      ADDRANSUCC: 'Ajout du réseau d\'accès radio réussi, et un groupe du même nom a été créé simultanément.',
      ADDMESHSUCC: 'Ajout du réseau WiFi Mesh réussi, et un groupe du même nom a été créé simultanément.',
      TAGERROR: "Seuls 0 à 32 caractères de lettres, de chiffres, de tirets (-), de soulignements (_), de points (.) et d'espaces sont autorisés."
    },
    PM: {
      PMWORD: 'PM',
      PMPARAM: 'Paramètre PM',
      PMCHART: 'Graphique PM',
      PERFORMANCEREPORT: 'Rapport de performance',
      PMSTATEITEMS: 'Les ID de métrique sélectionnés contiennent un ou plusieurs éléments de suivi!',
      PERFORMANCEREPORT_DESCRIPTION: "Liste des rapports serveur (AMP) générés avec des informations détaillées telles que l'état des appareils et des services.",
      PMSTATISTICS: 'Statistiques PM',
      SERIALNUMBER: 'Numéro de série',
      TARGETSERIALNUMBER: 'Numéro de série cible',
      TARGETSN: 'Numéro de série cible',
      TARGETGROUP: "Groupe cible",
      TARGET: "Cible",
      GROUP: 'Groupe',
      PARAMNAME: 'Nom du paramètre',
      PARAMPATH: 'Chemin du paramètre',
      CONDITION: 'Condition',
      CONDITIONS: 'Conditions',
      PARAMVALUE: 'Valeur du paramètre',
      FROM: 'De',
      TO: 'À',
      CREATEDBY: 'Créé par',
      BEGINTIME: 'Heure de début',
      ENDTIME: 'Heure de fin',
      TIME: 'Temps',
      UPDATETIME: 'Heure de mise à jour',
      MODELNAME: 'Nom du modèle',
      PRODUCTCLASS: 'Classe de produit',
      TIMERANGE: 'Plage de temps',
      OUI: 'OUI',
      METRICRULE: 'Règle de mesure',
      ALL: 'Tout',
      CONFIRM_DELETE: 'Confirmer la suppression',
      DO_DELETE: 'Voulez-vous supprimer la sélection ',
      DODELETE: 'Voulez-vous supprimer ',
      DELETESUCCESS: 'Suppression réussie',
      DELETEFAIL: 'Échec de la suppression',
      PLESESELECT: 'Veuillez sélectionner ',
      CONFIRM_REFRESH: 'Confirmer la nouvelle recherche',
      DO_REFRESHSELECT: 'Voulez-vous revoir les éléments sélectionnés',
      DO_REFRESH: 'Voulez-vous refaire la recherche',
      REFRESHSUCCESS: 'Nouvelle recherche réussie',
      REFRESHFAIL: 'Échec de la nouvelle recherche',
      EXPIREDUPDATESUCCESS: "Toutes les données expirées ont été mises à jour avec succès.",
      DATAEXPIRED: "Données expirées",
      ADDCONDITION: 'Ajouter une condition',
      DELETECONDITION: 'Supprimer une condition',
      SEARCH: 'Rechercher',
      REFRESH: 'Actualiser',
      REFRESHALL: "Tout rafraîchir",
      SEARCHRESULT: 'Résultats de la recherche',
      VIEWCHART: 'Voir le graphique',
      CHART: 'Graphique',
      SAVERULE: 'Enregistrer le nom de la règle',
      UPDATERULE: 'Mettre à jour le nom de la règle',
      NAME: 'Nom',
      DESCRIPTION: 'Description',
      CLOSE: 'Fermer',
      SAVE: 'Enregistrer',
      DELETE: 'Supprimer',
      DELETEALL: 'Tout supprimer',
      GOTDEVICEINFO: "Accéder à la page d'informations sur l'appareil pour le ",
      VIEWALLCHART: "Veuillez sélectionner des appareils pour afficher les graphiques de performances. (max : 20)",
      DURATION: "Durée",
      NOTIFICATIONTOOLTIP: 'Convertir en notification',
      REFRESHRULETOOLTIP: 'Règle de recherche',
      DEVICECOUNT: 'Appareils',
      FAIL: 'Échec !',
      REFRESHLOADING: 'Chargement de la nouvelle recherche en cours...',
      DOWNLOAD: 'Télécharger',
      CONFIRM_DOWNLOAD: 'Confirmer le téléchargement',
      DOWNLOADSUCCESS: 'Téléchargement réussi',
      DOWNLOADFAIL: 'Échec du téléchargement',
      DO_DOWNLOAD: 'Voulez-vous télécharger les appareils sélectionnés ?',
      DODOWNLOAD: 'Voulez-vous télécharger ',
      OPENSEARCHBAR: 'Ouvrir la barre de recherche',
      HIDESEARCHBAR: 'Masquer la barre de recherche',
      LASTMACHINGTIME: 'Dernière heure de correspondance',
      RESEARCH: 'Recherche',
      RESEARCHRULETOOLTIP: 'Règle de recherche',
      TRACKING: 'Suivi',
      RESULT: 'Résultat',
      RESEARCHALL: 'Rechercher tout',
      RESULTTOOLTIP: "Nombre d'appareils répondant à la condition",
      LASTMACHING: "Dernière correspondance",
    },
    REFURBISHMENT: {
      REFURBISHMENTSTATISTICS: 'Statistiques rénovation',
      REFURBISHMENTTIME: 'Temps de rénovation',
      REFURBISHMENTCOUNT: 'Anzahl der Sanierungen',
      INSTALLATIONTIME: 'Installationszeit',
    },
    CARE: {
      TITLE: 'care',
      GENERALINFO: 'Informations générales',
      GENERALINFODESCRIPTION: 'Device\'s general information.',
      MAP: 'Emplacement',
      MAPDESCRIPTION: 'Device\'s location on Google Map.',
      WIFICLIENTLIST: 'Liste des clients Wi-Fi',
      WIFICLIENTLISTDESCRIPTION: 'Clients WiFi connectés à l\'appareil.',
      ERRORSTATUS: 'Statut des erreurs',
      ERRORSTATUSDESCRIPTION: "Liste des erreurs de l'appareil comprenant le code d'événement, la description de l'erreur et l'heure de l'erreur.",
      ERRORSTCARE: "Obtenir un appareil",
      SELECTDEVICE: "Choisir un appareil",
      SERIALNUMBER: "numéro de série",
      PRODUCTNAME: "Nom du produit",
    },
    FIVEGC: {
      CELL_CONNECTED: "Cell Connectée",
      CELL_CONNECTED_DESCRIPTION: "Affiche le nombre de radios connectées et fonctionnant correctement.",
      CELL_DISCONNECTED: "Cellule Déconnectée",
      CELL_DISCONNECTED_DESCRIPTION: "Affiche le nombre de radios déconnectées.",
      ACTIVE_UE: 'UE actif',
      ACTIVE_UE_DESCRIPTION: 'Le nombre de cellules avec des UE actifs.',
      NO_ACTIVE_UE: 'UE inactif',
      NO_ACTIVE_UE_DESCRIPTION: "Indique le nombre de cellules sans activité. Cela ne signifie pas nécessairement qu'il n'y a pas de UE attachés ; ils pourraient être attachés mais non actifs.",
      CELLS_WITHOUT_ATTACHED_UE: 'Cellules sans UE attaché',
      CELLS_WITHOUT_ATTACHED_UE_DESCRIPTION: "Indique le nombre de radios connectées mais qui n'ont pas de UE attaché.",
      CELLS_WITH_ACTIVE_UE: "Cellules avec Utilisateurs Actifs",
      CELLS_WITH_ACTIVE_UE_DESCRIPTION: "Ce widget affiche un ensemble d'indicateurs d'activité sous forme de graphique à barres, montrant l'état d'activité des cellules sur différentes plages, y compris aucune activité, 1 à 5 utilisateurs actifs, 6 à 10 utilisateurs actifs, 11 à 20 utilisateurs actifs et 20 utilisateurs actifs ou plus.",
      CELLS_LIST: "Liste des Cellules",
      CELLS_LIST_DESCRIPTION: "Ce widget affiche la liste des cellules connectées au 5G Core.",
      ALARM_LIST: "Liste des Alarmes",
      ALARM_LIST_DESCRIPTION: "La liste des alarmes fournit des informations sur diverses alarmes dans le système.",
      FIVECORENETWORK: "Réseau Central 5G",
      FIVECORENETWORK_DESCRIPTION: "Ce widget fonctionne comme un lien vers le 5G Core, configurant l'URL du 5G Core dans le système/réglages.",
      CELL_THROUGHPUT: "Débit de Cellule",
      CELL_THROUGHPUT_DESCRIPTION: "Ce widget affiche le débit de téléchargement et de téléversement actuel de la cellule.",
      UE_THROUGHPUT_BY_CELL: "Débit UE par cellule",
      UE_THROUGHPUT_BY_CELL_DESCRIPTION: "Débit UE par cellule affiche le débit total en téléchargement et en téléversement de tous les UE connectés à une cellule spécifique, offrant un aperçu de la performance de transmission des données.",
      UE_LIST: "Liste UE",
      UE_LIST_DESCRIPTION: "Les informations UE fournissent des détails sur l'équipement utilisateur (UE) connecté au réseau central 5G.",
      UE_5QI_PACKET: "Paquet 5QI de l'UE",
      UE_5QI_PACKET_DESCRIPTION: "Ce widget affiche le paquet 5QI de l'UE, incluant divers indicateurs de trafic et des taux de perte pour les données montantes et descendantes.",
      ACTIVE: 'Actif',
      INACTIVE: 'Inactif',
      STATUS: 'Statut',
      NAME: 'Nom',
      STATE: 'État',
      gNBID: 'ID gNB',
      BAND: 'Bande',
      SESSIONS: 'Sessions',
      DATA: 'Données',
      PLMN: 'PLMN',
      TAC: 'TAC',
      IP: 'IP',
      SEVERITY: 'Gravité',
      ALARM_ID: 'ID Alarme',
      EVENT_TYPE: 'Type d\'événement',
      EVENT_TIME: 'Heure de l\'événement',
      PROBABLE_CAUSE: 'Cause probable',
      SPECIFIC_PROBLEM: 'Problème spécifique',
      OBJ_CLASS: "Classe d'objet",
      ADD_TEXT: "Ajouter du texte",
      THROUGHPUT_HISTORY: "Historique du débit",
      STATUS_HISTORY: "Historique du statut",
      HISTORY: 'Histoire',
      IMSI: 'IMSI',
      PHONENUMBER: "Numéro de téléphone",
      IMEI: 'IMEI',
      SUB_TYPE: 'Sous-type',
      REG_TYPE: "Type d'enregistrement",
      LOCAL_ATTACHMENT: 'Attachement local',
      LAST_ACTIVITY_TIME: "Dernière activité",
      REGISTRATION_TIME: "Temps d'enregistrement",
      DEREGISTRATION_TIME: "Temps de désinscription",
      UL_THROUGHPUT: 'Débit montant',
      DL_THROUGHPUT: 'Débit descendant',
      SUPI: 'SUPI',
      FIVEQI: '5QI',
      UL_INGRESS: 'Entrée montante',
      UL_EGRESS: 'Sortie montante',
      UL_DROPPED: 'Paquets montants perdus',
      UL_TOTAL_INGRESS: 'Total entrée montante',
      UL_TOTAL_EGRESS: 'Total sortie montante',
      UL_TOTAL_DROPPED: 'Total paquets montants perdus',
      DL_INGRESS: 'Entrée descendante',
      DL_EGRESS: 'Sortie descendante',
      DL_DROPPED: 'Paquets descendantes perdus',
      DL_TOTAL_INGRESS: 'Total entrée descendante',
      DL_TOTAL_EGRESS: 'Total sortie descendante',
      DL_TOTAL_DROPPED: 'Total paquets descendantes perdus',
      ALARM: 'Alarme',
      ALARM_DESCRIPTION: 'Les dernières alarmes du réseau 5G Core en temps réel. Elles incluent la gravité de l’alarme, l’horodatage et une brève description, permettant une identification et une réponse rapides aux problèmes du réseau.',
      ue_activity: 'Activité UE du Cœur 5G',
      ue_activity_DESCRIPTION: "Ce diagramme circulaire illustre l'état d'activité de l'Équipement Utilisateur (UE).",
      ue_presence: 'Présence UE du Cœur 5G',
      ue_presence_DESCRIPTION: "Indique le nombre d'UE attachés (connectés) et détachés (déconnectés).",
      cells_info: 'Cellules du Cœur 5G',
      cells_info_DESCRIPTION: 'Les cellules du Cœur 5G fournissent des informations détaillées sur les cellules connectées au réseau central 5G.',
      ACTIVITY_DESCRIPTION: {
        ACTIVE: "Le segment actif montre le nombre d'UE attachés qui sont actifs.",
        DATA: 'Le segment des données montre le nombre de sessions de données dans le système. Un UE peut avoir plusieurs sessions de données.',
        CALLS: "Le segment des appels montre le nombre d'UE attachés qui sont actuellement en appel.",
        INACTIVE: "Le segment inactif montre le nombre d'UE attachés qui ne sont pas actifs."
      },
      PRESENCE_DESCRIPTION: {
        ATTACHED: "L'état 'Attaché' indique que le dispositif utilisateur est connecté avec succès au réseau.",
        DETACHED: "L'état 'Détaché' indique que le dispositif utilisateur est déconnecté du réseau ou n'y a pas encore été connecté."
      },
      LICENSE_DESCRIPTION: {
        UE: 'Montre le nombre de sièges de licence UE utilisés et le nombre de sièges de licence UE disponibles.',
        NETWORK: 'Montre le nombre de sièges de licence PDN et le nombre de sièges de licence PDN disponibles.',
        CELLS_4G: 'Le nombre de sièges de licence pour cellules 4G utilisés et le nombre total de sièges de licence pour cellules 4G disponibles.',
        CELLS_5G: 'Le nombre de sièges de licence pour cellules 5G utilisés et le nombre total de sièges de licence pour cellules 5G disponibles.',
      },
      ACTIVITY_CHART_DESCRIPTION: {
        ACTIVE_20PLUS: "Indique le nombre de cellules avec 20 utilisateurs actifs ou plus.",
        ACTIVE_11To20: "Indique le nombre de cellules avec 11 à 20 utilisateurs actifs.",
        ACTIVE_6To10: "Indique le nombre de cellules avec 6 à 10 utilisateurs actifs.",
        ACTIVE_1To5: "Indique le nombre de cellules avec 1 à 5 utilisateurs actifs.",
        NOACTIVE: "Indique le nombre de cellules sans activité. Cela ne signifie pas nécessairement qu'il n'y a pas d'utilisateurs connectés ; ils peuvent être connectés mais inactifs."
      },
      ACTION: {
        RESTARTSYSTEM: 'Redémarrer le système',
        DORESTARTSYSTEM: 'Si vous continuez, TOUS LES SERVICES SERONT TEMPORAIREMENT PERDUS !',
        RESTARTSYSTEM_SUCESS: 'Redémarrage du système réussi !',
        RESTARTSYSTEM_FAIL: 'Échec du redémarrage du système !',
        BACKUPCONFIGURATION: 'Sauvegarder la configuration',
        BACKUPCONFIGURATION_SUCESS: 'Configuration sauvegardée avec succès !',
        BACKUPCONFIGURATION_FAIL: 'Échec de la sauvegarde de la configuration !',
        RESTORECONFIGURATION: 'Restaurer la configuration',
        RESTORECONFIGURATION_SUCESS: 'Configuration restaurée avec succès !',
        RESTORECONFIGURATION_FAIL: 'Échec de la restauration de la configuration !',
        FACTORYRESET: 'Réinitialisation d’usine',
        DOFACTORYRESET: 'Vous êtes sur le point de réinitialiser votre configuration aux paramètres d’usine par défaut !',
        FACTORYRESET_SUCESS: 'Réinitialisation d’usine réussie !',
        FACTORYRESET_FAIL: 'Échec de la réinitialisation d’usine !',
        REFRESHALL: "Tout rafraîchir",
        REFRESHALL_SUCESS: "Tout rafraîchi avec succès !",
        REFRESHALL_FAIL: "Échec du rafraîchissement de tout !",
        SYSTEMMANAGEMENT: "Gestion du système",
      },
    },
    POWER: {
      ENERGYSAVING: "Gestion de l'énergie",
      STARTTIME_MUST_BE_EARLIER: "L'heure de début doit être antérieure à l'heure de fin",
      STARTDATE_MUST_BE_EARLIER: "La date de début ne peut pas être postérieure à la date de fin.",
      POWER_CONSUMPTION_SUMMARY: "Résumé de la consommation d'énergie",
      REAL_AVG_ENERGY: "Énergie moyenne réelle",
      NORMAL_STATE_ENERGY: "Énergie en mode normal",
      ENERGY_CONSUMPTION_BY_POLICY: "Consommation par stratégie",
      POWER_CONSUMPTION: "Consommation d'énergie",
      TX_POWER: "Puissance d'émission",
      NETWORK_USAGE: "Utilisation du réseau",
      // UPLOAD: "Téléversement",
      // DOWNLOAD: "Téléchargement",
      UE_COUNT: "Nombre d'appareils connectés",
      // LOCATION: "Emplacement",
      CURRENT_POLICY: "Stratégie actuelle",
      POLICY_SETTING: "Paramètres de la stratégie",
      ENERGY_POLICY: "Stratégie d'économie d'énergie",
      MILD_SLEEP_DESCRIPTION: "Réduit la puissance d'émission tout en maintenant le fonctionnement complet de l'appareil.",
      MODERATE_SLEEP_DESCRIPTION: "Réduit la consommation et désactive temporairement la radio pour économiser plus d'énergie.",
      WAKEABLE_DEEPSLEEP_DESCRIPTION: "Met l'appareil progressivement en veille, avec réveil automatique si nécessaire.",
      DEEPSLEEP_DESCRIPTION: "Met complètement l'appareil hors tension. Un réveil manuel est nécessaire.",
      SCHEDULE_SETTING: "Paramètres de planification",
      POLICY_LIST: "Liste des stratégies",
      DURATION: "Durée",
      ENERGY: "Consommation d'énergie",
      POWER_CONSUMPTION_PER_DEVICE: "Consommation par appareil",
      DL_PRB_LOADING: "Charge PRB descendante",
      UL_PRB_LOADING: "Charge PRB montante",
      TOTAL_POWER_CONSUMPTION: "Consommation totale d'énergie",
      NAME: "Nom",
      SCHEDULE: "Calendrier",
      CONDITION: "Condition",
      ACTIVE: "Actif",
      NO_POLICIES_FOUND: "Aucune stratégie trouvée.",
      NO_POLICY_CONFIGURED: "Aucune stratégie d'économie d'énergie configurée pour cet appareil.",
      ENABLE_TO_ADD_POLICIES: "Vous pouvez ajouter des stratégies une fois le contrôle d'économie d'énergie activé.",
      ENERGY_SAVING_NOT_ENABLED: "L'économie d'énergie n'est pas activée.",
      NO_POLICY_SETTINGS_AVAILABLE: "Aucun paramètre de stratégie disponible pour cet appareil.",
      ENABLE_TO_CONFIGURE_POLICY: "Veuillez activer le contrôle d'économie d'énergie pour configurer ou consulter les détails de la stratégie.",
      ENERGY_MODE: "Mode d'économie d'énergie",
      TRAFFIC_LOADING: "Charge du trafic",
      UE_CONTEXT: "Contexte UE",
      POLICY_DISABLE_TITLE: "Désactiver le mode économie d'énergie ?",
      POLICY_DISABLE_TEXT: "Une fois désactivé, l'appareil reviendra à une consommation énergétique normale. Voulez-vous vraiment le désactiver ?",
    }
  }
}
