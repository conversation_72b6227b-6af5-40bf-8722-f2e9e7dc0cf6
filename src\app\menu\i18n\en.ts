export const locale = {
  lang: 'en',
  data: {
    MENU: {
      HOME: 'Home',
      SAMPLE: 'Sample'
    },
    DASHBOARD: {
      TITLE: 'Dashboard',
      AVGSESSIONS: {
        TITLE: 'Avg Sessions',
        VIEWDETAILS: 'View Details',
        LAST1DAY: 'Last 24 hours',
        LAST7DAYS: 'Last 7 Days',
        LAST15DAYS: 'Last 15 Days',
        LAST28DAYS: 'Last 28 Days',
        LAST30DAYS: 'Last 30 Days',
        LASTMONTH: 'Last Month',
        LASTYEAR: 'lAST Year',
      },
      MAP: 'Location',
      SESSIONDURATION: 'Session Duration',
      SESSIONDURATIONDESCRIPTION: "Historical chart of the average of Session duration for all Devices at regular intervals.Session duration: The total time spent in a session between the Device and AMP.",
      SESSIONRATE: 'Session Rate',
      SESSIONRATEDESCRIPTION: "Historical chart of Request frequency by CWMP for Online Devices.",
      LATENCY: 'Request Latency',
      LATENCYDESCRIPTION: "Historical chart of the average of Request latency for all Devices at regular intervals.Request Latency: The total time spent in a request between the Device and AMP.",
      REGISTERED_COUNT_DISTRIBUTION: 'Registered Devices',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: 'Distribution chart of Registered Devices for each Product.',
      PROVISIONINGTYPEDISTRIBUTION: "Provisioning Types",
      PROVISIONINGTYPEDISTRIBUTION_DESCRIPTION: "Distribution of all provisioning types which is selected for each registered product.",
      ONLINE_COUNT_DISTRIBUTION: 'Online Devices',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: 'Distribution chart of Online Devices counts for each product.',
      HISTORYONLINEDEVICE: 'Online Devices',
      ONLINEDEVICEDESCRIPTION: 'Historical chart of number of Online Devices for each Product.',
      SOFTWARE_VERSION_DISTRIBUTION: 'Software Version',
      SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION: 'Distribution chart of Software version for Online Devices.',
      PROVISIONING_CODE_DISTRIBUTION: 'Provisioning Code',
      PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION: 'Distribution chart of Provisioning Code for Online Devices.',
      XMPP_STATUS_DISTRIBUTION: 'XMPP Status',
      XMPP_STATUS_DISTRIBUTION_DESCRIPTION: 'Distribution chart of XMPP status for Online Devices.',
      IMS_STATUS_DISTRIBUTION: 'IMS Status Distribution',
      IMS_STATUS_DISTRIBUTION_DESCRIPTION: 'Distribution chart of IMS registration status for Online Devices.',
      SIM_STATUS_DISTRIBUTION: 'SIM Status',
      SIM_STATUS_DISTRIBUTION_DESCRIPTION: 'Distribution chart of SIM connection status for Online Devices.',
      IPSEC_STATUS_DISTRIBUTION: 'IPSec Status',
      IPSEC_STATUS_DISTRIBUTION_DESCRIPTION: 'Distribution chart of IPSec tunnel connection status for online devices.',
      TOTAL: 'Total',
      ONLINE: 'Online',
      ONLINE_DEVICE: 'Online Devices',
      ONLINE_DEVICE_DESCRIPTION: 'Total number of Online Devices.',
      GROUPS_COUNT: 'Groups',
      GROUPS_COUNT_DESCRIPTION: 'Total number of Groups.',
      ONLINE_USERS: 'Online Users',
      ONLINE_USERS_DESCRIPTION: "The total number of Users who are logged in within half hour.",
      UE_COUNT: 'UE',
      UE_COUNT_DESCRIPTION: 'Total number of UE(User Equipment ) attached to the Small Cell.',
      ALARMS_TOTAL: 'Total Alarms',
      ALARMS_TOTAL_DESCRIPTION: 'Total number of Alarms reported by the Device.',
      ALARMS_SERVERITY: 'Alarms',
      ALARMS_SERVERITY_DESCRIPTION: 'Total number of Alarms with different Severity such as Critical, Major, Minor and Warning.',
      GROUP_LIST: 'Group List',
      GROUP_LIST_DESCRIPTION: 'List created Groups with detailed information such as number of Devices , Alarms with different Severity.',
      COVERMAP: 'Coverage Map',
      COVERMAP_DESCRIPTION: 'Displays the locations and radio coverage of the devices in the Group.',
      ALARM_LIST: 'Alarm List',
      ALARM_LIST_DESCRIPTION: 'List all Alarm reported by the Device including Cleared, Un-cleared with Severity, Event time and Probable Cause.',
      SYSTEM_EVENT_LIST: 'System Events',
      SYSTEM_EVENT_LIST_DESCRIPTION: 'List all logged events which are related to communication and access failures.',
      STSTEM_INFORMATIONS: 'System Information',
      STSTEM_INFORMATIONS_DESCRIPTION: 'AMP system information or server report content.',
      TOTAL_CLIENTS: 'Total WiFi RSSI Distribution',
      TOTAL_CLIENTS_DESCRIPTION: 'Distribution chart of WiFi clients with different levels of RSSI.',
      TOTAL_CLIENTS_COUNT: 'WiFi Clients',
      TOTAL_CLIENTS_COUNT_DESCRIPTION: 'Total number of  WiFi Clients connected to WiFi APs.',
      EXCELLENT_CLIENTS: 'Excellent',
      GOOD_CLIENTS: 'Good',
      POOR_CLIENTS: 'Poor',
      STATISTICSOFTOTALCLIENTS: 'Total WiFi RSSI Distribution Records',
      STATISTICSOFTOTALCLIENTS_DESCRIPTION: 'Historical chart of WiFi Clients with different levels of RSSI.',
      GROUPNAME: 'Group Name',
      PRODUCTNAME: 'Product Name',
      MANAGEMENTSCOPE: 'Management Scope',
      REGION: 'Region',
      LOCATION: 'Location',
      CONFIGURATION: 'Configuration',
      APS: 'Total APs',
      TOTALCLIENTS: 'Total Clients',
      EXCELLENTCLIENTS: 'Excellent Clients',
      GOODCLIENTS: 'Good Clients',
      POORCLIENTS: 'Poor Clients',
      EXCELLENT: 'Excellent',
      GOOD: 'Good',
      POOR: 'Poor',
      EXCELLENT_DESCRIPTION: 'RSSI > -65dBm',
      GOOD_DESCRIPTION: '-65dBm < RSSI < -80dBm',
      POOR_DESCRIPTION: 'RSSI < -80dBm',
      TOTALCLIENTSTABLE: 'Clients Table',
      CLIENTS: 'WiFi Clients Information',
      ONLINEAPS: 'Online APs',
      TAGS: 'Tags',
      GROUPSLOCATION: 'Groups Location',
      GROUPSLOCATION_DESCRIPTION: 'Display the locations and basic information of all Groups on the map.',
      DEVICESLOCATION: 'Devices Location',
      DEVICESLOCATION_DESCRIPTION: 'Display the locations and basic information of all Devices on the map.',
    },
    DEVICES: {
      WIFICLIENT: 'WiFi Clients',
      WIFIAPNAME: 'WiFi AP Name',
      WIFIAPROLE: 'WiFi AP Role',
      WIFIAPCONFVERSION: 'WiFi AP Conf. Version',
      WIFIAPNCONFVERSION: 'WiFi APN Conf. Version',
      TAGS: 'Tags',
      LIST: 'Device List',
      SERIAL_NUMBER: 'Serial Number',
      MODEL_NAME: 'Model Name',
      FIRMWARE: 'Firmware',
      LABEL: 'Label',
      GROUP: 'Group',
      PRODUCT: 'Product',
      LAST_CONNECTED: 'Last Connected',
      LAST_EVENT: 'Last Event',
      UPTIME: 'Uptime',
      TIME_ZONE: 'Time Zone',
      ACTIVE: 'Active',
      BAND: 'Band',
      CHANNEL: 'Channel',
      BANDWIDTH: 'Bandwidth',
      UTILIZATION: 'Utilization',
      RECEIVED: 'Received',
      SENT: 'Sent',
      DOWNLINK_RATE: 'Downlink Rate',
      UPLINK_RATE: 'Uplink Rate',
      MODE: 'Mode',
      CONNECTTIME: 'Connect Time',
      ERRORCODE: 'Event Code',
      ERRORDESCRIPT: 'Error Descript',
      ERRORTIME: 'Error Time',
      DAILYSENT: 'Daily Sent',
      DAILYRECEIVED: 'Daily Received',
      ACCESSCOUNT: 'Access Count',
      UNINSTALLEDTIME: 'Uninstalled Time',
      DATASIZE: 'Data Size',
      CACHESIZE: 'Cache Size',
      SERVICEDISCOVERYSERVER: 'Service Discovery Server',
      ACTICE_BCG_SERVERS: 'Active BCG Servers',
      POWERCONSUMPTION: 'Power Consumption',
      STATE: 'State',
      CONTAINERVERSION: 'Container Version',
      APPLICATIONVERSION: 'Application Version',
      ENABLE: 'Enable',
      CELL_RESERVED_FOR_OPERATOR_USE: 'Cell Reserved For Operator Use',
      EUTRA_CARRIER_ARFCN: 'EUTRA Carrier ARFCN',
      BLACKLISTED: 'Blacklisted',
      VENDORCLASSID: 'Vendor Class ID',
      EARFCNDOWNLOAD: 'Earfcn Download',
      DOWNLOADBANDWIDTH: 'Download Bandwidth',
      UPLOADBANDWIDTH: 'Upload Bandwidth',
      REFERENCESIGNALPOWER: 'Reference Signal Power',
      SECURITY: 'Security',
      SEVERITY: 'Severity',
      ALARMID: 'Alarm ID',
      EVENTTYPE: 'Event Type',
      EVENTTIME: 'Event Time',
      PROBABLECAUSE: 'Probable Cause',
      SPECIFICPROBLEM: 'Specific Problem',
      ACKUSER: 'Ack User',
      ACKTIME: 'Ack Time',
      ADDITIONALTEXT: 'Additional Text',
      ADDITIONALINFORMATION: 'Additional Information',
      PEER: 'Peer',
      DURATION: 'Duration',
      CONNECT: 'Connect',
      START: 'Start',
      END: 'End',
      UPLOAD: 'Upload ',
      DOWNLOAD: 'Download',
      DOWNLOADDATAMODEL: 'Download whole Data model',
      DOWNLOADALL: 'Download All',
      DOWNLOADSELECT: 'Download Selected',
      TIME: 'Time',
      UPLOADRESULT: 'Upload Test Result',
      DOWNLOADRESULT: 'Download Test Result',
      EVENT: 'Event',
      LOGLEVEL: 'Log Level',
      REQUEST: 'Requests',
      CREATED: 'Created',
      IMEI: 'IMEI',
      MAC: 'MAC',
      IP: 'IP',
      RSSI: 'RSSI',
      SSID: 'SSID',
      BSSID: 'BSSID',
      APSN: 'AP S/N',
      APNAME: 'AP Name',
      APMAC: 'AP MAC',
      APIP: 'AP IP',
      LISTDESCRIPTION: 'List all allowed Devices with common information such as Serial Number, MAP and IP Address.',
      SMALLCELL_LIST: 'Small Cell List',
      SMALLCELL_LISTDESCRIPTION: 'List all allowed Small Cell belonging to Radio Access Network with specific information such as UEs,  PCI and GNB ID.',
      WIFI_AP_LIST: 'WiFi AP List',
      WIFI_AP_LISTDESCRIPTION: 'List all allowed WiFi APs belonging to WiFi AP Network with specific information such as Clients,  Channel and Channel Utilization.',
      WIFI_MESH_LIST: 'WiFi Mesh List',
      WIFI_MESH_LISTDESCRIPTION: 'List all allowed WiFi Mesh APs belonging to WiFi Mesh Network with specific information such as Clients,  Channel and Channel Utilization.',
      CURRENTNUMBERS: 'Current Alarm Numbers.',
      ALARMMGMTDESCRIPTION: "List all Alarm reported by the Device including Cleared, Un-cleared with Severity, Event time and Probable Cause.",
      REGISTERDEVICE: 'Register Device',
      REGISTERSMALLCELLDEVICE: 'Register Small Cell  Device',
      REGISTERAPDEVICE: 'Register WiFi AP Device',
      REGISTERNESHDEVICE: 'Register WiFi Mesh Device',
      LIVEUPDATE: 'Live Update',
      SPEEDTEST: 'Speed Test',
      SPEEDTESTDESCRIPTION: "The device uses TR-143 to conduct upload and download speed tests, measuring network performance to ensure optimal data transfer rates.",
      FIVECORE: '5G Core',
      FIVECORENETWORK: '5G Core Network',
      FIVECORENETWORK_DESCRIPTION: 'Provide a link to the 5G Core, configured in the System/Settings with the 5G Core URL.',
      CELL_THROUGHPUT: 'Cell Throughput',
      CELL_THROUGHPUT_DESCRIPTION: "Information about Small Cells' Download and Upload throughput.",
      UE_LIST: 'UE List',
      UE_LIST_DESCRIPTION: 'UE (User Equipment) attached and detached to the 5G Core Network with the detailed information such as State, IMSI, IMEI GNB ID and IP Address.',
      UE_5QI_PACKET: 'UE 5QI Packet',
      UE_5QI_PACKET_DESCRIPTION: 'List 5QI packets of the UE, including various traffic metrics and drop rates for uplink and downlink.',
      BULKDATAPROFILE_DESCRIPTION: "List Bulk Data profiles with detailed information such as Alias, Status, URL, Parameters and Encoded type.",
      SOFTWAREMODULES_DESCRIPTION: "List Software Modules with detailed information such as UUID, Alias, Name, URL and Last updated time.",
      ONLINE_COUNT_DISTRIBUTION: 'Online Devices',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: 'Distribution chart of Online Devices counts for each Product.',
      REGISTERED_COUNT_DISTRIBUTION: 'Registered Devices',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: 'Distribution chart of Registered Devices counts for each Product.',
      CONNECTIVITYTEST: 'Connectivity Test',
      REBOOT: 'Reboot',
      FACTORYRESET: 'Apply Factory Reset',
      UPLOADLOG: 'Upload Log',
      UPGRADEFIRMWARE: 'Upgrade',
      GENERATEREPORT: 'Generate Report',
      ADDFILE: 'Add File Pointer',
      SETTING: 'Manage Preference',
      GENERAL: 'General',
      GENERALSTATUS: 'General Status',
      OPERATION: 'Assign Operations',
      PROTOCOL: 'Protocol',
      SELECTPROTOCOL: 'Select Protocol',
      NETCONFAUTH: 'NETCONF support password and private key authentication, input either a password or private key.',
      REGISTER: 'Register',
      CONNECTIONREQ: 'Connection Request',
      TELEMETRY: 'Telemetry',
      INFO: 'Info',
      MAP: 'Map',
      FAPCONNECTEDCLIENTS: 'FAP Connected Clients',
      FAPCONNECTEDCLIENTSDESCRIPTION: 'Connected FAP Clients.',
      GENERALINFO: 'General Info',
      GENERALINFODESCRIPTION: "Device's general information such as Serial number, Model name and Software version.",
      CELLULARSTATUS: 'Cellular Status',
      CELLULARSTATUSDESCRIPTION: 'Cellular information such as Service status, Access Technology, Band, RSRP, RSRQ and RSRI ',
      WORKFLOWLIST: 'Workflow List',
      WORKFLOWLISTDESCRIPTION: 'List Workflows applied to this Device currently.',
      DATAUSAGE: 'Cellular Data Usage',
      DATAUSAGEDESCRIPTION: 'Historical chart of Cellular Data Usage.',
      CLIENTS: 'Clients',
      CLIENTDESCRIPTION: 'Clients such as Mobile, Laptop, Tablet numbers.',
      UE: 'UE',
      UEDESCRIPTION: 'Total numbers of UEs attached to the Small Cell.',
      SIMCARDINFO: 'SIM Card Info',
      SIMCARDINFODESCRIPTION: 'SIM Card information such as Status, ICCID, IMSI, IMPI and IMPU',
      WIFISTATUS: 'WiFi Radio Status',
      WIFISTATUSDESCRIPTION: "Current radio's settings and status from all avaliable WiFi band(s)/interface(s).",
      WIFICHANNELUTLILIZATION: 'WiFi Channel Utlilization',
      WIFICHANNELUTLILIZATIONDESCRIPTION: 'On-air channel loading from all available bands\' traffic.',
      CONNECTEDHOSTS: 'Connected Hosts',
      CONNECTEDHOSTSDESCRIPTION: 'Informations about the hosts connected to the device.',
      REGSTATUS: 'Registration Status',
      REGSTATUSDESCRIPTION: 'Registration information such as Last Registered Time, Last Disconnected Time and  Last Disconnected Reason.',
      ERRORSTATUS: 'Error Status',
      ERRORSTATUSDESCRIPTION: 'List Errors reported by Device such as Error description and Error time.',
      SPECTRUMDESCRIPTION: "Information about Small Cell's deployment mode with specific Spectrum, such as NR Band, ARFCN and TDD Time Slot.",
      APPLIST: 'Application List',
      APPLISTDESCRIPTION: 'Informations about the applications for the device.',
      SERVICEPRO: 'Service Provider',
      SERVICEPRODESCRIPTION: 'Service provider list for the device.',
      SPECTRUM: 'Spectrum',
      PLMNNEIGHBORLIST: 'Neighbor / PLMN List',
      NEIGHBORLIST: 'Neighbor List',
      NEIGHBORLISTDESCRIPTION: 'Information about ANR Neighbor List, Handover List and PLMN List.',
      HANDOVERLIST: 'Configured neighbor list',
      ANRNEIGHBORLIST: 'ANR Neighbor List',
      UTRANEIGHBORLIST: 'Utra Neighbor List',
      PLMNLIST: 'PLMN List',
      APPSTATUS: 'Device Apps Status',
      APPSTATUSDESCRIPTION: 'Power consumption of the applications for the device.',
      LXCSTATUS: 'LXC Status',
      LXCSTATUSDESCRIPTION: 'Linux container status for the device.',
      SUBSCRIPTION: 'USP Subscription',
      SUBSCRIPTIONDESCRIPTION: 'List Subscription items via USP with detailed information such as Alias, Status, Notification type and Recipient.',
      BULKDATAPROFILE: 'Bulk Data Profile',
      SOFTWAREMODULES: 'Softeware Modules',
      CONTROLLERTRUSTROLE: 'Controller Trust Role',
      CONTROLLERTRUSTROLEDESCRIPTION: 'List the controllerTrust roles that contain detailed information such as aliases, statuses, Permissions, Number Of Entries, etc',
      FIRMWAREIMAGES: 'Firmware Images',
      FIRMWAREIMAGESDESCRIPTION: 'List the firmware images that contain detailed information such as aliases, statuses, bootFailureLog, etc',
      BOOTFAILURELOG: "Boot Failure Log",
      AVAILABLE: "Available",
      DEVICEACTION: 'Device Action',
      CURRENTALARMLIST: 'Current Alarm List',
      ALARMLIST: 'Alarm List',
      ACKALARM: 'Ack Alarm',
      ALARMMGMT: 'Alarm Management',
      ALARMMGMDESCRIPTION: 'List all Alarm reported by the Device including Cleared, Un-cleared with Severity, Event time and Probable Cause.',
      CLEAREDTIME: 'Cleared Time',
      HISTORYALARM: 'History Alarm Management',
      CURRENTYALARM: 'Current Alarm List',
      DATAMODEL: 'Data Model',
      SELECTDATAMODEL: 'Select Data Model',
      DATANODE: 'Data Node',
      DATANODEDESCRIPTION: "Display all parameter nodes reported by the Device to AMP in a tree structure.",
      PARAMETERDATADESCRIPTION: "Detailed information of selected parameter node such as Child Nodes, parameter name, attribute, path and value.",
      PARAMETERDATA: 'Parameter Data',
      SELECTDATANODE: 'Please select parameter from Data Node',
      LOGS: 'Logs',
      LOG: ' Log',
      SESSIONLOG: 'Session Log List',
      SESSIONLOGDESCRIPTION: 'List Session logs between AMP and Device.',
      SESSIONLOGRATE: 'Reporting Rate',
      SESSIONLOGRATEDESCRIPTION: "Statistics of the device's periodic report rate(count) to AMP over the past 24 hours.",
      SESSLOG: 'Session Log',
      PENDINGLOG: 'Pending Operations Log List',
      PENDINGLOGPENDINGLOGS: 'List Pending operation tasks awaiting device reception.',
      OPERATIONLOGS: 'Operation Logs List',
      OPERATIONLOGSDESCRIPTION: 'List operation tasks assigned by AMP that the Device has executed, along with the reported results.',
      CALLLOG: 'Call Log List',
      CALLLOGDESCRIPTION: 'List call records such as Peer, Type and Duration.',
      SPEEDTESTHISTORY: 'Speed Test History',
      CONNECTIVITYTESTHISTORY: 'Connectivity Test History',
      CONNECTIVITYTESTHISTORYDESCRIPTION: 'Connectivity Test History.',
      DEVICEREPORTLIST: 'Device Report List',
      DEVICEREPORTLISTDESCRIPTION: 'List generated summary reports with key parameters for the Device.',
      PMLOG: 'PM KPI Logs',
      PMLOGDESCRIPTION: 'List records of KPI data reported by the Small Cell.',
      ADVANCE: 'Advanced',
      CBSDSTATUS: 'CBSD Status',
      CBSDSTATUSDESCRIPTION: 'Information about Citizen Broadband Service Device such as SAS Provider, CBSD ID, GPs status and Grant State.',
      CBSDCONDIGS: 'CBSD Configs',
      CBSDCONDIGSDESCRIPTION: 'Configuration of  Citizen Broadband Service Device such as CBSD Serial Number, Model and Software Version.',
      TERMINAL: 'Terminal',
      TERMINALDESCRIPTION: 'Provide devices that support remote real-time operation via Terminal.',
      COMMANDXML: 'Command XML',
      COMMANDXMLDESCRIPTION: 'Device Command XML.',
      ACSSTATUS: 'ACS Status',
      DEVICESTATUS: 'Device Status',
      SASTATUS: 'Cell Status',
      RFCONTROL: 'Cell RF Control',
      RFCONTROLDESCRIPTION: 'Provide RF-related settings and switches.',
      ANTENNABEAM: 'Antenna Beam',
      BEAMMENU: 'Beam Menu',
      ANTENNABEAMDESCRIPTION: "Provide changing Small Cell's Antenna Beam angle.",
      BEAMID: 'Beam ID',
      GPSANTENNA: 'GPS Antenna',
      GPSANTENNAPATH: 'GPS Antenna Path',
      ANTENNAPATH: 'Antenna Path',
      GPSANTENNADESCRIPTION: 'Provide selecting GPS Antenna path as External or Internal.',
      DEPLOYMENTMODE: 'Deployment Mode',
      DEPLOYMENTMODEDESCRIPTION: 'Device Deployment Mode.',
      HWMONITOR: 'Machine Diagnostics',
      HWMONITORDESCRIPTION: "Provide enable Small Cell's Diagnostics, such as CPU, Temperature, and Power Consumption.",
      RECONNECT: 'Reconnect',
      DISCONNECT: 'Disconnect',
      DOWNLOADLOG: 'Download Log',
      REPEATLASTCMD: 'Repeat Last Command',
      CLEARSCROLLBACK: 'Clear Scrollback',
      CELLULARDIAGNOSTIC: 'Request Cellular Interface Diagnostic',
      WIFIDIAGNOSTIC: 'Request WIFI Interface Diagnostic',
      SYSHEALTHREBOOT: 'Request System Health Reboot',
      DIFFRENTPROTOCOLNOTALLOWED: 'Batch register two or more different protocol devices is not allowed',
      DEVICEERRCODE1: "Device isn't allowed to be added to non-existent product",
      DEVICEERRCODE2: 'The device has been registered',
      DEVICEERRCODE3: 'SerialNumber is illegal',
      INVALIDPRODUCT: 'Invalid Product',
      WIFIRADIOTHROUGHPUT: 'WiFi Radio Data Usage',
      WIFIRADIOTHROUGHPUT_DESCRIPTION: "Current radio's protocol & data usage from all avaliable WiFi band(s)/interface(s).",
      DISCARDPACKETSSENT: 'Discard Packets Sent',
      ERRORSSENT: 'Sent Error Packets',
      BYTESRECEIVED: 'Received',
      PACKETSRECEIVED: 'Received Packets',
      ERRORSRECEIVED: 'Received Error Packets',
      BEACONINTERVAL: 'Beacon Interval',
      OPERATINGCHANNELBANDWIDTH: 'Bandwidth',
      TXPOWER: 'Transmit Power',
      BYTESSENT: 'Sent',
      PACKETSSENT: 'Sent Packets',
      MAXCLIENTS: 'Max Clients',
      SECUTIRY: 'Security',
      WMM: 'WMM',
      SSIDLIST: 'WiFi SSID List',
      SSIDLIST_DESCRIPTION: 'Current WiFi status about enabling, WMM, bandwidth and data usage on preset SSIDs of this device.',
      REQUESTUPDATEFAIL: 'Request Update Fail',
      SHUTDOWN_SUCCESS: 'Shutdown Success!',
      MAXUES: 'Maximum Number of Concurrent UEs:',
      OSMERROR: "Map loading error. Please check the internet connection or try again later.",
      GOOGLEMAPERROR: "Failed to load Google Maps API. Please check the Google Maps API key or internet connection.",
      PERMISSIONNUMBEROFENTRIES: "Number Of Entries",
      ACTION: {
        ASSIGN_OPERATION: 'Operation Assigned',
        ASSIGN_OPERATION_SUCC: 'Operation Assigned is Successful.',
        ASSIGN_OPERATION_FAIL: 'Operation Assigned is Failed.',
        ASSIGN_OPERATION_SUCCESS_MESSAGE: 'Operation is successful.',
        ASSIGN_OPERATION_WARNING_MESSAGE1: 'Waiting',
        ASSIGN_OPERATION_WARNING_MESSAGE2: 'to get the Operation.',
        ASSIGN_OPERATION_FAIL_MESSAGE: 'Operation Assigned is Failed.',
        CONFIRM_LIVE_UPDATE: 'Confirm Live Update',
        DO_LIVE: 'Do you want to Live Update ',
        DO_LIVE_UPDATE: 'Do you want to Live Update selected Devices?',
        LIVESUCCESS: 'Live Update Success!',
        LIVEFAIL: 'Live Update Fail!',
        CONFIRM_REBOOT: 'Reboot ',
        DOREBOOT: 'During the rebooting, all services provided by the device (including remote management) will be temporarily suspended for several minutes.',
        SELECTREBOOT: 'During the rebooting, all services provided by the selected devices (including remote management) will be temporarily suspended for several minutes.',
        ABOUT_TO_REBOOT: 'About to Reboot...',
        REBOOT_SUCCESS: 'Reboot Success!',
        WAIT_REBOOT: 'Waiting for Reboot...',
        REBOOTFAIL: 'Reboot Fail!',
        REBOOT_TIMED_OUT: 'Reboot timed out!',
        SHUTDOWN_SUCCESS: 'Shutdown Success!',
        CONFIRMFACTORYRESET: 'Factory Reset ',
        DOFACTORYRESET: 'The device will be restored to original manufacturing state, and all user provisioned settings will be erased.',
        SELECTFACTORYRESET: 'The selected devices will be restored to original manufacturing state, and all user provisioned settings will be erased.',
        ACTION_CONFIRM: 'Do you still want to proceed?',
        ABOUT_TO_FACTORYRESET: 'About to Factory Reset...',
        FACTORYRESETSUCC: 'Factory Reset Success!',
        FACTORYRESETFAIL: 'Factory Reset Fail!',
        FACTORYRESET_TIMED_OUT: 'Factory Reset timed out!',
        CONFIRMUPLOADLOG: 'Upload Log',
        DOUPLOADLOG: 'Request device to upload its system log to remote url for further troubleshooting. The upload url can be configured in files section of Settings of Product and System page.',
        SELECTUPLOADLOG: 'Request the selected devices to upload its system log to remote URL for further troubleshooting. The upload URL can be configured in Files section of Settings of Product and System page.',
        UPLOADLOG: 'Upload Log',
        UPLOADLOGSUCC: 'Upload Log Success!',
        UPLOADLOGSUCCMESSAGE: 'Device Operation was successfully created!',
        UPLOADLOGFAIL: 'Upload Log Fail!',
        ABOUT_TO_UPLOADLOG: 'About to Upload Log...',
        UPLOADLOG_TIMED_OUT: 'Upload Log timed out!',
        CONFIRMFIRMWARE: 'Upgrade ',
        SUCCESSFIRMWARE: 'Upgrade Success!',
        UPGRADE_BEING_DOWNLOADED: 'Downloading...',
        ASSIGN_UPGRADE_OPERATION: 'Assign upgrade opeartion',
        UPGRADE_STATUS_INSTALLING: 'Installing...',
        UPGRADE_BEING_ACTIVATED: 'Activating...',
        UPGRADE_DEVICE_BEING_RESTARTED: 'Device is rebooting...',
        WAIT_UPGRADE: 'Waiting for inform...',
        UPGRADING: 'Upgrading...',
        UPGRADE_COMPLETE_REBOOTING: 'Upgrade completed, restarting',
        UPGRADE_TIMED_OUT: 'Upgrade timed out!',
        UPGRADEFIRMWARE: 'Upgrade',
        UPGRADEFIRMWARESUCCMESSAGE: 'Device Operation for firmware upgrade was created successfully!',
        FAILFIRMWARE: 'Upgrade Fail!',
        FAILFIRMWAREPENDING: 'Upgrade Pending Operation Log.',
        REBOOTPENDING: 'Reboot in Pending Operation Log.',
        FACTORYRESETPENDING: 'Factory Reset in Pending Operation Log.',
        UPLOADLOGPENDING: 'Upload Log in Pending Operation Log.',
        DOFIRMWARE: 'During the firmware upgrade process, all services provided by the device (including remote management) will be temporarily suspended for several minutes.',
        SELECTFIRMWARE: 'During the firmware upgrade process, all services provided by the selected devices (including remote management) will be temporarily suspended for several minutes.',
        CONFIRM_GENERATE_REPORT: 'Generate Report',
        CONFIRM_GENERATE: 'Generate ',
        CONFIRM_REPORT: ' Report',
        DO_GENERATE_REPORT: 'Generate summary report with key parameters for the device. The report can be found in Device Report List widget of log page.',
        SELECT_GENERATE_REPORT: 'Generate summary report with key parameters for the selected devices. The report can be found in device report list widget of log page.',
        SUCCESSGENE: 'Generate Report Success!',
        FAILGENE: 'Generate Report Fail!',
        CONFIRM_DELETE: 'Confirm Delete',
        DO_DELETE: 'Do you want to delete selected devices?',
        CONFIRM_APPLY: 'Confirm Apply',
        DO_APPLY_CONFIGRUATION: 'Do you want to apply this configuration?',
        IMPORT_JSON_FILE_MESSAGE: 'Please import the configuration JSON file.',
        APPLY_SUCC: 'Apply Success!',
        APPLY_FAIL: 'Apply Fail!',
        PLESESELECT: 'Please select Device',
        CONFIRMReset: 'Confirm Reset',
        SUREReset: 'Are you sure you want to Reset?',
        RESETPERSONALTHEME: 'Are you sure you want to Reset personal theme for all pages?',
        RESETCURRENTPERSONALTHEME: 'Are you sure you want to reset personal theme for current page?',
        RESETALL: 'Reset all page',
        RESETCURRENTPAGE: 'Reset Current Page',
        RESETSuccess: 'Reset Widget Layout success',
        CONFIRMSave: 'Confirm Save',
        SURESave: 'Are you sure you want to save current edition?',
        SAVESuccess: 'Save Widget Layout success',
        DODELETE: 'Do you want to delete ',
        DELETESUCCESS: 'Delete Success',
        DELETEFAIL: 'Delete Fail!',
        CONFIRMBAN: 'Confirm Ban',
        BANSUCCESS: 'Ban Success!',
        BANFAIL: 'Ban Fail!',
        DOBAN: 'Do you want to ban ',
        BANSELECT: 'Do you want to ban selected Devices?',
        CONFIRMRegister: 'Confirm Register',
        ONLYVALID: 'Check to automatically retain legitimate devices',
        SUCCESSRegister: 'Register Device Success!',
        FAILRegister: 'Register Device Fail!',
        DORegister: 'Do you want to register ',
        FAIL: 'Fail!',
        CONFIRMOPER: 'Confirm Delete Operation Log',
        OPERSELECT: 'Do you want to delete selected Operation Log?',
        CONNECTIVITYTESTSELECT: 'Do you want to delete selected Connectivity Test History?',
        CONNECTIVITYTEAllCONFIRM: 'Confirm Connectivity Test History Cleanup?',
        CONNECTIVITYTEAll: 'Do you want to clean Connectivity Test History?',
        SPEEDTESTSTSELECT: 'Do you want to delete selected Speed Test History?',
        SPEEDTESTAllCONFIRM: 'Confirm Speed Test History Cleanup?',
        SPEEDTESTAll: 'Do you want to clean Speed Test History?',
        PLESEOPER: 'Please select Completed Operation Log',
        DOTAG: 'Do you want to delete tag ',
        TAGSUCC: 'Delete Tags Success!',
        TAGFAIL: 'Delete Tags Fail!',
        ADDTAGSUCC: 'Add Tags Success!',
        ADDTAGFAIL: 'Add Tags Fail!',
        UPDAGEDEVICE: 'Update device!',
        CONFIRMCANCEL: 'Confirm Operation Cancelling',
        DOCANCEL: 'Do you want to cancel ',
        CANCELSUCC: 'Cancel success!',
        CANCELFAIL: 'Cancel Fail!',
        SELECTCANCEL: 'Do you want to cancel selected Pending Operation Logs?',
        PLEASECANCEL: 'Please select Pending Operation Log',
        CONFIRMREPORT: 'Confirm Report Delete',
        SELECTREPORT: 'Do you want to delete selected Report Log?',
        PLEASEREPORT: 'Please select Report Log',
        CONFIRMSESSION: 'Confirm Delete Session Log',
        SELECTSESSION: 'Do you want to delete selected Session Log?',
        PLEASESESSION: 'Please select Session Log',
        CONFIRMACK: 'Confirm Ack Alarm?',
        SELECTACK: 'Do you want to ack all the selected alarms?',
        DOACK: 'Do you want to ack the alarm ',
        CONFIRMSAVELOCA: 'Confirm Save Location',
        DOSAVELOCA: 'Do you want to save Location?',
        CONFIRMRESETLOCA: 'Confirm Reset Location',
        DORESETLOCA: 'Do you want to reset Location?',
        SAVESUCC: 'Save Success!',
        SAVEFAIL: 'Save Fail!',
        RESETSUCC: 'Reset Success!',
        RESETFAIL: 'Reset Fail!',
        ACTIVESUCC: 'Activate Device Success!',
        ACTIVEFAIL: 'Activate Device Fail!',
        BANDEVSUCC: 'Ban Device Success!',
        BANDEVFAIL: 'Ban Device Fail!',
        WAITOTHER: 'Waiting for the other party to join the session ...',
        ACSTROUBL: 'ACS joins the troubleshooting session',
        WAITCPE: 'Waiting for CPE entering the troubleshooting session',
        ACSCONNECT: 'ACS is connecting to the troubleshooting session',
        ACSSETTING: 'Waiting for ACS setting up troubleshooting session',
        CPEJOIN: 'CPE joins the troubleshooting session',
        SESSESTABLISH: 'Session is established',
        CONFIRMTROUB: 'Confirm disconnect troubleshooting',
        DODISCONNECT: 'Do you want to disconnect the session ',
        CONFIRMUSER: 'Confirm User Removal?',
        DOUSER: 'Do you want to delete the user',
        SUCCESS: 'Success!',
        ERROR: 'Error!',
        CONFLOGENTRY: 'Confirm log entry removal',
        UPLOAD_CONNECTIVITYTEST: 'Upload Connectivity Testing, please wait ...',
        DOWNLOAD_CONNECTIVITYTEST: ' Download Connectivity Testing, please wait ...',
        COMPLETE_CONNECTIVITYTEST: ' Connectivity Test completed !',
        CONNECTIVITYTEST_URL: 'Test File URL',
        UPLOAD_SPEEDTEST: 'Upload Speed Testing, please wait ...',
        DOWNLOAD_SPEEDTEST: ' Download Speed Testing, please wait ...',
        COMPLETE_SPEEDTEST: ' Speed Test completed !',
        SPEEDTEST_URL: 'Test File URL',
        REQUESTUPDATEFAIL: 'Request Update Fail',
        CONFVERSION_NOTCHANGED: 'Configuration version has not changed.',
      },
      OPERATION_ACTION: {
        SELECT_OPERATION: 'Select Profiles',
        SELECT_WORKFLOW: 'Select Workflow',
        SELECT_Action_OR_WORKFLOW: "Select Profiles or Workflow",
        ADD_OPERATION: 'Add Into Profiles',
        OPERATION_COUNT: 'Count',
        EDIT_OPERATION: 'Edit Operation',
        EDIT_CONFIGURATION: 'Edit Configuration',
        OPERATION_DETAILS: 'Profiles Details',
        SETUP_OPERATION: 'Setup Profiles Details',
        ADD_OPERATION_TO: 'Add Operation To Profiles',
        OPERATION_ACTIONS: 'Operation Profiles',
        ENTER_OPERATION_ACTIONS: 'Enter Your Operation Profiles.',
        OPERATION_TYPE: 'Operation Type',
        SELECT_OPERATIONTYPE: 'Select Operation Type',
        EDIT_ACTIONS: 'Edit Operation Profiles',
        MANUAL_OPERATION: 'Manual Profile',
        MANUAL_WORKFLOW: 'Manual Workflow',
        COMPLETION_RATE: 'Completion Rate'
      },
      AMP: 'AMP',
      GO: 'Go',
      COLLAPSE: 'Collapse',
      COLLAPSEALL: 'Collapse All',
      EXPAND: 'Expand',
      EXPANDALL: 'Expand All',
      DISCOVER: 'Discover',
      DISCOVERDATAMODEL: 'Discover:Ask Device to update selected parameters',
      PATH: 'Path',
      REQUIRED: 'Required',
      OPTIONALPARAM: 'There are no optional parameters',
      SENDRESP: 'Send Resp',
      ACCESSLIST: 'Access List',
      SELECTACCESSLIST: 'Select Access List',
      REPORTEDVALUE: 'reportedValue',
      INPUTLIST: 'Input List',
      ADDEDITTAG: 'Add/Edit Tags',
      ADDTAGS: 'Add Tags',
      ADDTAG: 'Add Tag',
      INPUTTAGNAME: 'Input Tag Name',
      TAGSLIST: 'Tags List',
      EXISTTAG: 'Already have the same tag!',
      ADDEDITLABEL: 'Add/Edit Label',
      INPUTLABELNAME: 'Input Label Name',
      LOCATION: 'Location',
      HEALTHYSTATUS: 'Healthy Status',
      INFORMHISTORY: 'Inform History',
      SESSIONLOGINTERVAL: 'Session Log Interval',
      SESSIONLOGINTERVALDESCRIPTION: "Historical chart of the device's periodic report interval to the AMP over the past 24 hours.",
      TIMEINTERVAL_BETWEEN_CONSECUTIVE_INFORMS: "Time Intervals between Consecutive Informs",
      LOCATIONDESCRIPTION: "Display Device's location on the Map.",
      ONLINERATE: 'Online Rate',
      ONLINERATEDESCRIPTION: 'The pentagon radar chart displays the health status of the Device.',
      RESET: 'Reset',
      EDITCOMPONENT: 'Add Widget',
      BULKLOGLIST: 'Bulk Log List',
      EDITFILE: 'Edit File',
      FILTERNODE: 'Filter Nodes',
      SELECTACTION: 'Action to selected Devices',
      PRODUCTINFO: 'Product Info',
      CONNHISTORY: 'Connection History',
      LAST24: '(Last 24hrs)',
      WIFIUSAGE: 'WiFi Channel Usage',
      WIFIANALYZER: 'WiFi Analyzer',
      WIFIANALYZERDESCRIPTION: 'WiFi RSSI chart of neighbor APs from avaliable channles/bands on this device.',
      CELLSTATUS: 'Small Cell Status',
      CELLSTATUSDESCRIPTION: "Small Cell's status such as Radio Service status, PC, gNB ID, MCC, MNC and TAC.",
      CELLHANDOVERSTATUS: 'Small Cell Handover Status',
      CELLHANDOVERSTATUSDESCRIPTION: 'Small Cell Handover Status.',
      COVERMAP: 'Coverage Map',
      COVERMAPDESCRIPTION: 'Displays the locations and radio coverage of the Device in the Group.',
      TOTALALARMSDESCRIPTION: "Total number of Alarms reported by the Device.",
      ALARMSDESCRIPTION: "Total number of Alarms with different Severity such as Critical, Major, Minor and Warning.",
      CREATEMAP: 'Create',
      SAVEMAP: 'Save',
      SELECTIMAGE: 'Select Image',
      IMAGEPRE: 'Image Preview',
      MAPX: 'Map dimensions(X)',
      MAPY: 'Map dimensions(Y)',
      EDITMAP: 'Edit',
      M: 'm',
      ENGMODE: 'Engineer Mode',
      CLIMODE: 'Cli Mode',
      CONFIG: 'Configuration',
      PROVISIONING: 'Provisioning',
      DEVICEALARM24HRS: 'Alarm within 24 hrs',
      PMALARM24HRS: 'Anomalies detected in PM data within the last 24 hours.',
      PMSTATUS_GOOD: 'No abnormal PM data within the last 24 hours.',
      PMSTATUS_BAD: 'PM data has abnormalities within the last 24 hours.',
      PMSTATUS_NORMAL: 'No PM data available within the last 24 hours.',
      PMSTATUS_NOPERMISSION: 'Access to PM data is restricted.',
      ADDTOGROUP: 'Join A Group',
      ADDDEVTOGROUP: 'Add Device To Group',
      SELECTDEVICE: 'Selected Device',
      RESTARTNOW: 'Restart now for the changes to take effect.',
      TAGSDESCRIPTION: 'Custom tags on Device for easier Device management.',
      BATCHSAVE: 'Batch Save',
      CPUUSAGEDESCRIPTION: "Device's CPU Usage percentage.",
      MEMORYUSAGEDESCRIPTION: "Device's Memory Usage percentage.",
      CPUUSAGECHART_DESCRIPTION: "Historical chart of Device's CPU Usage percentage.",
      MEMORYUSAGECHART_DESCRIPTION: "Historical chart of Device's Memory Usage.",
      KPIDESCRIPTION: "Historical chart of Small Cell's KPI such as RRC, UE Context and Throughput.",
      PMPARAMDESCRIPTION: "Small Cell's KPI such as RRC, UE Context and Throughput.",
      ALARM_CURRENTNUMBERS: 'Total number of Alarms reported by Device.',
      PRODUCTMODEL: 'Product Model',
      PRODUCTMODELDESCRIPTION: 'Indicate which Product the current Device belongs to.',
      DEVICEOFFLINETIPS: 'Device is Offline now, AMP retains the information last reported by the device',
      DEVICENOTREGISTERTIPS: 'The device is a new added device and never online/provisioned yet',
      ONLINESTATUS: 'Online Status',
      ONLINESTATUSDESCRIPTION: "Historical chart of Device's Online Status.",
      REGISTERSINGLEDEVICE: "Register Single Device",
      REGISTERBATCHDEVICE: "Register Batch Devices",
      DOWNLOADSESSIONGCSV: 'Download CSV (All Fields)',
      DOWNLOADSESSIONGJSON: 'Download JSON (Full Content)',
      DOWNLOADLATESTSESSIONGJSON: 'Download Latest Session Logs',
      RSSIDISTRIBUTION: 'WiFi RSSI Distribution',
      RSSIDISTRIBUTION_DESCRIPTION: 'RSSI Distribution from associated WiFi clients\' ranges on this device.',
      SSIDSWITHCLIENTS: 'WiFi SSID Distribution',
      SSIDSWITHCLIENTS_DESCRIPTION: 'SSID Distribution from associated WiFi clients\' network on this device.',
      TOTALCLIENTHISTORY: 'WiFi Associated Clients',
      TOTALCLIENTHISTORY_DESCRIPTION: 'Historical Chart of an overview about WiFi associated client number of this device.',
      CLIENTSPERSSID: 'WiFi Clients per SSID',
      CLIENTSPERSSID_DESCRIPTION: 'Historical Chart of an overview about WiFi associated client number categorized by SSID of this device.',
      CLIENTSPERRADIO: 'WiFi Clients per Radio',
      CLIENTSPERRADIO_DESCRIPTION: 'Historical Chart of an overview about WiFi associated client number categorized by radio/band of this device.',
      TRAFFICPERSSID: 'WiFi Traffic per SSID',
      TRAFFICPERSSID_DESCRIPTION: 'Historical Chart of an overview about WiFi traffic categorized by SSID of this device.',
      TRAFFICPERRADIO: 'WiFi Traffic per Radio',
      TRAFFICPERRADIO_DESCRIPTION: 'Historical Chart of an overview about traffic categorized by radio/band of this device.',
      VIEWCHART: 'View Chart',
      LOGDETAIL: 'Log Detail',
      STATUS: 'Status',
      UTILIZATION_GREEN: 'Green',
      UTILIZATION_YELLOW: 'Yellow',
      UTILIZATION_RED: 'Red',
      UTILIZATION_GREEN_DESCRIPTION: 'Utilization <= 30%',
      UTILIZATION_YELLOW_DESCRIPTION: '30% < Utilization <=60%',
      UTILIZATION_RED_DESCRIPTION: 'Utilization > 60%',
      BEACONPERIOD: 'Beacon Period',
      MCS: 'MCS',
      INACTIVE: 'Inactive',
      DISABLE: 'Disable',
      CHANNEl: 'Channel',
      WHITELISTED: 'Whitelisted',
      REFURBISHMENT: 'Refurbishment',
      DOFURBISHMENT: ' will be refurbished and all device data will be removed. ',
      CONFURBISHMENT: 'Refurbishment Process',
      PMCHART: 'PM Chart',
      PMPARAMETER: 'PM Parameter',
      COMPLETED_DESCRIPTION: "Device done the Operations assigned from AMP.",
      FAIL_DESCRIPTION: "All Operations executed by Device are Fail.",
      PARTIAL_DESCRIPTION: "Partial Operations executed by Device are Fail.",
      CANCEL_DESCRIPTION: "Operations assigned by AMP are Canceled before executing.",
      PENDING_DESCRIPTION: "Waiting Device to receive the Operations assigned from AMP.",
      INPROCESS_DESCRIPTION: "Waiting Device report the result of executing Operations to AMP.",
      NETWORKTOPOLOGY: "Network Topology",
      NETWORKTOPOLOGY_DESCRIPTION: "Display tree-view for all associated node information.",
      core_5g: {
        ue_info: 'UE Info',
        ue_info_DESCRIPTION: 'The UE Info provides detailed information about the User Equipment (UE) connected to the 5G Core network.',
      },
      healthyStatus: {
        alarm_description: 'For each critical alarm that occurs within 24 hours, a deduction of 10 points will be applied, and for each major alarm, a deduction of 5 points will be applied, and so on.',
        session_log_rate_description: "The ratio of the device's normal operating cycle to the reports submitted to AMP over the past 24 hours.",
        online_rate_description: "The equipment's online rate over the past 24 hours indicates the percentage of time the equipment has been operational and connected.",
        service_quality_description: 'The WiFi equipment applies a deduction of 5 points for each poor client currently connected, while the Small Cell measures the 24-hour average RRC Rate to assess service quality.',
        service_active_description: 'For each device Reboot (Bootstrap) within the past 24 hours, a deduction of 20 points is applied.'
      },
      cableMedemInfo: {
        cable_medem_info: 'DOCSIS Status',
        cable_medem_info_description: 'Display Cable Modem information from cable access network'
      },
      ONTMediaInfo: {
        ont_media_info: 'ONT Media Status',
        ont_media_info_description: 'Display Octical Media information from ONT Device'
      },
      batteryStatus: {
        battery_information: 'Battery Status',
        battery_description: 'Battery information such as Status, Temperature and Level'
      },
      WIFIAPINFO: 'WiFi AP Info',
      WIFIAPINFO_DESCRIPTION: '',
      WIFIAPINFO_APCONFIGVERSION: 'AP Config Version',
      APCONFIG: 'AP Config',
      WIFIAPINFO_APNAME: 'AP Name',
      WIFIAPINFO_APROLE: 'AP Role',
      WIFIAPINFO_APNCONFIGVERSION: 'APN Config Version',
      APNCONFIG: 'APN Config',
      CONFIGVER: 'Config Ver.',
      EDITAPNAME: 'Edit AP Name',
      WIFIAPINFODESCRIPTION: 'WiFi AP information,including AP Role,AP Name,AP Config Version and APN Config Version.',
      FIVEGLICENSE: 'Cell Software License',
      FIVEGLICENSEDESCRIPTION: "Small Cell's License information such as expiration and supported items.",
      OPERATION_LOCATION: 'Profile Location',
      OPERATION_SETUPLOCATION: 'Setup Profile Location',
      SEARCH_KEYWORD: "Search Keyword",
      SERVICE_STATUS: 'Service',
      NCI: 'NCI',
      GNB_ID: 'GNB ID',
      GNB_ID_LENGTH: 'GNB ID Length',
      MCC: 'MCC',
      MNC: 'MNC',
      AMF_IP_CONTROL_PLANE: 'AMF IP-Control-Plane',
      CELL_ID: 'Cell ID',
      TAC: 'TAC',
      NSSAI: 'NSSAI',
      PCI: 'PCI',
      PARAMETER: 'Parameter',
      REPORTINTERVAL: 'Reporting Interval',
      TIMEREFERENCE: 'Time Reference',
      NUMBEROFRETAINEDFAILEDREPROTS: 'Number Of Retained Failed Reports',
      ENCODINGTYPE: "Encoding Type",
      REQUESTURIPARAMETER: "Request URI Parameter",
      NOTIFTYPE: 'NotifType',
      REFERENCELIST: 'Reference List',
      RECIPIENT: 'Recipient',
      LOADMAPFAIL: 'Map tile loading failed. Please try again later.',
      MISMATCHPROFILE: 'The parameters "conGlobalProfile" and "conDeviceSpecificProfile" do not match the configuration.',
      ENERGYSAVING: 'Energy Management',
      STARTTIME_MUST_BE_EARLIER: "Start time must be earlier than end time.",
      STARTDATE_MUST_BE_EARLIER: "Start date cannot be later than end date.",
    },
    GROUPS: {
      WIFICLIENT: 'WiFi Clients',
      TITLE: 'Groups',
      TOTAL: 'Total Groups',
      LIST: 'Group List',
      LISTDESCRIPTION: 'A distinct unit containing Devices within AMP, handle specific functions such as batch Operation, monitor Network status.',
      ADDGROUP: 'Add Group',
      EDITGROUP: 'Edit Group',
      NAME: 'Group Name',
      ACCEPT: 'Accept',
      MEMBERSTITLE: 'Members',
      MEMBERS: 'Devices',
      PENDINGLOGS: 'Pending Logs List',
      PENDINGLOGSDESCRIPTION: 'List Pending operation tasks awaiting devices reception.',
      OPERATIONLOGS: 'Operation Logs List',
      OPERATIONLOGSDESCRIPTION: 'List operation tasks assigned by AMP that the devices in the group has executed, along with the reported results.',
      IMPORTTYPE: 'Select Import Type',
      SELECTGROUP: 'Select Group Name',
      CREATE: 'Create',
      ADDDEVICE: 'Add Device',
      INPUTTYPE: 'Select Input Type',
      INPUTVALUE: 'Input Value',
      GROUPUPDATE: 'Group Update',
      FILTERING: 'Clear invalid devices',
      ADDTOGROUP: 'Add To Device Group',
      SELECTGROUPTYPE: 'Select Group Type',
      SEARCH: 'Search',
      PENDINGOPER: 'Pending Operation',
      PARAMNOTIFICATION: 'Parameter Notification',
      SELECTPARAMNOTIFICATION: 'Select Parameter Notification',
      NONEXISTINGMEN: 'Automatically filter out non-existing Device',
      BYINPUT: 'By Input',
      BYIMPORTFILE: 'By Import File',
      ADDMEMBER: 'Add Device',
      FILTERMEMBERIN: 'Filter Condition In Product',
      ENTERKEYWORDS: 'Enter Keywords',
      BYPRODUCT: 'By Product',
      CONFIRM: 'Confirm',
      DEVICECOUNT: 'Devices',
      ONLINERATE: 'Online Rate',
      SERVICEAVAILABILITY: 'Service Availability',
      CREATEBY: 'Created By',
      CREATETIME: 'Created Time',
      ALARMCOUNT: 'Alarms',
      ALARMCOUNTDESCRIPTION: 'Total number of Alarms with different Severity such as Critical, Major, Minor and Warning.',
      DRAGANDDROP: 'Drag and drop selected operations to arrange execution operation sequence',
      GROUPACT: 'Group Action',
      ADDMEMBY: 'Add Device By Import',
      ADDMEMINPUT: 'Add Device By Input',
      NETWORKTOPOLOGY: 'Network Topology',
      CANTDOOPERATION: 'No device exists in the group',
      ASSIGNOPERATION: 'Run Operation',
      REASSIGNOPERATION: 'Rerun Operation',
      DIFFRENTPROTOCOLNOTALLOWED: 'Adding two or more different protocol devices to one group is not allowed',
      CONFIRMADDTOGROUPMEMBER: 'Confirm adding to group',
      ONLYVALID: 'Only legitimate devices will be added',
      DEVICEERRCODE1: "The Product doesn't contain this device",
      DEVICEERRCODE2: 'The device already exists in the group',
      DEVICEERRCODE3: 'The device is illegal',
      UE: 'UE',
      UEDESCRIPTION: 'Total numbers of UEs attached to the Small Cell included in this group.',
      ONLINEDEVICESTITLE: 'Online Devices',
      ONLINEDEVICESDESCRIPTION: 'Total number of Online Devices included in the Group.',
      WIFICLIENTDESCRIPTION: 'Total number of WiFi Clients included in the Group.',
      ALARMSDESCRIPTION: 'Total number of Alarms reported by the Device included in the Group.',
      KPIDESCRIPTION: 'Historical chart of Small Cell’s KPI such as RRC, UE Context and Throughput included in this Group.',
      DEVICESLISTDESCRIPTION: 'List all Devices included in this Group.',
      GROUPSDELETED: 'The selected groups deleted successfully.',
      RFMAP_MAX_WARNING: 'The Coverage Map only supports up to 50 devices.',
      SSIDLIST: 'WiFi SSID List',
      SSIDLIST_DESCRIPTION: 'List all SSIDs included in this group.',
      SSID: 'SSID',
      SECURITY: 'Security',
      CAPTIVEPORTAL: 'Captive Portal',
      MACACL: 'MAC ACL',
      ATF: 'ATF Enable',
      ATFPERCENTAGE: 'ATF',
      BEAMFORMING: 'Beam Forming',
      MAXCLIENTS: 'Max Clients',
      WMM: 'WMM',
      BAND: 'Band',
      BANDWIDTHCONTROL: 'Bandwidth Control',
      DOWNLOAD: 'Sent',
      UPLOAD: 'Received',
      FOREIGNAPSLIST: 'Foreign APs List',
      FOREIGNAPSLIST_DESCRIPTION: 'WiFi AP list which is not included this group.',
      VLAN: 'VLAN',
      BSSID: 'BSSID',
      RSSI: 'RSSI',
      NEARBYAPS: 'Nearby APs',
      TIME: 'Time',
      SSIDSWITHCLIENTS: 'Top 6 WiFi SSIDs with the most Clients',
      SSIDSWITHCLIENTS_DESCRIPTION: 'The WiFi SSID in this group with the top 6 most client connections.',
      APSWITHCLIENTS: 'Top 6 WiFi APs with the most Clients',
      APSWITHCLIENTS_DESCRIPTION: 'The top 6 most connected WiFi APs by clients.',
      APSWITHTRAFFIC: 'Top 6 WiFi APs with the highest Traffic',
      APSWITHTRAFFIC_DESCRIPTION: 'The total numerical values of WiFi AP data sent and received, taking the top six APs with the highest values.',
      CLIENTSPERSSID: 'WiFi Clients per SSID',
      CLIENTSPERSSID_DESCRIPTION: 'Historical Chart of an overview about client connection numbers categorized by WiFi SSID for devices included the group.',
      CLIENTSPERRADIO: 'WiFi Clients per Radio',
      CLIENTSPERRADIO_DESCRIPTION: 'Historical Chart of an overview about client connection numbers categorized by radio/band for devices included the group.',
      TRAFFICPERSSID: 'WiFi Traffic per SSID',
      TRAFFICPERSSID_DESCRIPTION: 'Historical Chart of an overview about traffic categorized by SSID for devices included the group.',
      TRAFFICPERRADIO: 'WiFi Traffic per Radio',
      TRAFFICPERRADIO_DESCRIPTION: 'Historical Chart of an overview about traffic categorized by radio/band for devices included the group.',
      BYTESRECEIVED: 'Received',
      BYTESSENT: 'Sent',
      ENABLE: 'Enable',
      DISABLE: 'Disable',
      CHANNEL: 'Channel',
      TAGSGROUPDESCRIPTION: 'Custom tags on Group for easier Device management.',
      COUNTRY: 'Country',
      SELECTCOUNTRY: 'Select Country',
      STREETADDRESS: 'Street Address',
      CITY: 'City',
      STATE: 'State',
      ZIPCODE: 'Zip Code',
      TAGS: 'Tags',
      INPUTTAGNAME: 'Input Tag Name',
      ROAD: 'Road/Street',
      HOUSE: 'Building Number',
      GROUPDETAILS: 'Group Details',
      GROUPLOCATION: 'Group Location',
      GROUPLOCATIONDESCRIPTION: 'Display Group’s Location on the map.',
      EDITLOCATION: 'Edit Location',
      INPUTLAT: 'Input Lat',
      INPUTLNG: 'Input Lng',
      RESET: 'Reset',
      LATLNG: 'Latitude and Longitude',
      LAT: 'Latitude',
      LNG: 'Longitude',
      LOCATION: 'Location',
      VIEWLOCATION: ' View Group Location',
      VIEWCOVERAGEMAP: 'View Coverage Map',
      VIEWLOCATIONDESCRIB: 'When Group Location widget is open, clicking the icon in the column allows user to view location and information of the group.',
      VIEWCOVERAGEMAPDESCRIB: 'When Coverage Map widget is open, clicking the icon in the column allows user to view distribution and information of the devices included in the group.',
      WIDGETNAME: 'Widget name,class or subclass',
      COVERMAP: 'Coverage Map',
      COVERMAPDESCRIPTION: 'Displays the locations and radio coverage of all devices belonging to the Device’s group.',
      TOTALALARMSDESCRIPTION: 'Total number of Alarms reported by the Devices included in the Group.',
      ALARMMGMT: 'Alarm Management',
      ALARMMGMTDESCRIPTION: 'List all Alarm reported by the Devices included in the Group. including Cleared, Un-cleared with Severity, Event time and Probable Cause.',
      NETWORK: 'Network',
      UPLOADIMAGE: 'Please Upload Image and Edit Device Position'
    },
    PRODUCTS: {
      LIST: 'Product List',
      LISTDESCRIPTION: 'A distinct unit containing Devices within AMP, handle specific functions such as Access List control, default provisioning parameter and User account’s management scope. It is required for Device’s registration and onboarding.',
      NETWORKRADIOACCESSLIST: 'Radio Access Network List',
      NETWORKRADIOACCESSLISTDESCRIPTION: 'A distinct unit for managing specific Devices such as Small Cells. Group with the same name will also be created for Network management purposes.',
      NETWORKWIFIAPLIST: 'WiFi AP Network List',
      NETWORKWIFIAPLISTDESCRIPTION: 'A distinct unit for managing specific Devices such as WiFi APs. Group with the same name will also be created for Network management purposes.',
      NETWORKWIFIMESHLIST: 'WiFi Mesh Network List',
      NETWORKWIFIMESHLISTDESCRIPTION: 'A distinct unit for managing specific Devices such as WiFi Mesh APs. Group with the same name will also be created for Network management purposes.',
      ACTION: 'Product Action',
      ADD: 'Add Product',
      ADDRADIOACCESSNETWORK: 'Add Radio Access Network',
      ADDWIFIAPNETWORK: 'Add WiFi AP Network',
      ADDWIFIMESHNETWORK: 'Add WiFi Mesh Network',
      PERMISSION: 'Permission',
      DEVICEPERMISSION: 'Device Permission',
      ADDPERMISSION: 'Allow list',
      PERMITTEDTYPE: 'Access Control',
      PROVISIONINGTYPE: 'Provisioning Type',
      SELECTTYPE: 'Select Access Control',
      ALLOWALL: 'Allow all',
      WHITELIST: 'Allow List',
      CREATEWHITELIST: 'Create Allow list',
      LABEL: 'Label Name',
      LABELREQUIRED: 'This Label Name is required!',
      NAMEREQUIRED: 'This Name is required!',
      PRODUCTTYPE: 'Product Type',
      SELECTPRODUCTTYPE: 'Select Product Type',
      PRODUCTPICTURE: 'Product Picture',
      PRODUCTNAME: 'Product Name',
      ISPRODUCTNAME: 'This Product Name is required!',
      ISPRODUCTCLASS: 'This Product Class is required!',
      TARGETPRODUCT: 'Target Product Name',
      OBJECTNAME: 'Object Name',
      ENTEROBJECTNAME: 'Enter Object Name',
      PRODUCTDETAILS: 'Product Details',
      DETAILS: 'Details',
      CHOOSEFILE: 'Choose file',
      PERMITEDDEVICE: 'Allowed Devices',
      DEVICELIMITS: 'Device Limits',
      UPDATEDTIME: 'Updated Time',
      EDITPRODUCT: 'Edit Product',
      EDITRAN: 'Edit Radio Access Network',
      EDITAPN: 'Edit WiFi AP Network',
      EDITMESH: 'Edit WiFi Mesh Network',
      PRODUCTMODEL: 'Product Model',
      PRODUCTMODELDESCRIPTION: 'Product Model, description and image.',
      BYDEFAULT: 'Select Picture',
      BYUPLOAD: 'Upload Picture',
      DATACOLLECT: 'Data Collection',
      DATACOLLECTDESCRIPTION: "Toggle on to start collecting data, associated device's KPI Chart Widget can be created in Device Info page.",
      DATAEXPORTDESCRIPTION: "Toggle on to export performance metrics to 3rd party telemetry system.",
      PRODUCTNOTEXIST: 'The current Product does not exist',
      DEVICETEXIST: 'Device already exists',
      DEVICEERRCODE1: 'Item contain illegal string',
      DEVICEERRCODE2: 'DelItem does not exist',
      DEVICEERRCODE3: "The device already exists in product:",
      LABELORPATHEXISTS: "Label Name or Parameter Path already exists",
      PARSINGFAILED: "Parsing the file faied.",
      FILETYPEERROR: "Only CSV files can be uploaded",
      EXPORTINITDEFAULTTOJSON: "Download JSON (Provisioning default value)",
      EXPORTINITDEFAULTTOCSV: "Download CSV (Batch registration example)",
      EDITPROVISIONINGDEFAULTVALUE: "Edit Provisioning Default Value",
      TAGS: 'Tags',
      LOCATION: 'Location',
      REGISTERDEVICE: 'Register Device',
      NAME: 'Name',
      NAMETOOLTIP: 'Input user defined Product Name',
      NETWORKNAME: 'Input user defined Network Name',
      PICTURE: 'Picture',
      UEFORPRODUCT: 'UE count for each Product',
      OUI: 'Input the correct OUI according to the device',
      DESCRIPTION: 'Description',
      NETWORKDESCRIPTION: 'Input the characteristics or proprietary attributes of the devices belong to the Network.',
      PRODUCTDESCRIPTION: 'Input the characteristics or proprietary attributes of the devices belong to the Product.',
      PRODUCTCLASS: 'Input the correct Product Class according to the device',
      PROCPELIMIT: 'Input the capacity of the Product',
      NETCPELIMIT: 'Input the capacity of the Network',
      OUIDESCRIPTION: 'OUI must be correct for AMP to verify the accessing Device',
      ALLOWALLDESCRIPTION: 'Device’s OUI , Product Class should be verified.',
      PROALLOWLISTDESCRIPTION: 'Device’s OUI , Product Class and Serial Number should be verified, registering Device by Serial Number is required after creating Product.',
      NETALLOWLISTDESCRIPTION: 'Device’s OUI , Product Class and Serial Number should be verified, registering Device by Serial Number is required after creating Network.',
      PRODUCTCLASSDESCRIPTION: 'Product Class must be correct for AMP to verify the accessing Device. Ex:Femtocell_5G_SA,EAP,etc.',
      PRODEVICELIMITDESCRIPTION: 'Number of capacity of the Devices in the Product',
      NETDEVICELIMITDESCRIPTION: 'Number of capacity of the Devices in the Network',
      PROVISIONINGTYPEDESCRIPTION: 'Provisioning Type must be correct for AMP to verify the accessing Device'
    },
    ALARMS: {
      TOTAL: 'Total Alarms.',
      ALARMCOUNT: 'Critical/Major/Minor/Warning Count.',
      COUNT: 'Alarm Count',
      CRITICAL: 'Critical',
      MAJOR: 'Major',
      WARNING: 'Warning',
      INDETERMINATE: 'Indeterminate',
      MINOR: 'Minor',
      CURRENTNUMBERS: 'Current Alarm Numbers.',
      SYSTEM: 'System Events',
      LIST: 'System Event List',
      DEVICE: 'Device Alarms',
      DEVICELIST: 'Device Alarm List',
      ACKALARM: 'Ack Alarm',
      ERRORLIST: 'Device Error List',
      DEVICEEVENTTRACKING: 'Device Event Tracking',
      DEVICEEVENTTRACKINGDESCRIPTION: 'List significant events tracked by AMP related to devices such as Reboot, Offline and Reset.',
      NOTIFICATION: 'Notification',
      NOTIFICATIONLIST: 'Notification List',
      NOTIFICATIONLISTDESCRIPTION: 'List rules created by users to receive notifications via SMTP or SNMP Trap under specific conditions based on requirements.',
      FORK: 'Fork',
      FORKNOTIFICATION: 'Fork Notification',
      CLONE: 'Clone',
      CLONENOTIFICATION: 'Clone Notification',
      EDITNOTIFICATION: 'Edit Notification',
      SETUPDETAILS: 'Details(Setup Details)',
      ENTERDETAILS: 'Enter Your Details',
      GENERIC_TRAP_TYPE: "Generic Trap Type",
      SPECIFIC_TRAP_OID: "Specific Trap OID",
      VARIABLE_BINDINGS: 'Variable Bindings (optional)',
      ALIASVALUE: 'Alias Value',
      OlDNAME: 'OlD/Name',
      VARIABLE_BINDINGS_DESCRIPTION: 'The values of varbinds  is associated with the alias list configured in the stage.',
      TARGET: 'Target',
      EDITTARGET: 'Edit Target',
      SELECTTARGET: 'Select Target',
      ENTERTARGET: 'Enter Your Target.',
      TARGETREQUIRED: 'This Target File Name is required!',
      TARGETTYPE: 'Target Type',
      TARGETDEVICESN: 'Target Device SN',
      ISTARGETDEVICESN: 'This Target Device SN is required!',
      SCHEDULE: 'Schedule',
      EDITSCHEDULE: 'Edit Schedule',
      ENTERSCHEDULE: 'Enter Your Schedule.',
      SCHEDULEDOWNLOAD: 'Schedule Download Details',
      STARTDATE: 'Start Date',
      ENDDATE: 'End Date',
      STARTTIME: 'Start Time',
      ENDTIME: 'End Time',
      ENTEROPERATION: 'Enter Your Operation',
      TRIGGER: 'Trigger Type',
      SELECTTRIGGER: 'Select Trigger Type',
      INFORMPARAM: 'Inform Parameters',
      CONDITION: 'Condition',
      SELECTCONDITION: 'Select Condition',
      BUILDCONDITION: 'Build Condition',
      PARAMCONDITION: 'Parameter Condition',
      SELECTPARAMCONDITION: 'Select Parameter Condition',
      ATTACHED: 'Attached Messages',
      ADDITIONALPARAMINFO: 'Additional Parameter Info',
      ADDITIONALPARAMINFO_DESCRIPTION: 'More detailed information containing specific Parameter value in the Notified message.',
      NODE: 'Node',
      ENTERNODE: 'Enter Node',
      SELECTNODE: 'Select Node',
      VIEWNODE: 'View NetConf Node',
      REFERNODE: 'Refer Node',
      REFERNODEREQUIRED: 'This Refer Node is required!',
      PARENTNODE: 'Parent Node',
      SELECTPARENTNODE: 'Select Parent Node',
      CHILDNODE: 'Child Node',
      ADDCHILDNODE: 'Add Child Node',
      SELECTCHILDNODE: 'Select Child Node',
      CHILDCONTENT: 'Child Content',
      CONTENT: 'Content',
      ENTERCONTENT: 'Enter Content',
      CONFIG: 'Config',
      NAMESPACE: 'Namespace',
      ENTERNAMESPACE: 'Enter Namespace',
      ALIAS: 'Alias',
      ENTERALIAS: 'Enter Alias',
      ADDATTACHED: 'Add Attached Message',
      ADDDEVICEFALUT: 'Add Device Fault Parameters',
      SELECTDEVICEFAULTNAME: 'Select Device Fault Name',
      BUILD: 'Build Operation (Optional)',
      BUILDREQUIRED: 'Operation',
      PROGRESS: 'Progress',
      ACTIONS: 'Actions',
      REPEAT: 'Repeat',
      REPEATTYPE: 'Repeat Type',
      UPLOADNOTIFI: 'Upload Alarms Notification',
      DROPFILE: 'Drag and drop to upload or browse files',
      UPLOADFORMATSARESUPPORTED: 'Only supports uploading in .tar .tar.gz .tgz .zip .gzip format.',
      UPLOADALL: 'Upload all',
      UPLOAURL: 'Device Log Upload URL',
      BANDWIDTH: 'Upload Bandwidth',
      QUEUEPROGRESS: 'Queue progress',
      PARAMLIST: 'Parameter List',
      SELECT: 'Select',
      ENTERSELECT: 'Enter Select (XPath expression)',
      SOURCE: 'Source',
      SELECTSOURCE: 'Select Source',
      FILTERSTATE: 'Filter State',
      SELECTFILTERSTATE: 'Select Filter State',
      FILTERTYPE: 'Filter Type',
      SELECTFILTERTYPE: 'Select Filter Type',
      REMOVEALL: 'Remove all',
      REMOVE: 'Remove',
      UPDATEUSER: 'Update User',
      UPDATEDBY: 'Updated By',
      LASTACTIVE: 'Last Activity',
      UPDATELOG: 'Update Log',
      NOTIF: 'Notification',
      ONLYONCE: 'Only Once',
      ALLSTATE: 'All State',
      ALLSEVERITY: 'All Severity',
      ALLGROUPS: 'All Groups',
      SEVERITY: 'Severity',
      STATE: 'State',
      CLEAREDTIME: 'Cleared Time',
      CLEARED: 'Cleared',
      NOTCLEARED: 'Not Cleared',
      CLEAREDALARM: 'Cleared',
      UNCLEAREDALARM: 'Uncleared',
      CHANGEDALARM: 'Changed',
      NEWALARM: 'New Alarm',
      NOTIFICATIONTYPE: 'Notification Type',
      PROBABLECAUSE: 'Probable Cause',
      SPECIFICPROBLEM: 'Specific Problem',
      ADDITIONALTEXT: 'Additional Text',
      ADDITIONALINFORMATION: 'Additional Information',
      ALARMID: 'Alarm ID',
      EVENTTYPE: 'Event Type',
      EVENTTIME: 'Event Time',
      ACKUSER: 'Ack User',
      ACKTIME: 'Ack Time',
      ERRORCODE: 'Event',
      DEVICEFAULTPARAM: 'Device Fault Parameters',
      ATTACHEDMES: 'Attached Message',
      BYTAG: 'By Tag',
      RECEIVERLIST: 'Receiver List',
      RECEIVERCCLIST: 'Receiver CC List',
      RECEIVETAGSTOOLTIP: 'Add Receiver with specific Tag',
      RECEIVERCCTAGSTOOLTIP: 'Add Receiver CC with specific Tag',
      EMAILSUBJECT: 'Email Subject',
      EMAILCONTENT: 'Email Content',
      WITHACTIVEHOURS: 'With active hours',
      PRIPHONENUM: 'Primary Phone Number',
      SECPHONENUM: 'Secondary Phone Number',
      TEXTMESSAGE: 'Text Message',
      ACK: 'Ack',
      EDIT_STAGE_NOTIFICATIONS: 'Build Notification with Stage / Notifications',
      TOTALALARMSDESCRIPTION: 'Total number of Alarms reported by the System.',
      ALARMMGMT: 'Alarm Management',
      ALARMMGMTDESCRIPTION: 'List all Alarm reported by the System including Cleared, Un-cleared with Severity, Event time and Probable Cause.',
      ALARMCOUNTDESCRIPTION: 'Total number of Alarms with different Severity such as Critical, Major, Minor and Warning.',
      TARGETDEVICETAG: 'Target Device Tag',
    },
    PROVISIONING: {
      COLLAPSIBLE: 'Provisioning',
      WORKSFLOW: 'Workflows',
      WORKSFLOWLIST: 'Workflow List',
      WORKSFLOWLISTDESCRIPTION: 'List rules created by users to perform Operations to the specific Devices under specific conditions based on requirements.',
      CONFIGURATIONS: 'Configurations',
      CONFIGURATIONLIST: 'Configuration List',
      CONFIGURATIONLISTDESCRIPTION: 'List rules created automatically or by users to perform provisioning configurations to Devices under specific conditions based on requirements.',
      POLICYLIST: 'Energy Policy',
      POLICYLISTDESCRIPTION: 'List the energy-saving rules created automatically. These rules perform configuration settings on devices when the energy-saving mode is enabled.',
      CLONEPOLICY: 'Clone Policy',
      POLICYS: 'Policys',
      FROMWORKSFLOW: 'From Workflow',
      FROM: 'From',
      VALIDFROM: 'Valid From',
      CLONEWORKSFLOW: 'Clone Workflow',
      CLONECONFIGURATION: 'Clone Configuration',
      EDITWORKSFLOW: 'Edit Workflow',
      FORKWORKSFLOW: 'Fork Workflow',
      UPLOADWORKSFLOW: 'Upload Workflow',
      UPLOADQUEUE: 'Upload queue',
      OPERATIONS: 'Operation',
      PROFILES: 'Profiles',
      ACTIONS: 'Actions',
      CLONEOPERATIONS: 'Clone Profiles',
      FORKOPERATIONS: 'Fork Profiles',
      EDITOPERATIONS: 'Edit Operation',
      OPERATIONLIST: 'Profiles List',
      OPERATIONLISTDESCRIPTION: 'List rules created by users to perform Operations to the specific Devices under specific conditions based on requirements.',
      DUOPERATION: 'Add DU Operation',
      PARAPATH: 'Parameter Path',
      ENTERPARAPATH: 'Enter Parameter Path',
      ISPARAPATH: 'Parameter Path is required.',
      NEXTLEVEL: 'Next Level',
      PRODUCT: 'Product',
      SCRIPTS: 'Scripts',
      SCRIPTLIST: 'Script List',
      EDITSCRIPT: 'Edit Script',
      SCRIPTNAME: 'Script Name',
      FILES: 'Files',
      FILELIST: 'File List',
      FILELISTDESCRIPTION: 'A list created by users, containing required information when a Device performs file uploads or downloads, such as File Type, URL, and Authentication.',
      FILETYPE: 'File Type',
      SELECTFILETYPE: 'Select File Type',
      ENTERFILETYPE: 'Enter File Type',
      ISFILETYPE: 'This File Type is required!',
      ISURL: 'This Url is required!',
      DELAYSECONDS: 'Delay Seconds',
      TARGETNAME: 'Target File Name',
      ENTERTARGETNAME: 'Enter Target File Name',
      FILESIZE: 'File Size',
      DESCRIPTION: 'Description',
      SUBSCRIBE: 'Subscribe',
      SUBSCRIBETOPIC: 'Subscribe Topic',
      SELECTTOPIC: 'Select Subscribe Topic',
      VENDORFILE: 'Vendor Specific Files',
      VENDORFILEDESCRIPTION: 'A list created by users, containing required  Vendor specific information when a Device performs file uploads or downloads, such as File Type, URL, and Authentication.',
      ADDVENDORFILE: 'Add Vendor Specific Files',
      EDITVENDORFILE: 'Edit Vendor Specific Files',
      LATESTFIRMWARE: 'Latest Firmware',
      AVAILABLEFILES: 'Available Files',
      SETUPDETAILS: 'Setup Details',
      CODEDISTRIBUTION: 'Provisioning Code Distribution',
      OPERATENAME: 'Operate Name',
      ENTEROPERATENAME: 'Enter Operate Name',
      ADDINFORM: 'Add Inform Parameter',
      PUBLICTOPIC: 'Publish Topic',
      SELECTPUBLICTOPIC: 'Select Publish Topic',
      ISDATAMODEL: 'This DataModel is required!',
      CPELIMIT: 'CPE Limits',
      ISCPELIMIT: 'This CPE Limits is required!',
      SUMMARYACTION: 'Summary Action',
      ADDSUMMARYREPORT: 'Add Summary Report',
      SUMMARYREPORT: 'Summary Report',
      SUMMARYREPORTSETTING: 'Summary Report Setting',
      PLEASESELECTTEMPLATE: 'Please Select A Template First Before Proceeding',
      PLEASEFILLPARAMS: 'Please Fill In The Label Name And Parameter Path Before Proceeding',
      INFORMLIST: 'Inform Parameter List',
      SELECTTRIGGERREQ: 'Select Trigger (Required)',
      DEVICEPARAMLIST: 'Device Parameter List',
      ADDSTAGE: 'Stage',
      ENTERSTAGENAME: 'Enter Stage Name',
      SELECTFILE: 'Select File',
      SUCCESSURL: 'Success URL',
      FAILURL: 'Failure URL',
      NOTIFYTYPE: 'Notify Type',
      SELECTNOTIFYTYPE: 'Select Notify Type',
      NOTIFYPARAMS: 'Notify Params',
      SHOWDETAILS: 'show details',
      NOTIFYTYPEREQU: 'Select Notify Type (Required)',
      EDITNOTIFYPARA: 'Edit Notify Parameter',
      OBJECTPATH: 'Object Path',
      ENTEROBJECTPATH: 'Enter Object Path',
      ALLOWPAR: 'Allow Partial',
      ADDCREATEOBJ: 'Add Create Object',
      ISOBJECTNAME: 'Object name is required!',
      FILETARGET: 'File Target',
      SELECTSN: 'Select SN',
      GENERSUMMARYREPORT: 'Generate Summary Report',
      SCRIPT: 'Script',
      SELECTSCRIPT: 'Select Script',
      ACTIONSLIST: 'Actions List',
      PARAMTYPE: 'Parameter Type',
      ADDITIONALCON: 'Trigger Conditions',
      ADDCONDITION: 'Add additional condition',
      EDITCONDITION: 'Edit additional condition',
      DEVICEPARAMTRIGGER: 'Device Parameter Trigger',
      INFORM: 'Inform',
      DURATION: 'Duration',
      FIELD: 'Field',
      TRIGGEREVENTS: 'Trigger Events',
      TRIGGERTYPE: 'Trigger Type',
      INFORMEVENT: 'Inform Event',
      SELECTINFORMEVENT: 'Select Inform Event',
      EVENTNAME: 'Event Name',
      ENTEREVENTNAME: 'Enter Event Name',
      PARAMETERSKEY: 'Parameter ParametersKey',
      ENTERPARAMETERSKEY: 'Enter Parameter ParametersKey',
      PARAMETERSVALUE: 'Parameter ParametersValue',
      ENTERPARAMETERSVALUE: 'Enter Parameter ParametersValue',
      PROTOCOLVER: 'Protocol Versions',
      SOURCEURL: 'Source URL',
      TARGETURL: 'Target URL',
      SESSIONID: 'Session ID',
      OPERATEMODE: 'Operate Mode',
      SELECTOPERATEMODE: 'Select Operate Mode',
      INPUTURLFORMATE: 'Supported formats: http / https / ftp / ftps / sftp',
      HISTORY: 'History',
      WORKFLOWHISTORY: 'Workflow History',
      CONFIGURATIONHISTORY: 'Configuration History',
      POLICYHISTORY: 'Policy History',
      TRIGGERTIME: "Trigger Time",
      TARGETPRODUCT: 'Target Product',
      TARGETGROUP: 'Target Group',
      TARGETSOFTWAREVERSION: 'Target Software Version',
      TARGETSN: 'Target Device Serial Number',
      TARGETSV: 'Target Device Software Version',
      SUPPORTEDPRODUCT: 'Supported Product',
      MANAGEDEDPRODUCT: 'Managed Product',
      ALWAYSACTIVE: "Always Active",
      SELECTACTIVEDATERANGE: "Select Active Date & Time Range",
      DAYOFWEEK: 'With Day of Week',
      WITHONLYONCE: 'With Only Once',
      ACTIVEDATERANGE: 'Active Date Range',
      ACTIVETIMERANGE: 'Active Time Range In Day',
      EVERYDAY: 'Every Day',
      SUNDAY: 'Sunday',
      MONDAY: 'Monday',
      TUESDAY: 'Tuesday',
      WEDNESDAY: 'Wednesday',
      THURSDAY: 'Thursday',
      FRIDAY: 'Friday',
      SATURDAY: 'Saturday',
      EXECUTIONSTATUS: 'Execution Status',
      EXECUTIONTIME: 'Execution Time',
      EDITACTIONS: 'Edit Actions',
      DOWNLOADASMULTI: 'Download as multi files',
      DOWNLOADASONE: 'Download as one file',
      LASTEXECUTIONTIME: 'Last Execution Time',
      SEARCHSN: 'Search SN',
      ACTIVATE: 'Activate',
      DEACTIVATE: 'Deactivate',
      LOADING: 'Loading',
      STATETYPE: 'State Type',
      STAGE: 'Stage',
      EDIT_STAGE_OPERATIONS_DESCRIPTION: 'Build Workflow with Stage / Operations',
      EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION: 'Build Configuration with Stage / Operations',
      EDIT_POLICY_STAGE_OPERATIONS_DESCRIPTION: 'Build Policy with Stage / Operations',
      TRIGGERCONDITIONS: 'Trigger Conditions',
      BUILD: 'Build',
      SETUP_SCHEDULE_DESCRIPTION: 'Setup Active Time Range',
      INVALID_VALUE_MESSAGE: 'There are invalid values, please check the form.',
      RESET_TOOLTIP: 'Reset to default',
      RESET_CONFIRM: 'Are you want to reset all the initial provisioning value?',
      COUNT: 'Execution Count',
      COMPLETEDCOUNT: 'Completed',
      PARTIALFAILEDCOUNT: 'PartialFailed',
      CANCELEDCOUNT: 'Canceled',
      INPROGRESS: 'InProgress',
      FAILCOUNT: 'Failed',
      ADDTAGFAIL: 'Add Tags Fail!',
      ADDTAGSUCC: 'Add Tags Success!',
      DELTAGFAIL: 'Delete Tags Fail!',
      DELTAGSUCC: 'Delete Tags Success!',
      STEPAFTERSUCCESS: "Stop subsequent task if failure",
      WORKFLOWOPERATIONLOG: 'Workflow Operation Logs',
      OPERATIONLOGS: 'Operation Logs'
    },
    USERS: {
      ACCOUNT: 'Accounts',
      ONLINEUSERS: 'Online Users',
      ONLINEUSERSDESCRIPTION: "The total number of users who are currently in use or have logged in within half an hour.",
      PROFILE: 'Profile',
      PROFILEDESCRIPTION: 'Detailed information of the currently logged-in user.',
      STATUS: 'Status',
      ALLSTATUS: 'All Status',
      ALLTYPE: 'All Type',
      ROLE: 'Role',
      ROLELIST: 'Role List',
      ROLELISTDESCRIPTION: 'List Roles used to User’s Authority control.',
      CANNOTFINDROLE: "Can't find the role authority list",
      CHANGE: 'Change',
      ACCOUNTLIST: 'Account List',
      ACCOUNTLISTDESCRIPTION: 'List Accounts that the currently logged-in user can manage.',
      EDITUSER: 'Edit User',
      EXPIRATION: 'Expiration Date',
      DEPLOYEXPIRATION: 'Deployed Expiration',
      CONTROLLIST: 'Access Control List',
      ALLEDITABLE: 'All Editable',
      EDITABLE: 'Editable',
      ALLREADONLY: 'All Read Only',
      READONLY: 'Read Only',
      ALLDISABLED: 'All Disabled',
      DISABLED: 'Disabled',
      SUBMIT: 'Submit',
      AMPNODE: 'AMP Node',
      SUPERADMINPASSWORD: 'Super Admin Password',
      ACTIVITIES: 'Account Logs',
      ACTIVITIESLIST: 'Activities List',
      ACTIVITIESLISTDESCRIPTION: 'Log all request events for accounts that the currently logged-in user can manage.',
      DATERANGE: 'Select Date Range',
      BEGINTIMEDATERANGE: 'Select Begin Date',
      ENDTIMEDATERANGE: 'Select End Date',
      DASHPERMISSION: 'Dashboard Permission',
      CONFIRMPASSWORD: 'Confirm Password',
      NOTMATCH: 'Password & Confirm Password does not match.',
      NOTMATCH2: 'New Password & Confirm Password does not match.',
      ISPASSWORD: 'Password is required.',
      ISUSERNAME: 'User name is required.',
      ADDUSER: 'Add User',
      USERROLE: 'User Role',
      CONFIRMPSW: 'Passwords must have 8-128 characters and contain at least the following: letters, numbers and symbols.',
      SPECIALSYMBOLS: 'Only 1-32 characters of letters numbers and special symbols ( @ ! # ? $ / \ _ - .) allowed.',
      SPECIALSYMBOLS_NO_DASH: 'Only 1-128 characters of letters numbers and special symbols ( @ ! # ? $ / \ _ .) allowed.',
      ADDNEWUSER: 'Add New User',
      USERACTION: 'User Action',
      LANGUAGE: 'Language',
      AUTHORITYLIST: 'Authority List of Widget Class',
      CHANGEPASSWORD: 'Change Password',
      APIDOCUMENT: 'API Document',
      USERMANUAL: 'User Manual',
      OLDPASSWORD: 'Old Password',
      NEWPASSWORD: 'New Password',
      PREVIOUSTIME: 'Latest Login Time',
      PREVIOUSLOCATION: 'Latest Login Location',
      LASTLOGINLOCATION: 'Last Login Location',
      CURRENTTIME: 'Current Login Time',
      CURRENTLOCATION: 'Current Login Location',
      EDITROLE: 'Edit Role',
      ADDNEWROLE: 'Add New Role',
      AUTHORITY: 'Role Template',
      EMAIL: 'E-mail',
      ACTIONTYPE: 'Action Type',
      EMAILERROR: 'The format of the E-mail is incorrect',
      ISMAIL: 'E-mail is required.',
      TAGHASCREATED: 'This tag has been created by another user',
      DEVICEADMIN_TITLE: 'Device Administration',
      DEVICEADMIN_DESCRIPTION: 'Authority to Edit/Read Device related widgets classified in Device class/Device Admin subclass.Included widgets:Device List,Live Update,Reboot,Upgrade Firmware.',
      PRODUCTADMIN_TITLE: 'Product Administration',
      PRODUCTADMIN_DESCRIPTION: 'Authority to Edit/Read Product related widgets classified in Device class/Product Admin subclass,Included widgets:Registered Devices,Product List.',
      GROUPADMIN_TITLE: 'Group Administration',
      GROUPADMIN_DESCRIPTION: 'Authority to Edit/Read Group related widgets classified in Device class/Group Admin subclass.Included widgets:Group List,Add Device to Group,Add Operation.',
      GENERALDATA_TITLE: 'General Data',
      GENERALDATA_DESCRIPTION: 'Authority to Edit/Read Device general Data widgets classified in Device class/General Data subclass.Included widgets:Online Devices,Groups,Product Model,General Info,KPI.',
      ALARMMANAGEMENT_TITLE: 'Alarm Management',
      ALARMMANAGEMENT_DESCRIPTION: 'Authority to Edit/Read Alarm Management widgets in specific Device classified in Device class/Alarm Management subclass.Included widget:Alarm Management.',
      REMOTETROUBLESHOOTING_TITLE: 'Remote Troubleshooting',
      REMOTETROUBLESHOOTING_DESCRIPTION: 'Authority to Edit/Read Remote Advanced related widgets classified in Device class/Remote Troubleshooting subclass,Included widgets:Terminal, Command XML.',
      DATAMODEL_TITLE: 'Data Model',
      DATAMODEL_DESCRIPTION: 'Authority to Edit/Read Data Model related widgets classified in Device class/Data Model subclass.Included widget:Data Node,Parameter Data.',
      NETWORKLOCATION_TITLE: 'Network Location',
      NETWORKLOCATION_DESCRIPTION: 'Authority to Edit/Read network Location related widgets classified in Device class/Network Location subclass,Included widgets:Location, Network Topology, Coverage Map.',
      LOGCOLLECTION_TITLE: 'Log Collection',
      LOGCOLLECTION_DESCRIPTION: 'Authority to Edit/Read Log Collection related widgets classified in Device class/Log Collection subclass,Included widgets:Session Log List,Generate Report,Operation Logs List.',
      STATISTICALANALYSIS_TITLE: 'Statistical Analysis',
      STATISTICALANALYSIS_DESCRIPTION: 'Authority to Edit/Read Statistical Analysis widgets classified in Device class/Statistical Analysis subclass.',
      WIFISPECIFIC_TITLE: 'WiFi Specific',
      WIFISPECIFIC_DESCRIPTION: 'Authority to Edit/Read WiFi Specific widgets classified in Device class/WiFi Specific subclass.Included widgets:WiFi Clients,WiFi Radio Status,WiFi Analyzer,WiFi Neighbor List.',
      CELLULARSPECIFIC_TITLE: 'Cellular Specific',
      CELLULARSPECIFIC_DESCRIPTION: 'Authority to Edit/Read Cellular Specific widgets classified in Device class/Cellular Specific subclass.Included widgets:UE,Cell Status,Antenna Beam,Neighbor/PLMN List.',
      PMKPICOUNTER_TITLE: 'PM KPI Counter',
      PMKPICOUNTER_DESCRIPTION: 'Authority to Edit/Read PM KPI Counter widgets classified in Device class/PM KPI Counter subclass.Included widgets:PM Chart,PM Parameter.',
      FAPSPECIFIC_TITLE: 'FAP Specific',
      FAPSPECIFIC_DESCRIPTION: 'Authority to Edit/Read FAP Specific widgets classified in Device class/Fap Specific subclass.',
      APPSPECIFIC_TITLE: 'APP Specific',
      APPSPECIFIC_DESCRIPTION: 'Authority to Edit/Read Application Specific widgets classified in Device class/App Specific subclass.Included widgets:Application List,Service Provider,Device Apps Status.',
      YANGMODULE_TITLE: 'Yang Module',
      YANGMODULE_DESCRIPTION: 'Authority to Edit/Read Yang Module widgets classified in Device class/Yang Module subclass.',
      POWERSAVING_TITLE: 'Power Saving',
      POWERSAVING_DESCRIPTION: 'Authority to Edit/Read Power Saving widgets classified in Device class/Power Saving subclass.Included widgets:Energy Management.',
      DOCSIS_TITLE: 'Docsis Specific',
      DOCSIS_DESCRIPTION: 'Authority to Edit/Read Docsis Specific widgets classified in Device class/Docsis Specific subclass.Included widgets:DOCSIS Status.',
      DEVICEALARM_TITLE: 'Device Alarm',
      DEVICEALARM_DESCRIPTION: 'Authority to Edit/Read Device Alarm widgets classified in Alarm class/Device Alarm subclass.Included widgets:Total Alarms,Alarm Management (Download/Ack),Device Event Tracking.',
      NOTIFICATIONMANAGMENT_TITLE: 'Notification Management',
      NOTIFICATIONMANAGMENT_DESCRIPTION: 'Authority to Edit/Read Notification Management widgets classified in Alarm class/Notification Management subclass.Included widget:Notification List',
      WORKFLOWSETUP_TITLE: 'Workflow/Configuration Setup',
      WORKFLOWSETUP_DESCRIPTION: 'Authority to Edit/Read Workflow/Configuration Setup widgets classified in Provisioning class/Workflow Setup subclass.Included widgets:Workflow/Configuration List,Workflow/Configuration History.',
      OPERATIONSETUP_TITLE: 'Operation Setup',
      OPERATIONSETUP_DESCRIPTION: 'Authority to Edit/Read Operation Setup widgets classified in Provisioning class/Operation Setup subclass.Included widget:Action List.',
      POLICYSETUP_TITLE: 'Policy Setup',
      POLICYSETUP_DESCRIPTION: 'Authority to Edit/Read Policy Setup widgets classified in Provisioning class/Policy Setup subclass.Included widgets:Energy Policy.',
      SCRIPTSETUP_TITLE: 'Script Setup',
      SCRIPTSETUP_DESCRIPTION: 'Authority to Edit/Read Script Setup widgets.',
      FILESETUP_TITLE: 'File Setup',
      FILESETUP_DESCRIPTION: 'Authority to Edit/Read File Setup widgets classified in Provisioning class/File Setup subclass.Included widgets:File List,Vendor Specific Files.',
      ACCOUNTADMIN_TITLE: 'Account Administration',
      ACCOUNTADMIN_DESCRIPTION: 'Authority to Edit/Read Account administration widgets classified in User class/Account Admin subclass.Included widgets:Profile,Account List.',
      ACCOUNTLOG_TITLE: 'Account Log',
      ACCOUNTLOG_DESCRIPTION: 'Authority to Edit/Read Account Log widgets classified in User class/Account Log subclass.Included widget:Activities List.',
      ACCOUNTROLE_TITLE: 'Account Role',
      ACCOUNTROLE_DESCRIPTION: 'Authority to Edit/Read Account Role widgets classified in User class/Account Role subclass.Included widget:Role List.',
      DEVICESTATISTICS_TITLE: 'Device Statistics',
      DEVICESTATISTICS_DESCRIPTION: 'Authority to Edit/Read Device Statistics widgets classified in Analysis class/Device Statistics subclass.Included widgets:Online Devices,New Devices,Event Code.',
      SYSTEMSTATISTICS_TITLE: 'System Statistics',
      SYSTEMSTATISTICS_DESCRIPTION: 'Authority to Edit/Read System Statistics widgets classified in Analysis class/System Statistics subclass.Included widgets:DB Status,Device Sessions Duration,Device Sessions Rate,Free Memory.',
      PROVISIONINGSTATISTICS_TITLE: 'Provisioning Statistics',
      PROVISIONINGSTATISTICS_DESCRIPTION: 'Authority to Edit/Read Provisioning Statistics widgets classified in Analysis class/Provisioning Statistics subclass.Included widgets:Provisioning Code,Software Version,SIM Status.',
      PMSTATISTICS_TITLE: 'PM Statistics',
      PMSTATISTICS_DESCRIPTION: 'Authority to Edit/Read PM Statistics widgets classified in Analysis class/PM Statistics subclass.Included widgets:PM,PM Status,Performance Report.',
      SERVERSETTING_TITLE: 'Server Setting',
      SERVERSETTING_DESCRIPTION: 'Authority to Edit/Read Server Setting widgets classified in System class/Server Setting subclass.Included widgets:General,Live Update,CWMP,Netconf,Performance Service.',
      SERVERPREFERENCE_TITLE: 'Server Preference',
      SERVERPREFERENCE_DESCRIPTION: 'Authority to Edit/Read Server Preference widgets classified in System class/Server Preference subclass.Included widgets:SMTP Notifications,SNMP Trap Notifications,Reports,Statistics,Logs.',
      SERVERLICENSE_TITLE: 'Server License',
      SERVERLICENSE_DESCRIPTION: 'Authority to Edit/Read Server License widgets classified in System class/Server License subclass.Included widget:License.',
      SERVERREPORT_TITLE: 'Server Report',
      SERVERREPORT_DESCRIPTION: 'Authority to Generate/Read Server Report widgets classified in Add-On class/Report Export subclass.Included widget:Generate Summary Report.',
      SYSTEMEVENTS_TITLE: 'System Events',
      SYSTEMEVENTS_DESCRIPTION: 'Authority to Edit/Read System Events widgets classified in System class/System Events subclass.Included widget:System Events, Registration Log.',
      SYSTEMNODES_TITLE: 'System Nodes',
      SYSTEMNODES_DESCRIPTION: 'Authority to Edit/Read System Nodes widgets classified in System class/System Nodes subclass.Included widget:Nodes.',
      PERSONALTHEME_TITLE: 'Personal Theme',
      PERSONALTHEME_DESCRIPTION: 'Authority to Edit/Read Personal Theme widgets classified in Add-On class/Personal Theme subclass.',
      REPORTEXPORT_TITLE: 'Report Export',
      REPORTEXPORT_DESCRIPTION: 'Authority to Edit/Read Report Export widgets classified in Add-On class/Report Export subclass.Included widget:Generate Summary Report.',
      SYSTEMINFORMATION_TITLE: 'System Information',
      SYSTEMINFORMATION_DESCRIPTION: 'Authority to Edit/Read AMP system information widgets classified in System class/System Information subclass.Included widget:System Informations.',
      FURBISHMENTSTATISTICS_TITLE: 'Refurbishment statistics',
      GENERAL_TITLE: 'General',
      GENERAL_DESCRIPTION: 'Authority to Edit/Read General widgets classified in 5G Core class/General subclass.',
      UE_TITLE: 'UE',
      UE_DESCRIPTION: 'Authority to Edit/Read UE widgets classified in 5G Core class/UE subclass.',
      CELL_TITLE: 'Cell',
      CELL_DESCRIPTION: 'Authority to Edit/Read Cell widgets classified in 5G Core class/Cell subclass.',
      ALARM_TITLE: 'Alarm',
      ALARM_DESCRIPTION: 'Authority to Edit/Read Alarm widgets classified in 5G Core class/Alarm subclass.',
      SYSTEM_TITLE: 'System',
      SYSTEM_DESCRIPTION: 'Authority to Edit/Read actions and widgets related to the 5GC system.',
      TOTALREQUESTS_TITLE: 'Total Requests',
      TOTALREQUESTS_DESCRIPTION: 'Number of Total Requests over the past 24 hours',
      EXTERNAL_DEVICE_TITLE: 'Device',
      EXTERNAL_DEVICE_DESCRIPTION: 'Authority to Edit/Read NBI permission for External class/Device subclass.',
      CBSD_DEVICE_TITLE: 'Device',
      CBSD_DEVICE_DESCRIPTION: 'Authority to Edit/Read NBI permission for CBSD class/Device subclass.',
      REQUESTSHISTORY_TITLE: "Request History",
      REQUESTSHISTORY_DESCRIPTION: "Historical chart of Total Requests over the past 24 hours",
      IPREQUESTDISTRIBUTION: "IP Request Distribution",
      IPREQUESTDISTRIBUTION_DESCRIPTION: "Top 5 number of Total Requests for each IP over the past 24 hours.",
    },
    ANALYSIS: {
      COLLAPSIBLE: 'Analysis',
      SYSTEM: 'System Statistics',
      SESSIONDURATION: 'Device Sessions Duration',
      SESSIONRATE: 'Device Sessions Rate',
      LATENCY: 'Device Request Latency',
      REQUESTRATE: 'Device Requests Rate',
      PARSING: 'Device Requests Parsing',
      MEMORY: 'Memory Usage',
      SPACEUSAGE: "Space Usage",
      CPUUTILIZE: 'CPU Utilize',
      MEMORYUSAGECHART: 'Memory Usage Chart',
      FREEMEMORY: 'Free Memory',
      CPUUSAGE: 'CPU Usage',
      CPUUSAGECHART: 'CPU Usage Chart',
      FREEDISK: 'Free Disk',
      DEVICE: 'Device Statistics',
      PM: 'PM Statistics',
      TOTALDEVICE: 'Total Devices',
      NEWDEVICE: 'New Devices',
      SESSIONS: 'Sessions',
      EVENTCODE: 'Event Code',
      MEMORYUTILIZATION: 'Memory Utilization',
      DISKUTILIZATION: 'Disk Utilization',
      MEMORYUTILIZATIONDESCRIPTION: 'Memory Utilization Historical Chart.',
      DISKUTILIZATIONDESCRIPTION: 'Disk Utilization Historical Chart.',
      SESSIONDURATIONDESCRIPTION: 'Historical chart of the average of Session duration for all Devices at regular intervals.Session duration: The total time spent in a session between the Device and AMP.',
      SESSIONRATEDESCRIPTION: 'Historical chart of the average of Session rate for all devices at regular intervals.Session rate: The number of sessions initiated by the Device to AMP per second.',
      LATENCYDESCRIPTION: 'Historical chart of the average of Request latency for all Devices at regular intervals.Request Latency: The total time spent in a request between the Device and AMP.',
      REQUESTRATEDESCRIPTION: 'Historical chart of the average of Request rate for all Devices at regular intervals.Request rate: The number of requests initiated by the Device to AMP per second.',
      PARSINGDESCRIPTION: 'Historical chart of the average of Request parsing for all devices at regular intervals.Request Parsing: The total time spent for AMP parsing a request initiated by the Device.',
      MEMORYDESCRIPTION: 'Free Memory Historical Chart.',
      CPUUSAGEDESCRIPTION: 'CPU Usage Historical Chart.',
      FREEDISKDESCRIPTION: 'Free Disk Historical Chart.',
      TOTALDEVICEDESCRIPTION: 'Historical chart of the total number of Devices at regular intervals.',
      ONLINEDEVICEDESCRIPTION: 'Historical cahrt of the total number of Online Devices at regular intervals.',
      NEWDEVICEDESCRIPTION: 'Historical chart of the total number of new registered Devices at regular intervals.',
      SESSIONSDESCRIPTION: 'Historical chart of the total number of Sessions for each Product at regular intervals.',
      EVENTCODEDESCRIPTION: 'Historical chart of the total number of Event Codes for each Product at regular intervals.',
      PROVISIONING: 'Provisioning Statistics',
      PMSTATISTICS: 'PM Statistics',
      STATUSFORDEVICES: 'Status For Online Devices',
      RATE: 'Rate',
      NUMBER: 'Number',
      VERSIONDISTRIBUTION: 'Software Version Distribution',
      CODEDISTRIBUTION: 'Provisioning Code Distribution',
      XMPPSTATUS: 'XMPP Status',
      XMPPSTATUS_DESCRIPTION: 'XMPP service Status.',
      IMSSTATUS: 'IMS Registration Status',
      SIMSTATUS: 'SIM Status',
      IPSECSTATUS: 'IPSec Tunnel Status',
      STATUSFORDEVICE: 'Status For Total Devices',
      DBSTATUS: 'DB Status',
      DBSTATUS_DESCRIPTION: 'Database service Status.',
      SELECTDURATION: 'Select Duration',
      PMSTATUS: 'PM Status',
      PMSTATUS_DESCRIPTION: 'PM service Status.',
      REFURBISHMENETHISTORY: 'Refurbishing History'
    },
    SYSTEM: {
      COLLAPSIBLE: 'System',
      EXTERNALSERVICE: 'External Service',
      PERFORMANCESERVICE: 'Performance Service',
      PERFORMANCESERVICEDESCRIPTION: 'PM related setting such as default URL for Device uploading KPI file.',
      PREFERENCE: 'Preference',
      DBSERVER: 'DB Servers',
      SOURCES: 'Data Sources',
      SNMPTRAP: 'SNMP Trap Notifications',
      SNMPTRAP_DESCRIPTION: 'Configuration about Notification via SNMP.',
      SMTP: 'SMTP Notifications',
      SMTP_DESCRIPTION: 'Configuration about Notification via SMTP.',
      SMS: 'SMS Notifications',
      STATISTICSPRE: 'Statistics',
      STATISTICSPRE_DESCRIPTION: 'Preference setting about rotation of Data Collection in AMP.',
      LOGPRE: 'Logs',
      LOGPRE_DESCRIPTION: 'Log rotation setting for Device Session and Operation.',
      FAULT: 'Alarms',
      FAULT_DESCRIPTION: 'Preference setting for Alarms such as Auto-Acknowledge and Alarm rotation.',
      REPORTS: 'Reports',
      REPORTS_DESCRIPTION: 'Report rotation setting for Device Summary Report.',
      SYSREPORT: 'Server Report List',
      GENERATESYSREPORT: 'Generate Server Report',
      CONFIRMDELETE: 'Confirm Deleting All Server Reports',
      DOCONFIRMDELETE: 'Do you want to delete all Server Reports',
      CONFIRMDELETESELECT: 'Confirm Delete Selected Server Reports',
      DOCONFIRMDELETESELECT: 'Do you want to delete the selected Server Reports',
      DOWNLOADCSV: 'Download CSV',
      DOWNLOADTXT: 'Download TXT',
      DOWNLOADXLSX: 'Download XLSX',
      LICENSE: 'License',
      LICENSE_DESCRIPTION: 'Information about AMP License such as State, Type, Supported protocol and Expiration. Allow user to update License to extend expiration or enable more features in AMP.',
      LICENSETYPE: 'Edition',
      LICENSESTATE: 'License State',
      KEY_DESCRIPTION: "Provide the functionality to activate AMP after entering a key, along with key-related information.",
      CPENUM: 'Device Capacity',
      SESSIONNUM: 'Session Number',
      REMAININGTIME: 'Remaining Time',
      VALIDTO: 'Valid To',
      DISPLAY: 'Display',
      GENERAL: 'General',
      GENERAL_DESCRIPTION: 'System General setting such as Session log level, Logout Timeout and Server report.',
      CWMP: 'CWMP',
      CWMP_DESCRIPTION: 'Settings related to the CWMP protocol such as Session timeout and Online criteria.',
      NETCONF: 'Netconf',
      NETCONF_DESCRIPTION: 'Settings related to the NETCONF protocol such as Retry Interval and  Keep alive Interval.',
      USP: 'USP',
      USP_DESCRIPTION: 'Settings related to the USP protocol such as via Web socket or MQTT.',
      LIVEUPDATE: 'Live Update',
      LIVEUPDATE_DESCRIPTION: 'Features for initiating communication between AMP and  specific Device right away.',
      MQTT: 'System MQTT',
      FILES: 'Files',
      FILES_DESCRIPTION: 'System default Files setting such as FOTA and Device log URL and authentication.',
      TELEMETRY: 'System Telemetry',
      SUMMARYREPORT: 'Summary Report',
      SUMMARYREPORT_DESCRIPTION: 'Map related settings such as selection of map data providers and sources.',
      EVENTS: 'Events',
      SYSEVENTS: 'System Event',
      SYSEVENTS_DESCRIPTION: 'List all AMP-related Events with detailed information such as Severity, Specific Problem, Probable cause and Event time.',
      REGISTRATIONLOG: 'Registration Log',
      REGISTRATIONLOG_DESCRIPTION: 'Usp mqtt register log',
      NODES: 'Nodes',
      DEL_TITLE_NODES: 'Do you want to delete the node',
      NODES_DESCRIPTION: 'List AMP Nodes information such as Node name, IP Address, AMP Version and Uptime.',
      ENTERLICENSEKEY: 'Enter License Key',
      LICENSEKEY: 'License Key',
      KEY: 'Key',
      UNIQUEID: 'uniqueID',
      KEYVALIDITY: 'Key Validity',
      EDITURL: 'Edit URL',
      VERIFYSMTP: 'Test Email Send',
      VERIFYSNMP: 'Test SNMP Send',
      SERVICESTATUSTRACKING: 'Service Status Tracking',
      KPIFACTORS: 'KPI Factors',
      KPIFACTORSTRACKING: 'KPI Factors for Tracking',
      VERIFYXMPP: 'Test XMPP',
      MAP_DESCRIPTION: 'Map',
      PROCESS: 'Process',
      NODEID: 'Node ID',
      SEVERITY: 'Severity',
      LOCATIONMAP: 'Location Map',
      LOCATIONMAP_DESCRIPTION: 'Map related settings such as selection of map data providers and sources.',
      FIVECORESERVICE: '5G Core Service',
      FIVECORESERVICE_DESCRIPTION: '5G Core Service related setting such as 5G Core Vendor and Server URL.',
      ENERGYMANAGEMENT: 'Energy Management',
      ENERGY_DESCRIPTION: 'Settings related to energy management such as power limit and sleep interval.',
      NIDS: 'NIDS',
      NIDS_DESCRIPTION: 'Network Intrusion Detection System.',
      PROMETHEUS: 'Metrics Collection - Prometheus',
      PROMETHEUS_DESCRIPTION: 'Settings related to Data Collection using Prometheus.',
      PROMETHEUS_PARAMETER_DESCRIPTION: {
        PULL_PATH: 'URLs to scrape metrics from.',
        USERNAME: 'The username in the Authorization header for each Prometheus metric pull request.',
        PASSWORD: 'The password in the Authorization header for each Prometheus metric pull request.',
      },
      KAFKA: 'Metrics Collection - Kafka',
      KAFKA_DESCRIPTION: 'Settings related to Data Collection using Kafka.',
      KAFKA_PARAMETER_DESCRIPTION: {
        BROKERS: 'URLs of kafka brokers.',
        TOPIC: 'Kafka topic for producer messages.',
        ROUTING_KEY: 'Message routing mechanism.',
        ACCESS_TOKEN: 'Token for authentication.'
      },
      NODE_DESCRIPTION: {
        UPGRADE: 'Upgrade',
        ADDRESS: 'Address',
        HASHKEY: 'Hash key',
        TYPE: 'Type',
        USERNAME: 'User Name',
        PASSWORD: 'Password',
        TARGEVERSION: 'Targe Version',
        COMPOENT: 'Component',
        RUNTOOL: 'Run Tool',
        UPGRADESUCC: 'Upgrade was successful',
        UPGRADEFAIL: 'Upgrade failed',
        WAITFORUPDATE: 'Dowload upgrade files',
        STARTUPDATING: 'Start upgrade...',
        BEINGUPDATED: 'Upgrade complete and waiting  for restart',
        SERVERRESTART: 'Server is restarting...',
        TIMEOUT: 'Upgrade timeout!',
        ACSNODEREBOOT: 'Reboot this AMP node may cause loss of data.',
        ACSNODESHUTDOWN: 'Shutdown this AMP node may cause loss of data.',
      },
      SETTING_DESCRIPTION: {
        GENERAL: {
          SESSIONTIMEOUT: 'The setting is configurable timeout for CWMP session.',
          TRANSACTIONTIMEOUT: 'Transaction Timeout is the request and response timeout.',
          REFRESHINTERVAL: 'Netconf Retry Interval.',
          KEEPALIVEINTERVAL: 'Netconf Keepalive Interval.',
          SESSIONLOGLEVEL: 'This setting influences on the detailed Device Session log view: If ORIGINAL, the session is presented as SOAP envelops; If RPC, as a parsed SOAP messages.',
          DEVICE: "The settings Toggle On/Off send Device. all the following parameters are used for the device's initial online BootStrap method.",
          DEVICE_XMPP_CONNECTION_1: 'The settings Toggle On/Off send Device.XMPP.Connection.1 for the methods BootStrap.',
          DEVICE_MQTT_CLIENT_1: 'The settings On/Off send Device.MQTT.Client.1 for the methods BootStrap.',
          DEIVCE_DEVICEINFO_XVENDOR_HOLD: 'The settings Toggle On/Off send Devcie.DeviceInfo.X_VENDOR.HOID for the methods BootStrap.',
          DEVICE_MANAGEMENTSERVER_PERIODICINFORMTIME: 'The settings Toggle On/Off send Device.ManagementServer.PeriodicInformTime for the methods BootStrap.',
          TYPEOFINFORMRECEIVEDWITHIN: 'The settings is used to determine whether the device is online.',
          INTERVALOFINFORMRECEIVEDWITHIN: 'The settings configures the period for the drop condition.',
          SERVERREPORTENABLED: 'The settings Toggle On/Off server report generation: If On, the server report will be generated by the ACS. If Off the server report will not be generated by the ACS.',
          SERVERREPORTEMAILNOTIFICATION: 'Enable or disable the system reports email notification function.',
          SERVERREPORTPERIOD: 'The setting configures the frequency (in days) of the server report generation.',
          SERVERREPORTCONTENT: 'System report optional content, AMP supports information collection for MONGODB, XMPP, MQTT, and PM server.',
          PRIORITY: 'Defines priority of the CPE’s IP detection sources: RemoteAddress - IP is obtained from the specified data node. Eq.Device.DeviceInfo.X_Vendor_GlobalIPAddress; X-Forwarded-For - IP is obtained from XForwarded-For HTTP header; Custom - IP is obtained from the custom HTTP header, which name is specified in the [Custom Header] setting.',
          CUSTOMHEADER: 'Name of the custom HTTP header, which contains the CPE’s actual IP address.',
          IDLETIMEOUT: 'System Session expiration time.',
          SWAGGER_ENABLED: 'Eanble/Disable resful API UI.',
          CLIENT_URL: 'The URL for the CPE to connect to the ACS using the CPE WAN Management Protocol.'
        },
        CONNECTIONREQUEST: {
          USERNAME: 'Configurable username for the CR mechanism.',
          PASSWORD: 'Configurable password for the CR mechanism.',
          RETRYTIMEOUT: 'Configurable timeout for the CR.',
          NUMBEROFRETRY: 'The number of maximum CR attempts. ',
          TYPE: 'The type for the CR.',
          // XMPPADDRESS:'',
          XMPPDOMAIN: 'The setting is configurable domain name, used in JID auto generation.',
          XMPPPORT: 'The port of the XMPP server. Default value specified for ejabberd server.',
          XMPPACSUSERNAME: 'Configurable ACS username for the XMPP CR invoke.',
          XMPPACSPASSWORD: 'Configurable ACS password for the XMPP CR invokes.',
          XMPPADMINPORT: 'The port of the XMPP server for server administrator. Default value specified for ejabberd server.',
          XMPPADMINUSERNAME: 'The setting is configurable XMPP admin username credential, used in auto XMPP user registration.',
          XMPPADMINPASSWORD: 'The setting is configurable XMPP Admin password credential, used in auto XMPP user registration.',
          XMPPRESOURCE: 'Configurable Resource value for the XMPP CR.',
          XMPPUSETLS: 'The settings Toggle On/Off TLS usage for the XMPP CR: If On, TLS is enabled. If Off, TLS is disabled.'
        },
        USP: {
          BINDING: 'Binding Type, WebSocket or MQTT.',
          ADDRESS: 'MTP Server Domain.',
          PORT: 'MTP Connect Port.',
          APIKEY: "In MQTT, an API Key is utilized to query the server's status.",
          USERNAME: 'The username required by the broker, if any.',
          PASSWORD: 'The password required by the broker, if any.',
          USE_TLS: 'MTP over TLS.',
          EXADDRESS: 'The address or domain where the device can connect to the WebSocket/MQTT/CWMP/XMPP service.',
          EXPORT: 'The port where the device can connect to the WebSocket/mqtt service.',
          USETLS: 'Does the device use TLS to connect to the WebSocket/MQTT/CWMP/XMPP service.',
          EXURL: 'The URL where the device can connect to the CWMP service.',
        },
        FILES: {
          DOWNLOAD: {
            LATESTFIRMWARE: 'The setting allows User to specify the latest firmware version for CSR users.',
            FIRMWARESERVERURL: 'The path to the file server is to be used by APs to download a firmware.',
            FIRMWARESERVERUSERNAME: 'Username credential to authenticate with the file server.',
            FIRMWARESERVERPASSWORD: 'Password credential to authenticate with the file server.',
          },
          UPLOAD: {
            FILETYPE: 'Device upload file type.',
            INSTANCEPATH: 'The path in datamodel for users can upload log file.',
            LOGUPLOADURL: 'The URL where CSR users can upload AP log files. This feature requires additional server configuration.',
            USERNAME: 'Configurable username for the file upload.',
            PASSWORD: 'Configurable password for the file upload.'
          },
          CONF_DOWNLOAD: {
            DEFAULT_FILE: 'The selected default configuration file from Files (type: 3 Vendor Configuration File).',
            DEFAULT_FILE_URL: 'The URL of the default configuration file.',
            FILETYPE: 'Device Configuration file download type.',
            CONFURL: 'The URL for the device to download the configuration file from AMP.',
            USERNAME: 'Configurable username for the device download.',
            PASSWORD: 'Configurable password for the device download.'
          }
        },
        TELEMETRY: {
          TELEMETRYSERVERREDIRECTION: 'The settings Toggle On/Off 3rd party website link button on device info page. If On, 3rd party website link button displayed. If Off, 3rd party website link button not displayed. ',
          VENDOR: 'Supplier providing specific technology, services, or products, with options including Druid, DNMM, HP, and Open 5GC.',
          TYPE: 'Open source network intrusion detection system type, options include Suricata',
          SERVERURL: 'The URL of 3rd party website.',
          SERVERUSERNAME: 'Configurable username for login 3rd party website.',
          SERVERPASSWORD: 'Configurable password for login 3rd party website.',
          KPIFACTORS: 'KPI Factors can monitor PM parameter status based on user-defined rules.',
          UEINTERVAL: 'configurable UE Timer interval.',
          CELLINTERVAL: 'configurable Cell Timer interval.',
          ALARMINTERVAL: 'configurable Alarm Timer interval.',
          COMMONINTERVAL: 'configurable Common Timer interval.',
          APIURL: 'The northbound interface address of Performance Service.',
          APIUSERNAME: 'The username for the identity of the northbound interface of Performance Service.',
          APIPASSWORD: ' The password for the identity of the northbound interface of Performance Service.'
        },
        MAP: {
          TYPE: 'Map server type, AMP support Google map and Open street map. default is Google map.',
          URL: 'Map server url.',
          APIKEY: 'Map api key.',
        }
      },
      PREFERENCE_DESCRIPTION: {
        SMTP: {
          MAIL_HEALTHY: 'System health monitoring, like CPU loading,disk usage,crash and license.',
          MAIL_FORM: 'Email sender username.',
          MAIL_HOST: 'The SMTP server address that corresponds to the mailbox.',
          MAIL_USERNAME: 'Email sender address.',
          MAIL_PASSWORD: 'Email sender password.',
          MAIL_PORT: 'Email sender port.',
          MAIL_TO: 'Recipient Address',
          MAIL_SMTP_AUTH: 'SMTP protocol-related configuration,Whether authentication is required.',
          MAIL_SMTP_SECURITY: 'SMTP protocol-related configuration.',
          MAIL_TRANSPORT_PROTOCOL: 'Not currently used.',
        },
        SNMPTRAP: {
          SNMPTRAP_TARGET: 'SNMP Trap Destination Address.',
          SNMPTRAP_PORT: 'UDP port to send requests too, defaults to 161.',
          SNMPTRAP_RETRIES: 'Number of times to re-send a request, defaults to 1.',
          SNMPTRAP_TIMEOUT: 'Number of milliseconds to wait for a response before re-trying or failing, defaults to 5000.',
          SNMPTRAP_TRANSPORT: 'Specify the transport to use, can be either udp4 or udp6, defaults to udp4.',
          SNMPTRAP_TRAPPORT: 'UDP port to send traps and informs too, defaults to 162.',
          SNMPTRAP_VERSION: 'Either snmp.Version1 or snmp.Version2c.',
          SNMPTRAP_BACKOFF: 'The factor by which to increase the timeout for every retry, defaults to 1 for no increase.',
          SNMPTRAP_COMMUNITY: 'Used to ensure the security of communication and authentication.',
        },
        REPORTS: {
          DEVICE_REPORT_CLEANUP_ENABLE: 'If On, the setting switches on an automatic device reports storage cleanup. If Off, the automatic device reports storage cleanup is disabled.',
          DEVICE_REPORT_RETENTION_PERIOD: 'The setting specifies the number of days to keep device report entry in storage.',
          SERVER_REPORT_CLEANUPZZ_ENABLE: 'If On, the setting switches on an automatic server reports storage cleanup. If Off, the automatic server reports storage cleanup is disabled.',
          SERVER_REPORT_RETNETION_PERIOD: 'The setting specifies the number of days to keep server report entry in storage.'
        },
        STATISTICS: {
          CPU_COLLECTION_ENABLE: 'If Enabled, the license allows for the collection of CPU metrics. If Disabled, the license does not allow the collection of CPU metrics.',
          DISK_COLLECTION_ENABLE: 'If Enabled, the license allows collecting disk metrics. If Disabled, the license does not allow collecting disk metrics.',
          MEMORY_COLLECTION_ENABLE: 'If Enabled, the license allows collecting Memory metrics. If Disabled, the license does not allow collecting Memory metrics.',
          REPORT_ENABLE: 'If Enabled, the license allows reporting of statistical indicators. If Disabled, the license does not allow reporting of statistical indicators.',
          REPORT_PERIOD: 'The setting specifies the frequency to collect metrics.',
          PM_KPI_COLLECTION_RETENTION: 'The setting specifies the number of days to keep PM KPI DB Data.',
          PM_KPI_FILE_RETENTION: 'The setting specifies the number of days to keep PM KPI Files.'
        },
        LOGCONFIG: {
          DEVICE_GROUP_OPERATION_LOG_CLEANUP_ENABLE: 'If Enabled, the license allows automatic Device Group Operations data cleanup. If Disabled, the license does not allow automatic Device Group Operations data cleanup.',
          DEVICE_GROUP_OPERATION_LOG_RETENTION_PERIOD: 'The setting specifies the number of days to keep Group Operations data in logs. ',
          DEVICE_OPERATION_LOG_CLEANUP_ENABLE: 'If Enabled, the license allows automatic Device Operations data cleanup. If Disabled, the license does not allow automatic Device Operations data cleanup.',
          DEVICE_OPERATION_LOG_RETENTION_PERIOD: 'The setting specifies the number of days to keep Device Operations data in logs.',
          SESSION_LOG_CLEANUP_ENABLE: 'If Enabled, the license allows automatic event log cleanup. If Disabled, the license does not allow automatic event log cleanup.',
          SESSION_LOG_RETENTION_PERIOD: 'The setting specifies the number of days to keep session records in logs.',
          STATISTICS_LOG_CLEANUP_ENABLE: 'If Enabled, the license allows automatic statistical log cleaning. If Disabled, the license does not allow automatic statistical log cleaning.',
          STATISTICS_LOG_RENTENTION_PERIOD: 'The setting specifies the number of days to keep statistics logs.'
        },
        FAULTMANAGEMENT: {
          EVENT_ALARM_ACK_ENABLE: 'If On, the setting switches on an automatic ack on events. If Off, manual ack is required.',
          EVENT_ALARM_CLEANUP_ENABLE: 'If On, the setting switches on an automatic event log cleanup. If Off, the automatic event log cleanup is disabled.',
          EVENT_ALARM_EMAIL_ENABLE: 'If On, the setting switches on an automatic email notification on the occurrence of events. If Off, no alarm email notification.',
          EVENT_ALARM_RETENTION_PERIOD: 'The setting specifies the number of days to keep event logs.'
        }
      }
    },
    COMMON: {
      DEVICES: 'Devices',
      DEVICE: 'Device',
      CLIENTS: 'Clients',
      CLIENT: 'Client',
      USERS: 'Users',
      ALARMS: 'Alarms',
      TOTALALARMS: 'Total Alarms',
      HISTORYALARMS: 'History Alarms',
      CRITICALALARMS: 'Critical Alarms',
      MAJORALARMS: 'Major Alarms',
      WARNINGALARMS: 'Warning Alarms',
      MINORALARMS: 'Minor Alarms',
      PRODUCTS: 'Products',
      PRODUCTSDISTRIBUTION: 'Product distribution',
      ONLINEDEVICE: 'Online Devices',
      APPLY: 'Apply',
      DELETE: 'Delete',
      DELETEALL: 'Delete All',
      UPGRADE: 'Upgrade',
      CANCEL: 'Cancel',
      OK: 'OK',
      CLOSE: 'Close',
      ADD: 'Add',
      EDIT: 'Edit',
      Fail: 'Fail',
      SERIAL_NUMBER: 'Serial Number',
      PRODUCT_CLASS: 'Product Class',
      ACTION: 'Profiles',
      NEW: 'New',
      SELECTACTION: 'Select Operation',
      SELECTNOTIFICATION: 'Select Notification',
      IMPORT: 'Import',
      DOWNLOAD: 'Download',
      DOWNLOADLOG: 'Download Log',
      SAVE: 'Save',
      DONTSAVE: "Don't Save",
      UPLOAD: 'Upload',
      NAME: 'Name',
      ENTERNAME: 'Enter Name',
      VERSION: 'Version',
      PRIORITY: 'Priority',
      ENTERVERSION: 'Enter Version',
      SOFTVERSION: 'Software Version',
      TYPE: 'Type',
      SELECTTYPE: 'Select Type',
      PREVIOUS: 'Previous',
      NEXT: 'Next',
      USERNAME: 'Username',
      PASSWORD: 'Password',
      USERNAME1: 'User Name',
      PASSWORD1: 'User Password',
      ENTERUSERNAME: 'Enter Username',
      ENTERPASSWORD: 'Enter Password',
      UPDATE: 'Update',
      UNINSTALL: 'Uninstall',
      PARAMETERS: 'Parameters',
      PARAMNAME: 'Parameter Path',
      ENTERPARAMNAME: 'Enter Parameter Path',
      PARAMTYPE: 'Parameter Type',
      SELECTPARAMTYPE: 'Select Parameter Type',
      PARAMVALUE: 'Parameter Value',
      ENTERPARAMVALUE: 'Enter Parameter Value',
      ADDPARAM: 'Add Parameter',
      EXECUTE: 'Execute',
      SIZE: 'Size',
      CANCELALL: 'Cancel all',
      FIELDREQUIRED: 'This field is required!',
      DETAILS: 'Details',
      SELECTPRODUCTNAME: 'Select Product Name',
      SELECTPRODUCT: 'Select Product',
      AND: 'And',
      EDITPARAM: 'Edit Parameter',
      VALUE: 'Value',
      EXPANDCOLLROW: 'Expand/Collapse Row',
      PORT: 'Port',
      HOST: 'Host',
      THECUSTOMIZE: 'Theme Customizer',
      CUSTOMIZEREALTIME: 'Customize & Preview in Real Time',
      SKIN: 'Skin',
      LIGHT: 'Light',
      BORDERED: 'Bordered',
      DARK: 'Dark',
      RED: 'Red',
      BLUE: 'Blue',
      SEMIDARK: 'Semi Dark',
      ROUTETRA: 'Route Transition',
      FADEINLEFT: 'Fade In Left',
      ZOOMIN: 'Zoom In',
      FADEIN: 'Fade In',
      NONE: 'None',
      MENULAYOUT: 'Menu Layout',
      VERTICAL: 'Vertical',
      HORIZONTAL: 'Horizontal',
      MENUCOLL: 'Menu Collapsed',
      MENUHIDDEN: 'Menu Hidden',
      NAVBARCOLOR: 'Navbar Color',
      NAVBARTYPE: 'Navbar Type',
      MENYTYPE: 'Menu Type',
      FLOATING: 'Floating',
      STICKY: 'Sticky',
      STATIC: 'Static',
      FOOTERTYPE: 'Footer Type',
      WIDGETS: 'Customize Widgets',
      EDITMODE: 'Edit Widget Mode',
      CUSWIDGETS: 'Customizing Widgets',
      LOGOUT: 'Logout',
      RECENTNOTIF: 'Recent Notifications',
      NOTIFICATIONS: 'Notifications',
      READMORE: 'Read more',
      TOTAL: 'total',
      SELECTED: 'selected',
      CREATED: 'Created Time',
      SELECTCOLUMN: 'Select Column',
      ACTIVE: 'Active',
      ALLOW: 'Allow',
      YES: 'Yes',
      CLIENTLIST: 'Clients List',
      WIFICLIENTLIST: 'WiFi Client List',
      WIFICLIENTLIST_DESCRIPTION: 'List all WiFi clients included in this group.',
      WIFICLIENTLISTDESCRIPTION: 'Current WiFi status from associated clients on avaliable radios/bands of this device.',
      ONLINE: 'Online',
      OFFLINE: 'Offline',
      EXPORT: 'Export',
      MQTT: 'MQTT',
      CWMP: 'CWMP',
      NETCONF: 'NETCONF',
      CURRENTNODE: 'Current Node',
      CHILDNODE: 'Child Nodes',
      EDITSTAGE: 'Edit Stage Name',
      STAGENAME: 'Stage Name',
      ENTERSTAGENAME: 'Enter Stage Name',
      OPERATIONNAME: 'Operation Name',
      ADDOPERATION: 'Add Operation',
      ALLPROTOCOL: 'All Protocol',
      ALLPRODUCTS: 'All Products',
      ALLEVENT: 'All Event',
      USER: 'User',
      ALLFILETYPES: 'All File Types',
      TRANSMISSTIONTYPE: 'Transmission type',
      SELECTTRANSMISSTIONTYPE: 'Select Transmission type',
      REMOVEFROMGROUP: 'Remove From Group',
      SHUTDOWN: 'Shutdown',
      NOPERMISSION: 'Current Account is not authorized to read the content in this page.',
      SEPARATED_BY_SEMICOLONS: 'Separated by Semicolons',
      SEPARATED_BY_COMMAS: 'Separated by Commas',
      MAIL_SEPARATED_BY_SEMICOLONS: 'Separated by Semicolons (<EMAIL>;<EMAIL>;)',
      SN_SEPARATED_BY_COMMAS: 'Separated by Commas (sn1,sn2)',
      WIDGETNAME: 'Widget name,class or subclass',
      AUTHORITYNAME: 'Authority name,belonging class or description',
      LOCATE: 'Locate',
      RELOAD: "Reload",
      DATA: 'Data',
      STATE: 'State',
      REGISTER: 'Register',
      GROUP: 'Group',
      SELECTCHARTTYPE: 'Select Chart Type',
      OPEN_MAXIMIZE: "Open Maximize",
      SELECTORENTER: 'Please select or enter your option',
      INVALIDFILETYPE: 'Invalid file type. Please select one of the following types: ',
    },
    CONFIRM: {
      CONF: 'Confirm ',
      REMOVAL: ' Removal?',
      REBOOT: 'Reboot this AMP node may cause loss of data. Are you sure you want to reboot ',
      SHUTDOWN: 'Shutdown this AMP node may cause loss of data. Are you sure you want to Shutdown ',
      ADDFAIL: 'Add failed!',
      NAMEEXIST: 'name already exists!',
      ALARMNOTIF: 'Alarms Notification updated successfully',
      CONFREMGROUP: 'Confirm removal group?',
      CONFGROUP: 'Confirm Group Removal?',
      CONFGROUPS: 'Confirm Groups Removal?',
      DOGROUP: 'Do you want to remove ',
      FROMGROUP: ' from group?',
      IMPORTSUCCESS: 'Import Successfully!',
      IMPORTFAIL: 'Import Failed!',
      FILEEMPTY: 'The file is empty',
      NOTSUPPORT: 'Not support this file format',
      DODELETEGROUP: 'Do you want to delete the group ',
      DODELETEGROUPS: 'Do you want to delete these groups?',
      GROUPOPER: 'Device Group Actions!',
      WORKFLOWOPER: 'Device Workflow Actions!',
      WORKFLOWOPERATION: 'Device workflow operation ',
      WORKFLOWDOREMOVEALL: 'Do you want to remove all entries from the workflow operations log?',
      WORKFLOWCLEANSUCC: 'Device workflow operations log was cleaned successfully',
      WORKFLOWNOTCLEAN: 'Device workflow operations was not cleaned',
      SETGROUPOPERSUCC: 'Set Group Operation Success!',
      RENAMESUCC: 'Rename success',
      CONFNIT: 'Confirm Download Alarms Notification?',
      DODOWNLOADNIT: 'Do you want to download the alarms notification ',
      ALARMNIT: 'Notification',
      DOWNLOADSUCC: 'Download successfully!',
      PLESELECT: 'Please select alarms notifications first!',
      CONFDOWNLOADNIT: 'Confirm download the selected alarms notifications?',
      DODOWNLOADSELECT: 'Do you want to download the selected alarms notifications?',
      DOWNLOADSELECT: 'Download the selected alarms notifications successfully!',
      DOWANT: 'Do you want to ',
      THEALARMNIT: ' the alarms notification',
      SUCC: ' successfully!',
      CONFDELETENIT: 'Confirm Delete Alarms Notification?',
      DODELETENIT: 'Do you want to delete the alarms notification',
      NITDELSUCC: 'Alarms Notification deleted successfully',
      NITID: 'Alarms Notification (ID: ',
      WANDEL: ') was deleted!',
      NITDELETEFAIL: 'Alarms Notification delete failed',
      NOTDEL: ') was not deleted!',
      SELECTFIRST: 'Please select alarms notifications first!',
      STATEITEMS: 'The selected alarms notifications contain one or more active state items!',
      CONFSELECTNIT: 'Confirm Delete The Selected Alarms Notifications?',
      DOSELECTNIT: 'Do you want to delete the selected alarms notifications?',
      SELECTNITSUCC: 'Delete the selected alarms notifications successfully!',
      GROUPOPERATION: 'Device group operation ',
      CANCELSUCC: ' was canceled successfully',
      REMOVEDLOG: ' was removed from the log',
      CONFCLEANUP: 'Confirm Log Cleanup',
      DOREMOVEALL: 'Do you want to remove all entries from the group operations log?',
      GROUPCLEANSUCC: 'Device group operations log was cleaned successfully',
      GROUPNOTCLEAN: 'Device group operations was not cleaned',
      CONFPRODUCTREM: 'Confirm Product Removal?',
      CONFRANREM: 'Confirm Radio Access Network Removal?',
      CONFAPNREM: 'Confirm WiFi AP Network Removal?',
      CONFMESHREM: 'Confirm WiFi Mesh Network Removal?',
      DOPRODUCT: 'Do you want to delete the product ',
      DORAN: 'Do you want to delete the Radio Access Network ',
      DOAP: 'Do you want to delete the WiFi AP Network ',
      DOMESH: 'Do you want to delete the WiFi Mesh Network ',
      CONFPRODUCTBAN: 'Confirm Product Ban',
      CONFRANBAN: 'Confirm Radio Access Network Ban',
      CONFAPNBAN: 'Confirm WiFi AP Network ban',
      CONFMESHNBAN: 'Confirm WiFi Mesh Network ban',
      PRODUCTACCESS: '? Devices of this product will be unable to access server.',
      PRODUCTSACCESS: '? Devices of these products will be unable to access server.',
      RANSACCESS: '? Devices of this Radio Access Network will be unable to access server.',
      APSACCESS: '? Devices of this WiFi AP Network will be unable to access server.',
      MESHSACCESS: '? Devices of this WiFi Mesh Network will be unable to access server.',
      CONFFILE: 'Confirm File Removal?',
      DELFILESUCC: 'Delete file success',
      CONFSCRIPT: 'Confirm script removal?',
      DOSCRIPT: 'Do you want to delete the script ',

      CONFFLOW: 'Confirm Download Workflow?',
      WORKFLOW: 'Workflow',
      DOWORKFLOW: 'Do you want to download the workflow ',
      CONFSELECT: 'Confirm Download The Selected Workflows?',
      DOSELECTFLOW: 'Do you want to download the selected workflows as multi files?',
      DOSELECTFLOWASONE: 'Do you want to download the selected workflows as one file?',
      DOWNSELECTFLOW: 'Download the selected workflows successfully!',
      DOWNSELECTFILESUCCESS: 'Download the selected files successfully!',
      DELSELECTFLOW: 'Delete the selected workflows successfully!',
      PLEASEFLOWS: 'Please select workflows first!',
      THEFLOW: ' the workflow ',
      CONFDELFLOW: 'Confirm Delete Workflow?',
      DODELFLOW: 'Do you want to delete the workflow',
      DELSUCC: 'Workflow deleted successfully',
      FLOWID: 'Workflow (ID: ',
      FLOWDELFAIL: 'Workflow delete failed',
      FLOWITEM: 'The selected workflows contain one or more active state items',
      CONFSELECTFLOW: 'Confirm Delete The Selected Workflows?',
      DODELSELECTFLOW: 'Do you want to delete the selected workflows?',

      CONFCONFIGURATION: 'Confirm Download Configuration?',
      CONFIGURATION: 'Configuration',
      DOWORKCONFIGURATION: 'Do you want to download the configuration',
      CONFSELECTCONFIGURATION: 'Confirm Download The Selected Configurations?',
      DOSELECTCONFIGURATION: 'Do you want to download the selected configurations as multi files?',
      DOSELECTCONFIGURATIONASONE: 'Do you want to download the selected configurations as one file?',
      DOWNSELECTCONFIGURATION: 'Download the selected configurations successfully!',
      DELSELECTCONFIGURATION: 'Delete the selected configurations successfully!',
      PLEASECONFIGURATIONS: 'Please select configurations first!',
      THECONFIGURATION: 'the configuration',
      CONFDELCONFIGURATION: 'Confirm Delete Configuration?',
      DODELCONFIGURATION: 'Do you want to delete the configuration',
      DELCONFIGURATIONSUCC: 'Configuration deleted successfully',
      CONFIGURATIONID: 'Configuration (ID:',
      CONFIGURATIONDELFAIL: 'Configuration delete failed',
      CONFIGURATIONITEM: 'The selected configurations contain one or more active state items',
      CONFDELSELECTCONFIGURATION: 'Confirm Delete The Selected Configurations?',
      DODELSELECTCONFIGURATION: 'Do you want to delete the selected configurations?',

      CONFPOLICY: 'Confirm Download Policy?',
      DOWORKPOLICY: 'Do you want to download the policy',
      POLICYID: 'Policy (ID:',
      POLICYFLOWSUCC: 'Policy updated successfully',
      POLICY: 'Policy',
      POLICYCLONESUCCESS: 'Policy cloned successfully!',
      CONFDELPOLICY: 'Confirm Delete Policy?',
      DODELPOLICY: 'Do you want to delete the Policy',
      DELPOLICYSUCC: 'Policy deleted successfully',
      POLICYDELFAIL: 'Policy delete failed',
      PLEASEPOLICYS: 'Please select policys first!',
      DOSELECTPOLICY: 'Do you want to download the selected policys as multi files?',
      DOSELECTPOLICYSONE: 'Do you want to download the selected policys as one file?',
      CONFSELEPOLICY: 'Confirm Download The Selected policys?',
      DOWNSELECTPOLICY: 'Download the selected policys successfully!',
      POLICYITEM: 'The selected policys contain one or more active state items',
      CONFDELSELECTPOLICY: 'Confirm Delete The Selected Policys?',
      DODELSELECTPOLICY: 'Do you want to delete the selected policys?',
      DELSELECTPOLICY: 'Delete the selected policys successfully!',

      CONPROFILE: "Confirm Download Profile File?",
      PROFILE: "Profile file '",
      DOPROFILE: "Do you want to download the profile file",
      CONOSELECT: "Confirm Download The Selected Profiles?",
      DOSELECTPROFILE: "Do you want to download the selected work profile file as multiple files",
      DOSELECTPROFILEASONE: "Do you want to download the selected profiles as one file?",
      DOWNSELECTPROFILE: "Download the selected profiles successfully!",

      PROVISIONINGFILE: "Confirm Download File?",
      PROVISIONINGCONFILE: "Do you want to download the file",
      PROVISIONINGSELECT: "Confirm Download The Selected Files?",
      PROVISIONINGDOSELECTFILE: "Do you want to download the selected work file as multiple files",
      PROVISIONINGDOSELECTFILEONE: "Do you want to download the selected files as one file?",
      PROVISIONINGCONOSELECT: "Are you sure you want to download the selected file",

      ENDGREATER: 'The End Date should be greater than the current date!',
      STARTHAVEVALUE: 'The Start Date or End Date should be a value!',
      ENDGREATERSTART: 'The End Date should be greater than the Start Date!',
      STARTENDALUE: 'The Start Time or End Time should be a value!',
      EXACTLYEQUAL: 'Time cannot be exactly equal!',
      CONFSTAGE: 'Confirm Stage Removal?',
      BADREQ: 'Bad Request',
      PARAMNEED: 'Parameters need to be filled in before saving',
      FLOWSUCC: 'Workflow updated successfully',
      CONFIGURATIONSUCC: 'Configuration updated successfully',
      WASUPDATE: ') was updated!',
      CROSSCLICK: 'Cross click',
      FLOWADDSUCC: 'Workflow added successfully',
      WASADD: ') was added!',
      FORMFAIL: 'Form validation failed!',
      CONFOPER: 'Confirm Operation Removal?',
      VERSIONERROR: 'Version Error!',
      ONLY16: 'Only 1-16 characters of letters numbers and special symbols ( _ - .) allowed.',
      FORKSUCC: 'Workflow forked successfully!',
      WASFORK: ' was forked. New alarms notification: ',
      FLOWSPACE: 'Workflow ',
      OPERFORKSUCC: 'Action forked successfully!',
      OPERFLOWSPACE: 'Operation ',
      OPERATION: ' Operation',
      CONFDELOPER: 'Confirm Delete Profiles?',
      DODELOPER: 'Do you want to delete the profiles',
      DELSUCCESS: 'Delete successfully!',
      PLEOPERFIRST: 'Please select actions first!',
      CONFSELECTOPER: 'Confirm Delete The Selected Profiles?',
      DOSELOPER: 'Do you want to delete the selected profiles?',
      DELOPERSUCC: 'Delete the selected profiles successfully!',
      CONFACTIONRE: 'Confirm Operation Removal?',
      STAGE: ' stage ?',
      ACTION: ' Profiles?',
      OPERUPDATESUCC: 'Profiles updated successfully',
      OPERADDSUCC: 'Profiles added successfully',
      ALARMADDSUCC: 'Alarms Notification added successfully',
      CONFLICT: 'Conflict',
      ALARMNOTNAME: 'Alarms Notification with name ',
      ALREADYEXIST: ' already exists',
      CONTAINDATA: ' contains inconsistent data',
      NAMEERROR: 'Name error!',
      ONLYNAMESYM: 'Only 1-64 characters of letters, numbers, and special characters (_ - spaces) are allowed',
      CLONENOTI: 'Alarms Notification cloned successfully!',
      WANCLONE: ' was cloned. New alarms notification: ',
      CONFIGUPDATESUCC: 'Configuration updated successfully',
      SETOPERSUCC: 'Set Device Operation Success!',
      SETOPERFAIL: 'Set Device Operation Fail!',
      TASKSUCC: 'Task added successfully!',
      LABELSUCC: 'Edit Label Success!',
      LABELFAIL: 'Edit Label Fail!',
      UPDATEDEV: 'update success',
      CMDSENQUSUCC: 'Command enqueued successfully!',
      FORKNOT: 'Alarms Notification forked successfully!',
      WANIMPORT: ') was not imported.',
      NOTIIMPORTFAIL: 'Alarms Notification import failed',
      IMPORTSUCC: 'import successful!',
      GROUPCREATESUCC: 'Device group created successfully',
      IMPORTTOGROUP: ' devices are imported to group ',
      GNAMEEXIST: 'This group name already exists',
      SAVESRSSUCC: 'Save Summary Report Settings Successful',
      PLEASECONF: 'The topic cannot be modified after setting, please confirm',
      ADDPRODSUCC: 'Add Product Successful.',
      ADDPARAMSUCC: 'Add Parameter Successful.',
      UPDATEPRODSUCC: 'Update Product Successfully.',
      UPDATERANSUCC: 'Update Radio Access Network Successfully.',
      UPDATEAPNSUCC: 'Update WiFi AP Network Successfully.',
      UPDATEMESHSUCC: 'Update WiFi Mesh Network Successfully.',
      UPDATEPARAMSUCC: 'Update Parameter Successful.',
      PLEASEFILL: 'Please fill in the required items of star mark first!',
      UPDATEPERM: 'Update Permission Type Successful',
      UPDATEPERMDEV: 'Update Permission Devices Successful',
      ADDSUCC: 'add success',
      UPDATESUCC: 'update success',
      DONE: 'done',
      WARNING: 'Warning!',
      PARAMNAMEEXIST: 'Parameter name is already exists!',
      SCRITEMPTY: 'Script list is empty!',
      USPGROUPEMPTY: 'Group list is empty!',
      GROUPNAMEREQ: 'Group name is required',
      OPERMODEREQ: 'Operate mode is required',
      FLOWIMPORTFAIL: 'Workflow import failed',
      SYSSETSUCC: 'Save System Settings Successful!',
      SYSSETFAIL: 'Save System Settings Fail!',
      DEVSETSUCC: 'Save Device Settings Successful!',
      DEVSETFAIL: 'Save Device Settings Fail!',
      PROSETSUCC: 'Save Product Settings Successful!',
      PROSETFAIL: 'Save Product Settings Fail!',
      SYSPRESUCC: 'Save System Preference Successful!',
      SYSPREFAIL: 'Save System Preference Fail!',
      DELETEUSER: 'Delete user ',
      CHANGEUSER: 'Change user ',
      SPFAIL: ' failed',
      ACTIVESUSS: ' active status successfully',
      ACTIVEFAIL: ' active status failed',
      IMAGEOVERSIZE: 'The image is oversize.',
      PWDNOTSAME: 'Confirm password don\'t match.',
      PWDREQ: 'The password is required.',
      FORMATINCORRECT: 'The Expiration Date format is incorrect.',
      MUSTADMIN: 'The product of ADMIN/CSR User must be "ADMIN".',
      PRODUCTREQ: 'The product is required.',
      SELECTADMIN: 'Only ADMIN role can choose select "ADMIN".',
      ROLEREQ: 'The user role is required.',
      AVATARSUCC: 'The avatar is updated successfully.',
      AVATARFAIL: 'Failed to update the avatar.',
      EMAILSUCC: 'The email is updated successfully',
      EMAILFAIL: 'Failed to update the email.',
      UPPERLIMIT: 'The number of devices that can be added has exceeded the upper limit!',
      MAXLIMIT: 'Maximum device limit is ',
      NOTSAVETITLE: 'Do you want to save your changes?',
      NOTSAVECONTENT: 'The changes you made will not be saved.',
      CONFIRMROLE: 'Confirm Role Removal?',
      DOROLE: 'Do you want to delete the role?',
      DOSELECTROLE: 'Do you want to delete the selected role?',
      DELROLESUCC: 'Roles deleted successfully.',
      ROLEUPSUCC: 'Role updated successfully.',
      ROLEADDSUCC: 'Role added successfully.',
      CHANGEWILLBELOSE: "Your changes will be lost if you don't save them.",
      SYSTEMRETURNLOGIN: "The system is about to return to the login page",
      SAVEEVENTTIP: 'Please select inform event before saving in this stage!',
      SAVENOTIFYORDEVICEPARAMTIP: 'Please add notify parameter or device parameter condition before saving in this stage!',
      SAVEDEVICEFAULTTIP: 'Please add device fault parameter condition before saving in this stage!',
      ADDMEMBERSUC: 'Adding group members successfully!',
      ADDMEMBERFAIL: 'Adding group members failed!',
      SMTPHEALTHY: 'Test email success.',
      SNMPHEALTHY: 'Test SNMP Trap success.',
      XMPPHEALTHY: 'Test XMPP success.',
      GENSERVERREPORT: 'Generate server report successfully.',
      WORKFLOWCLONESUCCESS: 'Workflow cloned successfully!',
      CONFIGURATIONWCLONESUCCESS: 'Configuration cloned successfully!',
      HASBEENCLONED: 'has been cloned!',
      HASBEENFORKED: 'has been forked!',
      APNAMEEDITSUCC: 'Edit AP Name Success!',
      APNAMEEDITFAIL: 'Edit AP Name fail.',
      ADDAPNSUCC: 'Add WiFi AP Network Successful, and a group with the same name was created simultaneously.',
      ADDRANSUCC: 'Add Radio Access Network Successful, and a group with the same name was created simultaneously.',
      ADDMESHSUCC: 'Add WiFi Mesh Network Successful, and a group with the same name was created simultaneously.',
      TAGERROR: 'Only 0-32 characters of letters,numbers,-,_,. and spaces are allowed'
    },
    PM: {
      PMWORD: 'PM',
      PMPARAM: 'PM Parameter',
      PMCHART: 'PM Chart',
      PERFORMANCEREPORT: 'Performance Report',
      PMSTATEITEMS: 'The selected Metric IDs contain one or more tracking items!',
      PERFORMANCEREPORT_DESCRIPTION: 'List generated Server(AMP) Reports with detailed information such as Device’s and Service’s status.',
      PMSTATISTICS: 'PM Statistics',
      SERIALNUMBER: 'Serial Number',
      TARGETSERIALNUMBER: 'Target Serial Number',
      TARGETSN: 'Target SN',
      TARGETGROUP: 'Target Group',
      TARGET: 'Target',
      GROUP: 'Group',
      PARAMNAME: 'Parameter Name',
      PARAMPATH: 'Parameter Path',
      CONDITION: 'Condition',
      CONDITIONS: 'Conditions',
      PARAMVALUE: 'Parameter Value',
      FROM: 'From',
      TO: 'To',
      CREATEDBY: 'Created By',
      BEGINTIME: 'Begin Time',
      ENDTIME: 'End Time',
      TIME: 'Time',
      UPDATETIME: 'Update Time',
      MODELNAME: 'Model Name',
      PRODUCTCLASS: 'Product Class',
      TIMERANGE: 'Time Range',
      OUI: 'OUI',
      METRICRULE: 'Metric Rule',
      ALL: 'All',
      CONFIRM_DELETE: 'Confirm Delete',
      DO_DELETE: 'Do you want to delete selected ',
      DODELETE: 'Do you want to delete ',
      DELETESUCCESS: 'Delete Success',
      DELETEFAIL: 'Delete Fail',
      PLESESELECT: 'Please select ',
      CONFIRM_REFRESH: 'Confirm Re-Search',
      DO_REFRESHSELECT: 'Do you want to Re-Search selected ',
      DO_REFRESH: 'Do you want to Re-Search ',
      REFRESHSUCCESS: 'Re-Search Success',
      REFRESHFAIL: 'Re-Search Fail',
      EXPIREDUPDATESUCCESS: "All expired data has been successfully updated.",
      DATAEXPIRED: 'Data Expired',
      ADDCONDITION: 'Add Condition',
      DELETECONDITION: 'Delete Condition',
      SEARCH: 'Search',
      REFRESH: 'Refresh',
      REFRESHALL: 'Refresh All',
      SEARCHRESULT: 'Search Result',
      VIEWCHART: 'View Chart',
      CHART: 'Chart',
      SAVERULE: 'Save Rule Name',
      UPDATERULE: 'Update Rule Name',
      NAME: 'Name',
      DESCRIPTION: 'Description',
      CLOSE: 'Close',
      SAVE: 'Save',
      DELETE: 'Delete',
      DELETEALL: 'Delete All',
      GOTDEVICEINFO: 'Navigate to the device info page for the ',
      VIEWALLCHART: "Please select devices to view performance charts. (max: 20)",
      DURATION: 'Duration',
      NOTIFICATIONTOOLTIP: 'Convert Into Notification',
      REFRESHRULETOOLTIP: 'Re-Search Rule',
      DEVICECOUNT: 'Devices',
      FAIL: 'Fail!',
      REFRESHLOADING: 'Re-Search loading ...',
      DOWNLOAD: 'Download',
      CONFIRM_DOWNLOAD: 'Confirm Download',
      DOWNLOADSUCCESS: 'Download Success',
      DOWNLOADFAIL: 'Download Fail',
      DO_DOWNLOAD: 'Do you want to download selected Devices?',
      DODOWNLOAD: 'Do you want to download ',
      OPENSEARCHBAR: 'Open Search Bar',
      HIDESEARCHBAR: 'Hide Search Bar',
      LASTMACHINGTIME: 'Last Matching Time',
      RESEARCH: 'Re-Search',
      RESEARCHRULETOOLTIP: 'Re-Search Rule',
      TRACKING: 'Tracking',
      RESULT: 'Result',
      DEVICE_RESULT: 'Device #',
      RESEARCHALL: 'Re-Search All',
      RESULTTOOLTIP: 'Number of devices meeting the condition',
      LASTMACHING: 'Last Matching',
    },
    REFURBISHMENT: {
      REFURBISHMENTSTATISTICS: 'Refurbishment statistics',
      REFURBISHMENTTIME: 'Refurbishment Time',
      REFURBISHMENTCOUNT: 'Refurbished Count',
      INSTALLATIONTIME: 'Installation Time',
    },
    CARE: {
      TITLE: 'care',
      GENERALINFO: 'General Info',
      GENERALINFODESCRIPTION: 'Device\'s general information.',
      MAP: 'Location',
      MAPDESCRIPTION: 'Device\'s location on Google Map.',
      WIFICLIENTLIST: 'WiFi Client List',
      WIFICLIENTLISTDESCRIPTION: 'WiFi clients connect to the device.',
      ERRORSTATUS: 'Errors Status',
      ERRORSTATUSDESCRIPTION: 'Error list of the device including event code,error description and error time.',
      ERRORSTCARE: "Obtain Device",
      SELECTDEVICE: "Select device",
      SERIALNUMBER: "serialNumber",
      PRODUCTNAME: "productName",
    },
    FIVEGC: {
      CELL_CONNECTED: 'Cell Connected',
      CELL_CONNECTED_DESCRIPTION: 'Displays the number of connected and functioning radios.',
      CELL_DISCONNECTED: 'Cell Disconnected',
      CELL_DISCONNECTED_DESCRIPTION: 'Shows the number of disconnected radios.',
      ACTIVE_UE: 'Active UE',
      ACTIVE_UE_DESCRIPTION: 'The number of cells with active UE.',
      NO_ACTIVE_UE: 'Inactive UE',
      NO_ACTIVE_UE_DESCRIPTION: 'Indicates the number of cells with no activity. This does not necessarily mean that there are no UE attached; they could be attached but not active.',
      CELLS_WITHOUT_ATTACHED_UE: 'Cells without Attached UE',
      CELLS_WITHOUT_ATTACHED_UE_DESCRIPTION: 'Indicates the number of radios that are connected but do not have UE attached.',
      CELLS_WITH_ACTIVE_UE: 'Cells with Active UE',
      CELLS_WITH_ACTIVE_UE_DESCRIPTION: 'This widget displays a set of activity indicators in a bar chart format, showing the activity status of cells across different ranges, including no activity, 1 to 5 active UE, 6 to 10 active UE, 11 to 20 active UE, and 20 or more active UE.',
      CELLS_LIST: 'Cells List',
      CELLS_LIST_DESCRIPTION: 'This widget displays a list of cells connected to the 5G Core.',
      ALARM_LIST: 'Alarm List',
      ALARM_LIST_DESCRIPTION: 'The Alarms List provides information on the various alarms in the system.',
      FIVECORENETWORK: '5G Core Network',
      FIVECORENETWORK_DESCRIPTION: 'This widget serves as a link to the 5G Core, configured in the System/Settings with the 5G Core URL.',
      CELL_THROUGHPUT: 'Cell Throughput',
      CELL_THROUGHPUT_DESCRIPTION: 'This widget displays the current download and upload throughput of the cell.',
      UE_THROUGHPUT_BY_CELL: 'UE Throughput by Cell',
      UE_THROUGHPUT_BY_CELL_DESCRIPTION: 'UE Throughput by Cell displays the total download and upload throughput of all UEs connected to a specific cell, providing an overview of its data transmission performance.',
      UE_LIST: 'UE List',
      UE_LIST_DESCRIPTION: 'The UE Info provides detacdiled information about the User Equipment (UE) connected to the 5G Core network.',
      UE_5QI_PACKET: 'UE 5QI Packet',
      UE_5QI_PACKET_DESCRIPTION: 'This widget displays the 5QI packets of the UE, including various traffic metrics and drop rates for uplink and downlink.',
      ACTIVE: 'Active',
      INACTIVE: 'Inactive',
      STATUS: 'Status',
      NAME: 'Name',
      STATE: 'State',
      gNBID: 'gNB ID',
      BAND: 'Band',
      SESSIONS: 'Sessions',
      DATA: 'Data',
      PLMN: 'PLMN',
      TAC: 'TAC',
      IP: 'IP',
      SEVERITY: 'Severity',
      ALARM_ID: 'Alarm ID',
      EVENT_TYPE: 'Event Type',
      EVENT_TIME: 'Event Time',
      PROBABLE_CAUSE: 'Probable Cause',
      SPECIFIC_PROBLEM: 'Specific Problem',
      OBJ_CLASS: 'Object Class',
      ADD_TEXT: 'Add Text',
      THROUGHPUT_HISTORY: 'Throughput History',
      STATUS_HISTORY: 'Status History',
      HISTORY: 'History',
      IMSI: 'IMSI',
      PHONENUMBER: 'Phone Number',
      IMEI: 'IMEI',
      SUB_TYPE: 'Sub Type',
      REG_TYPE: 'Rregistration Type',
      LOCAL_ATTACHMENT: 'Local Attachment',
      LAST_ACTIVITY_TIME: 'Last Activity Time',
      REGISTRATION_TIME: 'Registration Time',
      DEREGISTRATION_TIME: 'Deregistration Time',
      UL_THROUGHPUT: 'UL Throughput',
      DL_THROUGHPUT: 'DL Throughput',
      SUPI: 'SUPI',
      FIVEQI: '5QI',
      UL_INGRESS: 'UL Ingress',
      UL_EGRESS: 'UL Egress',
      UL_DROPPED: 'UL Dropped',
      UL_TOTAL_INGRESS: 'UL Total Ingress',
      UL_TOTAL_EGRESS: 'UL Total Egress',
      UL_TOTAL_DROPPED: 'UL Total Dropped',
      DL_INGRESS: 'DL Ingress',
      DL_EGRESS: 'DL Egress',
      DL_DROPPED: 'DL Dropped',
      DL_TOTAL_INGRESS: 'DL Total Ingress',
      DL_TOTAL_EGRESS: 'DL Total Egress',
      DL_TOTAL_DROPPED: 'DL Total Dropped',
      ALARM: 'Alarm',
      ALARM_DESCRIPTION: 'The latest alarms from the 5G Core network in real time. It includes alarm severity, timestamp, and a brief description, allowing quick identification and response to network issues.',
      ue_activity: 'UE Activity of 5G Core',
      ue_activity_DESCRIPTION: 'This pie chart illustrates the User Equipment (UE) activity status.',
      ue_presence: 'UE Presence of 5G Core',
      ue_presence_DESCRIPTION: 'Shows the number of UE that are attached (connected) and detached (disconnected).',
      cells_info: 'Cells of 5G Core',
      cells_info_DESCRIPTION: 'The Cells of 5G Core provide detailed information about the cells connected to the 5G core network.',
      ACTIVITY_DESCRIPTION: {
        ACTIVE: 'The active segment shows the number of attached UE that are active.',
        DATA: 'The data segment shows the number of data sessions in the system. A UE may have more than one data session.',
        CALLS: 'The calls segment shows the number of attached UE who are currently in a call.',
        INACTIVE: 'The inactive segment shows the number of attached UEs who are not active.'
      },
      PRESENCE_DESCRIPTION: {
        ATTACHED: 'The "Attached" state indicates that the user device has successfully connected to the network.',
        DETACHED: 'The "Detached" state indicates that the user device has disconnected from the network or has not yet connected to it.'
      },
      LICENSE_DESCRIPTION: {
        UE: 'Shows the number of UE license seats in use and the number of UE license seats available.',
        NETWORK: 'Shows the number of PDN license seats and the number of PDN license seats available.',
        CELLS_4G: 'The number of 4G cell license seats in use and the total number of 4G cell license seats available.',
        CELLS_5G: 'The number of 5G cell license seats in use and the total number of 5G cell license seats available.',
      },
      ACTIVITY_CHART_DESCRIPTION: {
        ACTIVE_20PLUS: 'Indicates the number of cells with 20 or more active UE.',
        ACTIVE_11To20: 'Indicates the number of cells with 11 to 20 active UE.',
        ACTIVE_6To10: 'Indicates the number of cells with 6 to 10 active UE.',
        ACTIVE_1To5: 'Indicates the number of cells with 1 to 5 active UE.',
        NOACTIVE: 'Indicates the number of cells with no activity. This does not necessarily mean that there are no UE attached; they could be attached but not active.'
      },
      ACTION: {
        RESTARTSYSTEM: 'Restart System',
        DORESTARTSYSTEM: 'If you proceed ALL SERVICES WILL BE LOST MOMENTARILY!',
        RESTARTSYSTEM_SUCESS: 'Restart System Successfully!',
        RESTARTSYSTEM_FAIL: 'Restart System Fail!',
        BACKUPCONFIGURATION: 'Backup Configuration',
        BACKUPCONFIGURATION_SUCESS: 'Backup Configuration Successfully!',
        BACKUPCONFIGURATION_FAIL: 'Backup Configuration Fail!',
        RESTORECONFIGURATION: 'Restore Configuration',
        RESTORECONFIGURATION_SUCESS: 'Restore Configuration Successfully!',
        RESTORECONFIGURATION_FAIL: 'Restore Configuration Fail!',
        FACTORYRESET: 'Factory Reset',
        DOFACTORYRESET: 'You are about to reset your configuration to factory defaults!',
        FACTORYRESET_SUCESS: 'Factory Reset Successfully!',
        FACTORYRESET_FAIL: 'Factory Reset Fail!',
        REFRESHALL: 'Refresh All',
        REFRESHALL_SUCESS: 'Refresh All Successfully!',
        REFRESHALL_FAIL: 'Refresh All Fail!',
        SYSTEMMANAGEMENT: 'System Management',
      },
    },
    POWER: {
      ENERGYSAVING: 'Energy Management',
      STARTTIME_MUST_BE_EARLIER: "Start time must be earlier than end time.",
      STARTDATE_MUST_BE_EARLIER: "Start date cannot be later than end date.",
      POWER_CONSUMPTION_SUMMARY: "Power Consumption Summary",
      REAL_AVG_ENERGY: "Real Avg. Energy",
      NORMAL_STATE_ENERGY: "Normal State Energy",
      ENERGY_CONSUMPTION_BY_POLICY: "Energy Consumption by Policy",
      POWER_CONSUMPTION: "Power Consumption",
      TX_POWER: "Tx Power",
      NETWORK_USAGE: "Network Usage",
      UPLOAD: "Upload",
      DOWNLOAD: "Download",
      UE_COUNT: "UE Count",
      LOCATION: "Location",
      CURRENT_POLICY: "Current Policy",
      POLICY_SETTING: "Policy Setting",
      ENERGY_POLICY: "Energy Policy",
      MILD_SLEEP_DESCRIPTION: "Reduces transmit power while keeping the device fully operational.",
      MODERATE_SLEEP_DESCRIPTION: "Reduces power and temporarily disables the radio to save more energy.",
      WAKEABLE_DEEPSLEEP_DESCRIPTION: "Gradually powers down the device, but allows it to auto-wake when needed.",
      DEEPSLEEP_DESCRIPTION: "Fully powers down the device. Manual wake-up is required to resume operation.",
      SCHEDULE_SETTING: "Schedule Setting",
      POLICY_LIST: "Policy List",
      DURATION: "Duration",
      ENERGY: "Energy",
      POWER_CONSUMPTION_PER_DEVICE: "Power Consumption per Device",
      DL_PRB_LOADING: "DL PRB Loading",
      UL_PRB_LOADING: "UL PRB Loading",
      TOTAL_POWER_CONSUMPTION: "Total Power Consumption",
      NAME: "Name",
      SCHEDULE: "Schedule",
      CONDITION: "Condition",
      ACTIVE: "Active",
      NO_POLICIES_FOUND: "No policies found.",
      NO_POLICY_CONFIGURED: "This device has no energy saving policy configured.",
      ENABLE_TO_ADD_POLICIES: "You can add policies once Energy Saving control is enabled.",
      ENERGY_SAVING_NOT_ENABLED: "Energy Saving is not enabled.",
      NO_POLICY_SETTINGS_AVAILABLE: "No policy settings are available for this device.",
      ENABLE_TO_CONFIGURE_POLICY: "Please enable Energy Saving control to configure or view policy details.",
      ENERGY_MODE: "Energy Mode",
      TRAFFIC_LOADING: "Traffic Loading",
      UE_CONTEXT: "UE Context",
      POLICY_DISABLE_TITLE: "Disable Power Saving Mode?",
      POLICY_DISABLE_TEXT: "After disabling, the device will return to normal power settings. Are you sure you want to turn it off?",
    }
  }
}
