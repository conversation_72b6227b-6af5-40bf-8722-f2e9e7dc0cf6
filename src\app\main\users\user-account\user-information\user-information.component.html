<core-card
  [actions]="[]"
  [componentId]="accessor.componentId">
  <h4 class="card-title">{{ accessor.name | translate }}</h4>
  <div
    class="card-body"
    [perfectScrollbar]>
    <section class="app-user-view">
      <div class="d-flex flex-wrap">
        <div class="d-flex flex-column justify-content-between border-container-lg"
          [ngStyle]="{'width': getColumnWidth()}"
          #profileContent>
          <div class="user-avatar-section">
            <div class="d-flex justify-content-start">
              <div *ngIf="data.avatar&&data.avatar.length > 0; else customAvatar">
                <img
                  class="img-fluid rounded"
                  [src]="data.avatar"
                  (error)="data.avatar = null"
                  height="104"
                  width="104"
                  style="max-height: 104px;"
                  alt>
              </div>
              <ng-template #customAvatar>
                <div class="mr-1 ml-0">
                  <div class="rounded p-3 bg-light-success">
                    <h2 class="m-0 text-success">{{ data.userName | initials }}</h2>
                  </div>
                </div>
              </ng-template>
              <div class="d-flex flex-column ml-1">
                <div class="user-info mb-1">
                  <h4 class="mb-0">{{ data.userName }}</h4>
                  <!-- <span class="card-text">{{ data.userName }}</span> -->
                </div>
                <div class="d-flex flex-column ">
                  <label class="btn btn-primary p-1 py-50 mr-1 mb-50 d-flex flex-nowrap "  for="change-picture" style="width: 100px;">
                    <i class="mr-50 " data-feather="image"></i>
                    <span>{{ 'USERS.CHANGE' | translate }}</span>
                    <input
                      class="form-control"
                      type="file"
                      id="change-picture"
                      hidden
                      [accept]="allowedFileTypes"
                      (change)="uploadImage($event)">
                  </label>
                  <button
                  class="btn btn-outline-danger px-1 py-50 "
                  rippleEffect
                  (click)="deleteAvatar()"
                  style="width: 100px;">
                    <i class="mr-50 " data-feather="trash-2"></i>
                    <span >{{ 'COMMON.DELETE' | translate }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="" [ngStyle]="{'width': getColumnWidth(),'margin-top': getColumnWidth() == '100%' ? '1rem' : '0'}">
          <!-- <div class="user-info-wrapper">
            <div class="d-flex flex-wrap my-50">
              <div class="user-info-title">
                <i
                  data-feather="user"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'COMMON.USERNAME1' | translate }}</span>
              </div>
              <p class="card-text mb-0">{{ data.userName }}</p>
            </div>
            <div class="d-flex flex-wrap my-50">
              <div class="user-info-title">
                <i
                  data-feather="check"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'COMMON.PASSWORD' | translate }}</span>
              </div>
              <button
                (click)="openChangePasswordModal(modalChangePassword)"
                type="button"
                class="btn icon-btn btn-sm hide-arrow"
                style="padding: 0px !important;"
                rippleEffect>
                <span
                  class="text-primary"
                  [data-feather]="'edit-3'"></span>
              </button>
            </div>
            <div class="d-flex flex-wrap my-50">
              <div class="user-info-title">
                <i
                  data-feather="star"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'USERS.ROLE' | translate }}</span>
              </div>
              <p class="card-text mb-0">{{ data.role }}</p>
            </div>
            <div class="d-flex flex-wrap my-50">
              <div class="user-info-title">
                <i
                  data-feather="shopping-bag"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'PROVISIONING.PRODUCT' | translate }}</span>
              </div>
              <div class="card-text mb-0 card-text-custom">
                <app-beautify-content
                  [content]="data.productName?.join(',') ?? ''"
                  [placement]="'bottom'"></app-beautify-content>
              </div>
            </div>
          </div> -->
          <div class="d-flex h-100 flex-column" style="flex: 1;">
            <!-- Username -->
            <div class="media-body">
              <div class="transaction-title d-flex flex-wrap ">
                <div class="col-6 px-0 text-truncate" ngbTooltip="{{ 'COMMON.USERNAME1' | translate }}" container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <i data-feather="user" class="mr-50"></i>
                    <span>{{ 'COMMON.USERNAME1' | translate}}</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                  <app-beautify-content [content]="data.userName" [placement]="'left'"
                    [textClass]=" ''"></app-beautify-content>
                </div>
              </div>
            </div>
            <!-- Password -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">
                <div class="col-6 px-0 text-truncate" 
                ngbTooltip="{{ 'COMMON.PASSWORD' | translate }}" 
                container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <i data-feather="key" class="mr-50"></i>
                    <span>{{ 'COMMON.PASSWORD' | translate}}</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                  <button 
                  (click)="openChangePasswordModal(modalChangePassword)" 
                  type="button"
                  class="btn icon-btn btn-sm hide-arrow"
                  style="padding: 0px !important;" rippleEffect>
                  <span
                   class="text-primary"
                    [data-feather]="'edit-3'"></span>
                </button>
                </div>
              </div>
            </div>
            <!-- Role -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">
                <div class="col-6 px-0 text-truncate" 
                ngbTooltip="{{ 'USERS.ROLE' | translate }}" 
                container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <i data-feather="user-check" class="mr-50"></i>
                    <span>{{ 'USERS.ROLE' | translate }}</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                  <app-beautify-content [content]="data.role" [placement]="'top'"
                    [textClass]=" ''"></app-beautify-content>
                </div>
              </div>
            </div>
            <!-- Product -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">

                <div class="col-6 px-0 text-truncate" 
                ngbTooltip="{{ 'USERS.ROLE' | translate }}" 
                container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <i data-feather="hard-drive" class="mr-50"></i>
                    <span>{{ 'PROVISIONING.PRODUCT' | translate}}</span>
                  </span>
                </div>
                <div class="col-6 px-0 align-items-center text-truncate">
                  <app-beautify-content  [content]="data.productName" [type]="'list'"
                  [textClass]="'badge-light-info'"></app-beautify-content>
                </div>
            </div>
            </div>
            <!-- Email -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">
                <div class="col-6 px-0" style="">
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <i data-feather="mail" class="mr-50"></i>
                    <span>{{ 'USERS.EMAIL' | translate }}</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                  <app-input-editable
                  [mode]="'text'"
                  [currentTargetData]="emailData"
                  [customerStatusName]="'isEditValue'"
                  [customerAttributeName]="'email'"
                  [customerWidth]="'100%'"
                  [invalidField]="emailData.email"
                  (postDataToParentComponent)="saveEmail($event)">
                </app-input-editable>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div 
        [ngStyle]="{'width': getColumnWidth(),'margin-top': getColumnWidth() == '50%' ? '1rem' : '0'}">
          <!-- <div class="user-info-wrapper">
            <div class="d-flex flex-wrap my-50">
              <div
                class="user-info-title"
                style="width: 15rem">
                <i
                  data-feather="mail"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'USERS.EMAIL' | translate }}</span>
              </div>
              <app-input-editable
                [mode]="'text'"
                [currentTargetData]="emailData"
                [customerStatusName]="'isEditValue'"
                [customerAttributeName]="'email'"
                [customerWidth]="'100%'"
                [invalidField]="emailData.email"
                (postDataToParentComponent)="saveEmail($event)">
              </app-input-editable>
            </div>
            <div class="d-flex flex-wrap my-50">
              <div
                class="user-info-title"
                style="width: 15rem">
                <i
                  data-feather="link-2"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'USERS.PREVIOUSTIME' | translate }}</span>
              </div>
              <p class="card-text mb-0">
                {{ data.previousSignInTime | date:'MM/dd/yy, HH:mm:ss' }}
              </p>
            </div>
            <div class="d-flex flex-wrap my-50">
              <div
                class="user-info-title"
                style="width: 15rem">
                <i
                  data-feather="map-pin"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">{{ 'USERS.PREVIOUSLOCATION' | translate }}</span>
              </div>
              <p class="card-text mb-0">{{ data.previousSignInLocation }}</p>
            </div>
            <div class="d-flex flex-wrap my-50">
              <div
                class="user-info-title"
                style="width: 15rem">
                <i
                  data-feather="map-pin"
                  class="mr-1"></i>
                <span class="card-text user-info-title font-weight-bold mb-0">MFA</span>
              </div>
              <div
                class="custom-control custom-control-primary custom-switch"
                style="min-height: 1.7rem;">
                <input
                  name="mfaEnabled"
                  type="checkbox"
                  class="custom-control-input"
                  id="mfaEnabled">
                <label
                  class="custom-control-label"
                  for="mfaEnabled"></label>
              </div>
            </div>
          </div> -->
          <div
            class="d-flex h-100 flex-column"
            style="flex: 1;">
            <!-- Current sign location -->
            <div class="media-body d-flex flex-wrap">
             <div class="transaction-title text-truncate d-flex w-100">
              <div class="col-6 px-0 text-truncate" ngbTooltip="{{ 'USERS.CURRENTLOCATION' | translate }}" container="body" >
                <span class="card-text user-info-title font-weight-bold mb-0">
                  <i data-feather="map-pin" class="mr-50"></i>
                  <span>{{ 'USERS.CURRENTLOCATION' | translate }}</span>
                </span>
              </div>
              <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                <app-beautify-content [content]="data.currentSignInLocation" [placement]="'top'"
                  [textClass]=" ''"></app-beautify-content>
              </div>
              </div>
            </div>
            <!-- Previous sign location -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">
                <div class="col-6 px-0 text-truncate"
                ngbTooltip="{{ 'USERS.LASTLOGINLOCATION' | translate }}" 
                container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <i data-feather="map-pin" class="mr-50"></i>
                    <span>{{ 'USERS.LASTLOGINLOCATION' | translate }}</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                  <app-beautify-content [content]="data.previousSignInLocation" [placement]="'top'"
                    [textClass]=" ''"></app-beautify-content>
                </div>
              </div> 
            </div>
            <!-- Previous sign time -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">
                <div class="col-6 px-0 text-truncate" 
                ngbTooltip="{{ 'USERS.PREVIOUSTIME' | translate }}" 
                container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <svg width="14px" height="14px" class="mr-50">
                      <use href="./../assets/fonts/added-icon.svg#user-sign-in-time"></use>
                    </svg>
                    <span>{{ 'USERS.PREVIOUSTIME' | translate }}</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate">
                  <app-beautify-content [content]="data.currentSignInTime | date:'MM/dd/yy, HH:mm:ss'" [placement]="'top'"
                    [textClass]=" ''"></app-beautify-content>
                </div>
              </div>
            </div>
            <!-- MFA -->
            <div class="media-body d-flex flex-wrap">
              <div class="transaction-title text-truncate d-flex w-100">
                <div class="col-6 px-0 text-truncate" 
                ngbTooltip="{{ 'USERS.PREVIOUSLOCATION' | translate }}" 
                container="body" >
                  <span class="card-text user-info-title font-weight-bold mb-0">
                    <svg width="14px" height="14px" class="mr-50">
                      <use href="./../assets/fonts/added-icon.svg#user-mfa"></use>
                    </svg>
                    <span>MFA</span>
                  </span>
                </div>
                <div class="col-6 pl-50 pr-0 align-items-center text-truncate custom-control custom-control-primary custom-switch"
                style="min-height: 1.7rem;">
                  <input
                  [(ngModel)]="mfaData.mfaEnabled"
                  (change)="mfaEnabledChange($event);"
                  name="mfaEnabled"
                  type="checkbox"
                  class="custom-control-input"
                  id="mfaEnabled">
                <label
                  class="custom-control-label"
                  for="mfaEnabled"></label>
              </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</core-card>

<!-- Change Password Sidebar -->
<!-- Model -->
<ng-template
  #modalChangePassword
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      {{'USERS.CHANGEPASSWORD'| translate}}
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">×</span>
    </button>
  </div>
  <div
    class="modal-body"
    tabindex="0"
    ngbAutofocus>
    <app-change-password
      (dismissEvt)="modal.dismiss('Cross click')"
      [username]="data.userName"
      [modal]="modal"></app-change-password>
  </div>
</ng-template>
<!-- / Change Password Sidebar -->


<ng-template
  #modalOTPurlInformation
  let-modal>
  <div class="modal-header">
    <h4
      class="modal-title"
      id="myModalLabel1">
      Setup MFA Verify
    </h4>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss();mfaData.mfaEnabled = false"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body modalOTPurl"
    tabindex="0"
    ngbAutofocus>
    <div
      *ngIf="currentQRStep == 1"
      class="d-flex"
      style="flex-direction: column;">
      <div class="col-lg-12 d-flex justify-content-center align-items-center">
        <img
          style="background-color: #eee;"
          [src]="otpUrl"
          class="m-1"
          height="130"
          width="130">
      </div>
      <div
        class="col-lg-12 d-flex justify-content-start p-0"
        style="flex-direction: column;">
        <ngb-alert
          [type]="'success'"
          [dismissible]="false">
          <div
            class="alert-body"
            style="font-size: 12px;">
            <!-- <p> -->
            Setup MFA Verify, Lauch FreeOTP on your mobile device
            <!-- </p> -->
          </div>
        </ngb-alert>
        <!-- <p class="text-secondary">Scan the QR code to continue.</p> -->
      </div>
    </div>
    <div
      class="d-flex"
      *ngIf="currentQRStep == 2">
      <div class="col-md-12 p-0">
        <label>Enter your MFA Verify passcode</label>
        <div class="list input-container">
          <input
            #qrcodeInput
            type="text"
            trim
            name="qrcode"
            [(ngModel)]="enterCodeString"
            class="form-control"
            placeholder="Enter Code"
            maxlength="6"
            (keydown)="isNumberKey($event)"
            autofocus
            autocomplete="off">
        </div>
      </div>
    </div>
    <ngb-alert
      *ngIf="error && currentQRStep == 2"
      [type]="'danger'"
      [dismissible]="false">
      <div
        class="alert-body"
        style="font-size: 12px;">
        {{error}}
      </div>
    </ngb-alert>
  </div>
  <div class="modal-footer">
    <button
      *ngIf="currentQRStep == 1"
      class="btn btn-primary btn-block"
      (click)="next()"
      rippleEffect>
      <!-- <span
      class="spinner-border spinner-border-sm mr-1"></span> -->
      Scan the QR code to continue.
    </button>
    <button
      *ngIf="currentQRStep == 2"
      class="btn btn-primary"
      (click)="currentQRStep = 1"
      rippleEffect>
      <!-- <span
      class="spinner-border spinner-border-sm mr-1"></span> -->
      Back to Scan
    </button>
    <button
      *ngIf="currentQRStep == 2"
      class="btn btn-primary"
      (click)="verify(modal)"
      [disabled]="verifyDsiable()"
      rippleEffect>
      <!-- <span
      class="spinner-border spinner-border-sm mr-1"></span> -->
      Verify
    </button>
  </div>
</ng-template>
