export const locale = {
  lang: 'de',
  data: {
    MENU: {
      HOME: 'Startseite',
      SAMPLE: 'Beispiel'
    },
    DASHBOARD: {
      TITLE: 'Dashboard',
      AVGSESSIONS: {
        TITLE: 'Durchschnittliche Sitzungen',
        VIEWDETAILS: 'Details anzeigen',
        LAST1DAY: 'Letzte 24 Stunden',
        LAST7DAYS: 'Letzte 7 Tage',
        LAST15DAYS: 'Letzte 15 Tage',
        LAST28DAYS: 'Letzte 28 Tage',
        LAST30DAYS: 'Letzte 30 Tage',
        LASTMONTH: 'Letzter Monat',
        LASTYEAR: 'Letz<PERSON> Jahr',
      },
      MAP: 'Standort',
      SESSIONDURATION: 'Sitzungsdauer',
      SESSIONDURATIONDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Sitzungsdauer für alle Geräte in regelmäßigen Abständen. Sitzungsdauer: Die gesamte in einer Sitzung zwischen dem Gerät und AMP verbrachte Zeit.',
      SESSIONRATE: 'Sitzungsrate',
      SESSIONRATEDESCRIPTION: 'Historisches Diagramm der Anforderungsfrequenz durch CWMP für Online-Geräte.',
      LATENCY: 'Anforderungsverzögerung',
      LATENCYDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Anforderungsverzögerung für alle Geräte in regelmäßigen Abständen. Anforderungsverzögerung: Die gesamte Zeit, die für eine Anforderung zwischen dem Gerät und AMP aufgewendet wurde.',
      REGISTERED_COUNT_DISTRIBUTION: 'Registrierte Geräte',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm der registrierten Geräte für jedes Produkt.',
      PROVISIONINGTYPEDISTRIBUTION: "Zuteilung der Versorgungsart",
      PROVISIONINGTYPEDISTRIBUTION_DESCRIPTION: "Die Verteilung aller für jedes registrierte Produkt ausgewählten Konfigurationstypen.",
      ONLINE_COUNT_DISTRIBUTION: 'Online-Geräte',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm der Online-Geräteanzahl für jedes Produkt.',
      HISTORYONLINEDEVICE: 'Online-Geräte',
      ONLINEDEVICEDESCRIPTION: 'Historisches Diagramm der Anzahl von Online-Geräten für jedes Produkt.',
      SOFTWARE_VERSION_DISTRIBUTION: 'Softwareversion',
      SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm der Softwareversionen für Online-Geräte.',
      PROVISIONING_CODE_DISTRIBUTION: 'Bereitstellungscode',
      PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm des Bereitstellungscodes für Online-Geräte.',
      XMPP_STATUS_DISTRIBUTION: 'XMPP-Status',
      XMPP_STATUS_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm des XMPP-Status für Online-Geräte.',
      IMS_STATUS_DISTRIBUTION: 'IMS-Statusverteilung',
      IMS_STATUS_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm des IMS-Registrierungsstatus für Online-Geräte.',
      SIM_STATUS_DISTRIBUTION: 'SIM-Status',
      SIM_STATUS_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm des SIM-Verbindungsstatus für Online-Geräte.',
      IPSEC_STATUS_DISTRIBUTION: 'IPSec-Status',
      IPSEC_STATUS_DISTRIBUTION_DESCRIPTION: 'Verteilungsdiagramm des IPSec-Tunnelverbindungsstatus für Online-Geräte.',
      TOTAL: 'Gesamt',
      ONLINE: 'Online',
      ONLINE_DEVICE: 'Online-Geräte',
      ONLINE_DEVICE_DESCRIPTION: 'Gesamtzahl der Online-Geräte.',
      GROUPS_COUNT: 'Gruppen',
      GROUPS_COUNT_DESCRIPTION: 'Gesamtzahl der Gruppen.',
      ONLINE_USERS: 'Online-Nutzer',
      ONLINE_USERS_DESCRIPTION: 'Die Gesamtzahl der Nutzer, die sich innerhalb der letzten halben Stunde angemeldet haben.',
      UE_COUNT: 'UE',
      UE_COUNT_DESCRIPTION: 'Gesamtanzahl der an die Kleinzelle angeschlossenen Benutzerausrüstung (UE).',
      ALARMS_TOTAL: 'Gesamtanzahl der Alarme',
      ALARMS_TOTAL_DESCRIPTION: 'Gesamtzahl der vom Gerät gemeldeten Alarme.',
      ALARMS_SERVERITY: 'Alarme',
      ALARMS_SERVERITY_DESCRIPTION: 'Gesamtzahl der Alarme mit verschiedenen Schweregraden wie Kritisch, Groß, Klein und Warnung.',
      GROUP_LIST: 'Gruppenliste',
      GROUP_LIST_DESCRIPTION: 'Liste der erstellten Gruppen mit detaillierten Informationen wie Anzahl der Geräte und Alarme mit verschiedenen Schweregraden.',
      COVERMAP: 'Abdeckungskarte',
      COVERMAP_DESCRIPTION: 'Zeigt die Standorte und die Funkabdeckung der Geräte in der Gruppe an.',
      ALARM_LIST: 'Alarmeliste',
      ALARM_LIST_DESCRIPTION: 'Liste aller vom Gerät gemeldeten Alarme, einschließlich gelöschter und ungelöschter Alarme, mit Schweregrad, Ereigniszeit und wahrscheinlicher Ursache.',
      SYSTEM_EVENT_LIST: 'Systemereignisse',
      SYSTEM_EVENT_LIST_DESCRIPTION: 'Liste aller protokollierten Ereignisse im Zusammenhang mit Kommunikations- und Zugriffsfehlern.',
      STSTEM_INFORMATIONS: 'Systeminformationen',
      STSTEM_INFORMATIONS_DESCRIPTION: 'AMP-Systeminformationen oder Serverberichtsinhalt.',
      TOTAL_CLIENTS: 'Gesamtanzahl der WiFi-RSSI-Verteilung',
      TOTAL_CLIENTS_DESCRIPTION: 'Verteilungsdiagramm der WiFi-Clients mit verschiedenen RSSI-Pegel.',
      TOTAL_CLIENTS_COUNT: 'WiFi-Clients',
      TOTAL_CLIENTS_COUNT_DESCRIPTION: 'Gesamtzahl der mit WiFi-APs verbundenen WiFi-Clients.',
      EXCELLENT_CLIENTS: 'Hervorragend',
      GOOD_CLIENTS: 'Gut',
      POOR_CLIENTS: 'Schlecht',
      STATISTICSOFTOTALCLIENTS: 'Aufzeichnungen der Gesamt-WiFi-RSSI-Verteilung',
      STATISTICSOFTOTALCLIENTS_DESCRIPTION: 'Historisches Diagramm der WiFi-Clients mit verschiedenen RSSI-Pegeln.',
      GROUPNAME: 'Gruppenname',
      PRODUCTNAME: 'Produktname',
      MANAGEMENTSCOPE: 'Verwaltungsbereich',
      REGION: 'Region',
      LOCATION: 'Ort',
      CONFIGURATION: 'Konfiguration',
      APS: 'Gesamtanzahl der APs',
      TOTALCLIENTS: 'Gesamtanzahl der Clients',
      EXCELLENTCLIENTS: 'Hervorragende Clients',
      GOODCLIENTS: 'Gute Clients',
      POORCLIENTS: 'Schlechte Clients',
      EXCELLENT: 'Hervorragend',
      GOOD: 'Gut',
      POOR: 'Schlecht',
      EXCELLENT_DESCRIPTION: 'RSSI > -65dBm',
      GOOD_DESCRIPTION: '-65dBm < RSSI < -80dBm',
      POOR_DESCRIPTION: 'RSSI < -80dBm',
      TOTALCLIENTSTABLE: 'Client-Tabelle',
      CLIENTS: 'WiFi-Client-Informationen',
      ONLINEAPS: 'Online-APs',
      TAGS: 'Tags',
      GROUPSLOCATION: 'Standort der Gruppen',
      GROUPSLOCATION_DESCRIPTION: 'Zeigt die Standorte und grundlegende Informationen aller Gruppen auf der Karte an.',
      DEVICESLOCATION: "Gerätestandorte",
      DEVICESLOCATION_DESCRIPTION: "Zeigt die Standorte und grundlegenden Informationen aller Geräte auf der Karte an.",
    },
    DEVICES: {
      WIFICLIENT: 'WiFi-Kunden',
      WIFIAPNAME: 'WiFi-AP-Name',
      WIFIAPROLE: 'WiFi-AP-Rolle',
      WIFIAPCONFVERSION: 'WiFi-AP-Konfig.-Version',
      WIFIAPNCONFVERSION: 'WiFi-APN-Konfig.-Version',
      TAGS: 'Tags',
      LIST: 'Geräteliste',
      SERIAL_NUMBER: 'Seriennummer',
      MODEL_NAME: 'Modellname',
      FIRMWARE: 'Firmware',
      LABEL: 'Etikett',
      GROUP: 'Gruppe',
      PRODUCT: 'Produkt',
      LAST_CONNECTED: 'Zuletzt verbunden',
      LAST_EVENT: 'Letztes Ereignis',
      UPTIME: 'Betriebszeit',
      TIME_ZONE: 'Zeitzone',
      ACTIVE: 'Aktiv',
      BAND: 'Band',
      CHANNEL: 'Kanal',
      BANDWIDTH: 'Bandbreite',
      UTILIZATION: 'Auslastung',
      RECEIVED: 'Empfangen',
      SENT: 'Gesendet',
      DOWNLINK_RATE: 'Downlink-Geschwindigkeit',
      UPLINK_RATE: 'Uplink-Geschwindigkeit',
      MODE: 'Modus',
      CONNECTTIME: 'Verbindungszeit',
      ERRORCODE: 'Fehlercode',
      ERRORDESCRIPT: 'Fehlerbeschreibung',
      ERRORTIME: 'Fehlerzeit',
      DAILYSENT: 'Täglich gesendet',
      DAILYRECEIVED: 'Täglich empfangen',
      ACCESSCOUNT: 'Zugriffszähler',
      UNINSTALLEDTIME: 'Deinstallierte Zeit',
      DATASIZE: 'Datengröße',
      CACHESIZE: 'Cache-Größe',
      SERVICEDISCOVERYSERVER: 'Dienstentdeckungsserver',
      ACTICE_BCG_SERVERS: 'Aktive BCG-Server',
      POWERCONSUMPTION: 'Stromverbrauch',
      STATE: 'Status',
      CONTAINERVERSION: 'Container-Version',
      APPLICATIONVERSION: 'Anwendungsversion',
      ENABLE: 'Aktivieren',
      CELL_RESERVED_FOR_OPERATOR_USE: 'Zelle für Betreiber-Nutzung reserviert',
      EUTRA_CARRIER_ARFCN: 'EUTRA-Träger-ARFCN',
      BLACKLISTED: 'In der Schwarzen Liste',
      VENDORCLASSID: 'Herstellerklassen-ID',
      EARFCNDOWNLOAD: 'Earfcn-Download',
      DOWNLOADBANDWIDTH: 'Download-Bandbreite',
      UPLOADBANDWIDTH: 'Upload-Bandbreite',
      REFERENCESIGNALPOWER: 'Referenzsignalstärke',
      SECURITY: 'Sicherheit',
      SEVERITY: 'Schweregrad',
      ALARMID: 'Alarm-ID',
      EVENTTYPE: 'Ereignistyp',
      EVENTTIME: 'Ereigniszeit',
      PROBABLECAUSE: 'Wahrscheinliche Ursache',
      SPECIFICPROBLEM: 'Spezifisches Problem',
      ACKUSER: 'Bestätigung Benutzer',
      ACKTIME: 'Bestätigungszeit',
      ADDITIONALTEXT: 'Zusätzlicher Text',
      ADDITIONALINFORMATION: 'Zusätzliche Informationen',
      PEER: 'Peer',
      DURATION: 'Dauer',
      CONNECT: 'Verbinden',
      START: 'Start',
      END: 'Ende',
      UPLOAD: 'Hochladen ',
      DOWNLOAD: 'Herunterladen',
      DOWNLOADDATAMODEL: 'Gesamtes Datenmodell herunterladen',
      DOWNLOADALL: 'Alle herunterladen',
      DOWNLOADSELECT: 'Ausgewählt herunterladen',
      TIME: 'Zeit',
      UPLOADRESULT: 'Hochladen Testergebnis',
      DOWNLOADRESULT: 'Herunterladen Testergebnis',
      EVENT: 'Ereignis',
      LOGLEVEL: 'Protokollstufe',
      REQUEST: 'Anfragen',
      CREATED: 'Erstellt',
      IMEI: 'IMEI',
      MAC: 'MAC',
      IP: 'IP',
      RSSI: 'RSSI',
      SSID: 'SSID',
      BSSID: 'BSSID',
      APSN: 'AP S/N',
      APNAME: 'AP Name',
      APMAC: 'AP MAC',
      APIP: 'AP IP',
      LISTDESCRIPTION: 'Liste aller erlaubten Geräte mit allgemeinen Informationen wie Seriennummer, MAP und IP-Adresse.',
      SMALLCELL_LIST: 'Kleine Zellenliste',
      SMALLCELL_LISTDESCRIPTION: 'Liste aller erlaubten kleinen Zellen, die zum Funkzugangsnetz gehören, mit spezifischen Informationen wie UEs, PCI und GNB-ID.',
      WIFI_AP_LIST: 'WiFi AP-Liste',
      WIFI_AP_LISTDESCRIPTION: 'Liste aller erlaubten WiFi-APs, die zum WiFi-AP-Netzwerk gehören, mit spezifischen Informationen wie Clients, Kanal und Kanalnutzung.',
      WIFI_MESH_LIST: 'WiFi-Mesh-Liste',
      WIFI_MESH_LISTDESCRIPTION: 'Liste aller erlaubten WiFi-Mesh-APs, die zum WiFi-Mesh-Netzwerk gehören, mit spezifischen Informationen wie Clients, Kanal und Kanalnutzung.',
      CURRENTNUMBERS: 'Aktuelle Alarmzahlen.',
      ALARMMGMTDESCRIPTION: "Liste aller vom Gerät gemeldeten Alarme, einschließlich geklärter, ungelöster mit Schweregrad, Ereigniszeit und wahrscheinlicher Ursache.",
      REGISTERDEVICE: 'Gerät registrieren',
      REGISTERSMALLCELLDEVICE: 'Kleine Zelle registrieren',
      REGISTERAPDEVICE: 'WiFi-AP-Gerät registrieren',
      REGISTERNESHDEVICE: 'WiFi-Mesh-Gerät registrieren',
      LIVEUPDATE: 'Live-Update',
      SPEEDTEST: 'Geschwindigkeitstest',
      SPEEDTESTDESCRIPTION: "Das Gerät verwendet TR-143, um Upload- und Download-Geschwindigkeitstests durchzuführen und die Netzwerkleistung zu messen, um optimale Datenübertragungsraten sicherzustellen.",
      FIVECORE: '5G-Kernnetz',
      FIVECORENETWORK: '5G-Kernnetz',
      FIVECORENETWORK_DESCRIPTION: 'Bereitstellung eines Links zum 5G-Kern, konfiguriert im System/Settings mit der 5G-Kern-URL.',
      CELL_THROUGHPUT: 'Zellen-Durchsatz',
      CELL_THROUGHPUT_DESCRIPTION: "Informationen über den Download- und Upload-Durchsatz von kleinen Zellen.",
      UE_LIST: 'UE-Liste',
      UE_LIST_DESCRIPTION: 'UE (Benutzergerät), das mit dem 5G-Kernnetz verbunden und getrennt ist, mit detaillierten Informationen wie Zustand, IMSI, IMEI, GNB-ID und IP-Adresse.',
      UE_5QI_PACKET: 'UE 5QI-Paket',
      UE_5QI_PACKET_DESCRIPTION: 'Liste der 5QI-Pakete des UE, einschließlich verschiedener Verkehrsmessungen und Abbruchraten für Uplink und Downlink.',
      BULKDATAPROFILE_DESCRIPTION: "Liste von Bulk-Datenprofilen mit detaillierten Informationen wie Alias, Status, URL, Parametern und codiertem Typ.",
      SOFTWAREMODULES_DESCRIPTION: "Liste von Softwaremodulen mit detaillierten Informationen wie UUID, Alias, Name, URL und letztem Aktualisierungszeitpunkt.",
      ONLINE_COUNT_DISTRIBUTION: 'Online-Geräte',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: 'Verteilungsgrafik der Anzahl der Online-Geräte für jedes Produkt.',
      REGISTERED_COUNT_DISTRIBUTION: 'Registrierte Geräte',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: 'Verteilungsgrafik der Anzahl der registrierten Geräte für jedes Produkt.',
      CONNECTIVITYTEST: 'Konnektivitätstest',
      REBOOT: 'Neustart',
      FACTORYRESET: 'Werkseinstellungen anwenden',
      UPLOADLOG: 'Protokoll hochladen',
      UPGRADEFIRMWARE: 'Aktualisieren',
      GENERATEREPORT: 'Bericht generieren',
      ADDFILE: 'Dateizeiger hinzufügen',
      SETTING: 'Präferenz verwalten',
      GENERAL: 'Allgemein',
      GENERALSTATUS: 'Allgemeiner Status',
      OPERATION: 'Betrieb zuweisen',
      PROTOCOL: 'Protokoll',
      SELECTPROTOCOL: 'Protokoll auswählen',
      NETCONFAUTH: 'NETCONF unterstützt Passwort- und private Schlüssel-Authentifizierung, geben Sie entweder ein Passwort oder einen privaten Schlüssel ein.',
      REGISTER: 'Registrieren',
      CONNECTIONREQ: 'Verbindungsanfrage',
      TELEMETRY: 'Telemetrie',
      INFO: 'Info',
      MAP: 'Karte',
      FAPCONNECTEDCLIENTS: 'FAP verbundene Clients',
      FAPCONNECTEDCLIENTSDESCRIPTION: 'Verbundenen FAP-Clients.',
      GENERALINFO: 'Allgemeine Informationen',
      GENERALINFODESCRIPTION: "Allgemeine Informationen des Geräts wie Seriennummer, Modellname und Softwareversion.",
      CELLULARSTATUS: 'Mobilfunkstatus',
      CELLULARSTATUSDESCRIPTION: 'Mobilfunkinformationen wie Dienststatus, Zugangstechnologie, Band, RSRP, RSRQ und RSRI ',
      WORKFLOWLIST: 'Workflow-Liste',
      WORKFLOWLISTDESCRIPTION: 'Liste der Workflows, die derzeit auf diesem Gerät angewendet werden.',
      DATAUSAGE: 'Mobilfunkdatenverbrauch',
      DATAUSAGEDESCRIPTION: 'Historisches Diagramm des Mobilfunkdatenverbrauchs.',
      CLIENTS: 'Clients',
      CLIENTDESCRIPTION: 'Clients wie mobile Geräte, Laptops, Tablet-Zahlen.',
      UE: 'UE',
      UEDESCRIPTION: 'Gesamtzahl der UEs, die mit der kleinen Zelle verbunden sind.',
      SIMCARDINFO: 'SIM-Karteninfo',
      SIMCARDINFODESCRIPTION: 'Informationen zur SIM-Karte wie Status, ICCID, IMSI, IMPI und IMPU',
      WIFISTATUS: 'WiFi-Radiostatus',
      WIFISTATUSDESCRIPTION: "Aktuelle Einstellungen und Status des Radios von allen verfügbaren WiFi-Band(s)/Schnittstelle(n).",
      WIFICHANNELUTLILIZATION: 'WiFi-Kanalnutzung',
      WIFICHANNELUTLILIZATIONDESCRIPTION: 'Kanalbelastung aus dem Verkehr aller verfügbaren Bänder.',
      CONNECTEDHOSTS: 'Verbundenen Hosts',
      CONNECTEDHOSTSDESCRIPTION: 'Informationen über die mit dem Gerät verbundenen Hosts.',
      REGSTATUS: 'Registrierungsstatus',
      REGSTATUSDESCRIPTION: 'Registrierungsinformationen wie letzte Registrierungszeit, letzte Trennungszeit und letzter Trennungsgrund.',
      ERRORSTATUS: 'Fehlerstatus',
      ERRORSTATUSDESCRIPTION: 'Liste der vom Gerät gemeldeten Fehler wie Fehlermeldung und Fehlerzeit.',
      SPECTRUMDESCRIPTION: "Informationen über den Bereitstellungsmodus der kleinen Zelle mit spezifischem Spektrum, wie NR-Band, ARFCN und TDD-Zeitfenster.",
      APPLIST: 'Anwendungsliste',
      APPLISTDESCRIPTION: 'Informationen zu den Anwendungen für das Gerät.',
      SERVICEPRO: 'Dienstanbieter',
      SERVICEPRODESCRIPTION: 'Dienstanbieter-Liste für das Gerät.',
      SPECTRUM: 'Spektrum',
      PLMNNEIGHBORLIST: 'Nachbarn / PLMN-Liste',
      NEIGHBORLIST: 'Nachbarnliste',
      NEIGHBORLISTDESCRIPTION: 'Informationen über die ANR-Nachbarnliste, die Handover-Liste und die PLMN-Liste.',
      HANDOVERLIST: 'Konfigurierte Nachbarliste',
      ANRNEIGHBORLIST: 'ANR-Nachbarnliste',
      UTRANEIGHBORLIST: 'Utra-Nachbarnliste',
      PLMNLIST: 'PLMN-Liste',
      APPSTATUS: 'Status der Geräte-Apps',
      APPSTATUSDESCRIPTION: 'Stromverbrauch der Anwendungen für das Gerät.',
      LXCSTATUS: 'LXC-Status',
      LXCSTATUSDESCRIPTION: 'Status des Linux-Containers für das Gerät.',
      SUBSCRIPTION: 'USP Abonnement',
      SUBSCRIPTIONDESCRIPTION: 'Liste der Abonnementartikel über USP mit detaillierten Informationen wie Alias, Status, Benachrichtigungstyp und Empfänger.',
      BULKDATAPROFILE: 'Bulk-Datenprofil',
      SOFTWAREMODULES: 'Softwaremodule',
      CONTROLLERTRUSTROLE: 'Controller-Vertrauensrolle',
      CONTROLLERTRUSTROLEDESCRIPTION: 'Listen Sie die controllerTrust-Rollen auf, die detaillierte Informationen wie Aliase, Status, Berechtigungen, Anzahl der Einträge usw. enthalten',
      FIRMWAREIMAGES: 'Firmware-Bilder',
      FIRMWAREIMAGESDESCRIPTION: 'Listen Sie die Firmware-Images auf, die detaillierte Informationen wie Aliase, Status, bootFailureLog usw. enthalten',
      BOOTFAILURELOG: "Boot-Fehlerprotokoll",
      AVAILABLE: "Verfügbar",
      DEVICEACTION: 'Geräteaktion',
      CURRENTALARMLIST: 'Aktuelle Alarmliste',
      ALARMLIST: 'Alarmliste',
      ACKALARM: 'Alarm bestätigen',
      ALARMMGMT: 'Alarmverwaltung',
      ALARMMGMDESCRIPTION: 'Liste aller vom Gerät gemeldeten Alarme, einschließlich geklärter, ungelöster mit Schweregrad, Ereigniszeit und wahrscheinlicher Ursache.',
      CLEAREDTIME: 'Geklärte Zeit',
      HISTORYALARM: 'Historie der Alarmverwaltung',
      CURRENTYALARM: 'Aktuelle Alarmliste',
      DATAMODEL: 'Datenmodell',
      SELECTDATAMODEL: 'Datenmodell auswählen',
      DATANODE: 'Datenknoten',
      DATANODEDESCRIPTION: "Alle vom Gerät an AMP gemeldeten Parameterknoten in einer Baumstruktur anzeigen.",
      PARAMETERDATADESCRIPTION: "Detaillierte Informationen des ausgewählten Parameterknotens wie Kindknoten, Parametername, Attribut, Pfad und Wert.",
      PARAMETERDATA: 'Parameterdaten',
      SELECTDATANODE: 'Bitte Parameter aus dem Datenknoten auswählen',
      LOGS: 'Protokolle',
      LOG: 'Protokoll',
      SESSIONLOG: 'Sitzungsprotokoll-Liste',
      SESSIONLOGDESCRIPTION: 'Liste der Sitzungsprotokolle zwischen AMP und Gerät.',
      SESSIONLOGRATE: 'Berichtsrate',
      SESSIONLOGRATEDESCRIPTION: "Statistiken der periodischen Berichtsrate (Anzahl) des Geräts an AMP über die letzten 24 Stunden.",
      SESSLOG: 'Sitzungsprotokoll',
      PENDINGLOG: 'Liste der ausstehenden Vorgangsprotokolle',
      PENDINGLOGPENDINGLOGS: 'Liste der ausstehenden Vorgangsaufgaben, die auf die Gerätev reception warten.',
      OPERATIONLOGS: 'Liste der Vorgangsprotokolle',
      OPERATIONLOGSDESCRIPTION: 'Liste der vom AMP zugewiesenen Vorgangsaufgaben, die das Gerät ausgeführt hat, zusammen mit den gemeldeten Ergebnissen.',
      CALLLOG: 'Anrufprotokoll-Liste',
      CALLLOGDESCRIPTION: 'Liste der Anrufprotokolle wie Peer, Typ und Dauer.',
      SPEEDTESTHISTORY: 'Geschwindigkeitstestverlauf',
      CONNECTIVITYTESTHISTORY: 'Konnektivitätstestverlauf',
      CONNECTIVITYTESTHISTORYDESCRIPTION: 'Konnektivitätstestverlauf.',
      DEVICEREPORTLIST: 'Gerätereport-Liste',
      DEVICEREPORTLISTDESCRIPTION: 'Liste der generierten Zusammenfassungsberichte mit Schlüsselfunktionen für das Gerät.',
      PMLOG: 'PM KPI-Protokolle',
      PMLOGDESCRIPTION: 'Liste der KPI-Daten, die von der kleinen Zelle gemeldet werden.',
      ADVANCE: 'Erweitert',
      CBSDSTATUS: 'CBSD-Status',
      CBSDSTATUSDESCRIPTION: 'Informationen über das Bürgerbreitbanddienstgerät wie SAS-Anbieter, CBSD-ID, GPS-Status und Genehmigungszustand.',
      CBSDCONDIGS: 'CBSD-Konfigurationen',
      CBSDCONDIGSDESCRIPTION: 'Konfiguration des Bürgerbreitbanddienstgeräts wie CBSD-Seriennummer, Modell und Softwareversion.',
      TERMINAL: 'Terminal',
      TERMINALDESCRIPTION: 'Bereitstellung von Geräten, die eine remote Echtzeit-Operation über das Terminal unterstützen.',
      COMMANDXML: 'Befehls-XML',
      COMMANDXMLDESCRIPTION: 'Gerätebefehls-XML.',
      ACSSTATUS: 'ACS-Status',
      DEVICESTATUS: 'Gerätestatus',
      SASTATUS: 'Zellenstatus',
      RFCONTROL: 'RF-Steuerung der Zelle',
      RFCONTROLDESCRIPTION: 'Bereitstellung von RF-bezogenen Einstellungen und Schaltern.',
      ANTENNABEAM: 'Antennenstrahl',
      BEAMMENU: 'Strahlmenü',
      ANTENNABEAMDESCRIPTION: "Bereitstellung der Änderung des Antennenstrahlwinkels der kleinen Zelle.",
      BEAMID: 'Strahl-ID',
      GPSANTENNA: 'GPS-Antenne',
      GPSANTENNAPATH: 'GPS-Antennenpfad',
      ANTENNAPATH: 'Antennepfad',
      GPSANTENNADESCRIPTION: 'Bereitstellung der Auswahl des GPS-Antennenpfades als extern oder intern.',
      DEPLOYMENTMODE: 'Bereitstellungsmodus',
      DEPLOYMENTMODEDESCRIPTION: 'Bereitstellungsmodus des Geräts.',
      HWMONITOR: 'Maschinen-Diagnose',
      HWMONITORDESCRIPTION: "Aktivierung der Diagnosen der kleinen Zelle, wie CPU, Temperatur und Stromverbrauch.",
      RECONNECT: 'Erneut verbinden',
      DISCONNECT: 'Trennen',
      DOWNLOADLOG: 'Protokoll herunterladen',
      REPEATLASTCMD: 'Letzten Befehl wiederholen',
      CLEARSCROLLBACK: 'Scrollback löschen',
      CELLULARDIAGNOSTIC: 'Diagnose der Mobilfunk-Schnittstelle anfordern',
      WIFIDIAGNOSTIC: 'Diagnose der WLAN-Schnittstelle anfordern',
      SYSHEALTHREBOOT: 'Neustart des Systemstatus anfordern',
      DIFFRENTPROTOCOLNOTALLOWED: 'Batch Register zwei oder mehr verschiedene Protokollgeräte sind nicht erlaubt',
      DEVICEERRCODE1: 'Nicht erlaubt, Geräte zu nicht vorhandenen Produkten hinzuzufügen',
      DEVICEERRCODE2: 'Das Gerät wurde registriert',
      DEVICEERRCODE3: 'Seriennummer ist illegal',
      INVALIDPRODUCT: 'Ungültiges Produkt',
      DOFURBISHMENT: ' will be refurbishment process and all device data are removed. Willst du trotzdem fortfahren?',
      CONFURBISHMENT: 'Refurbishment Process',
      MAXUES: 'Maximale Anzahl gleichzeitiger UEs:',
      OSMERROR: "Fehler beim Laden der Karte. Bitte überprüfen Sie die Internetverbindung oder versuchen Sie es später erneut.",
      GOOGLEMAPERROR: "Fehler beim Laden der Google Maps API. Bitte überprüfen Sie den Google Maps API-Schlüssel oder die Internetverbindung.",
      PERMISSIONNUMBEROFENTRIES: "Anzahl der Einträge",
      ACTION: {
        ASSIGN_OPERATION: 'Operation Zugewiesen',
        ASSIGN_OPERATION_SUCC: 'Operation erfolgreich zugewiesen.',
        ASSIGN_OPERATION_FAIL: 'Die Zuweisung der Operation ist fehlgeschlagen.',
        ASSIGN_OPERATION_SUCCESS_MESSAGE: 'Operation ist erfolgreich.',
        ASSIGN_OPERATION_WARNING_MESSAGE1: 'Warten',
        ASSIGN_OPERATION_WARNING_MESSAGE2: 'um die Operation zu erhalten.',
        ASSIGN_OPERATION_FAIL_MESSAGE: 'Zuweisung der Operation fehlgeschlagen.',
        CONFIRM_LIVE_UPDATE: 'Bestätigen Sie das Live-Update',
        DO_LIVE: 'Möchten Sie ein Live-Update durchführen ',
        DO_LIVE_UPDATE: 'Möchten Sie ausgewählte Geräte live aktualisieren?',
        LIVESUCCESS: 'Live-Update erfolgreich!',
        LIVEFAIL: 'Live-Update fehlgeschlagen!',
        CONFIRM_REBOOT: 'Neustarten ',
        DOREBOOT: 'Während des Neustarts werden alle vom Gerät bereitgestellten Dienste (einschließlich Fernverwaltung) für mehrere Minuten vorübergehend unterbrochen.',
        SELECTREBOOT: 'Während des Neustarts werden alle vom ausgewählten Gerät bereitgestellten Dienste (einschließlich Fernverwaltung) für mehrere Minuten vorübergehend unterbrochen.',
        ABOUT_TO_REBOOT: 'Neustart steht bevor ...',
        REBOOT_SUCCESS: 'Neustart erfolgreich!',
        WAIT_REBOOT: 'Warte auf den Neustart ...',
        REBOOTFAIL: 'Neustart fehlgeschlagen!',
        REBOOT_TIMED_OUT: 'Zeitüberschreitung beim Neustart!',
        SHUTDOWN_SUCCESS: 'Abschaltung erfolgreich!',
        CONFIRMFACTORYRESET: 'Werkseinstellungen zurücksetzen ',
        DOFACTORYRESET: 'Das Gerät wird auf den ursprünglichen Herstellungszustand zurückgesetzt und alle benutzerdefinierten Einstellungen werden gelöscht.',
        SELECTFACTORYRESET: 'Die ausgewählten Geräte werden auf den ursprünglichen Herstellungszustand zurückgesetzt und alle benutzerdefinierten Einstellungen werden gelöscht.',
        FACTORYRESETSUCC: 'Werkseinstellungen zurücksetzen erfolgreich!',
        FACTORYRESETFAIL: 'Werkseinstellungen zurücksetzen fehlgeschlagen!',
        FACTORYRESET_TIMED_OUT: 'Beim Zurücksetzen auf Werkseinstellungen ist eine Zeitüberschreitung aufgetreten!',
        CONFIRMUPLOADLOG: 'Protokoll hochladen',
        DOUPLOADLOG: 'Anfrage, dass das Gerät sein Systemprotokoll zur weiteren Fehlersuche an eine entfernte URL hochlädt. Die Upload-URL kann im Abschnitt Dateien der Einstellungen auf der Produkt- und Systemseite konfiguriert werden.',
        SELECTUPLOADLOG: 'Anfrage, dass die ausgewählten Geräte ihr Systemprotokoll zur weiteren Fehlersuche an eine entfernte URL hochladen. Die Upload-URL kann im Abschnitt Dateien der Einstellungen auf der Produkt- und Systemseite konfiguriert werden.',
        UPLOADLOG: "Protokoll hochladen",
        UPLOADLOGSUCC: "Protokoll erfolgreich hochgeladen!",
        UPLOADLOGSUCCMESSAGE: "Geräteoperation wurde erfolgreich erstellt!",
        UPLOADLOGFAIL: 'Protokoll konnte nicht hochgeladen werden!',
        ABOUT_TO_UPLOADLOG: 'Das Protokoll wird hochgeladen ...',
        UPLOADLOG_TIMED_OUT: 'Zeitüberschreitung beim Upload-Protokoll!',
        CONFIRMFIRMWARE: 'Aktualisierung ',
        SUCCESSFIRMWARE: 'Aktualisierung erfolgreich!',
        UPGRADE_BEING_DOWNLOADED: 'Herunterladen...',
        ASSIGN_UPGRADE_OPERATION: 'Upgrade-Vorgang zuweisen',
        UPGRADE_STATUS_INSTALLING: 'Installieren...',
        UPGRADE_BEING_ACTIVATED: 'Aktivierend...',
        UPGRADE_DEVICE_BEING_RESTARTED: "Das Gerät wird neu gestartet ...",
        WAIT_UPGRADE: 'Warte auf Inform...',
        UPGRADING: 'Upgrade...',
        UPGRADE_COMPLETE_REBOOTING: 'Upgrade abgeschlossen, Neustart',
        UPGRADE_TIMED_OUT: 'Beim Upgrade ist eine Zeitüberschreitung aufgetreten!',
        UPGRADEFIRMWARE: 'Aktualisieren ',
        UPGRADEFIRMWARESUCCMESSAGE: "Geräteoperation für Firmware-Upgrade wurde erfolgreich erstellt!",
        FAILFIRMWARE: 'Aktualisierung fehlgeschlagen!',
        FAILFIRMWAREPENDING: 'Aktualisierung ausstehendes Operationsprotokoll.',
        REBOOTPENDING: 'Neustart im ausstehenden Betriebsprotokoll.',
        FACTORYRESETPENDING: 'Werkseinstellungen zurücksetzen im ausstehenden Betriebsprotokoll.',
        UPLOADLOGPENDING: 'Protokoll hochladen im ausstehenden Betriebsprotokoll.',
        DOFIRMWARE: 'Während des Firmware-Aktualisierungsprozesses werden alle vom Gerät bereitgestellten Dienste (einschließlich Fernverwaltung) für mehrere Minuten vorübergehend unterbrochen. ',
        SELECTFIRMWARE: 'Während des Firmware-Aktualisierungsprozesses werden alle von den ausgewählten Geräten bereitgestellten Dienste (einschließlich Fernverwaltung) für mehrere Minuten vorübergehend unterbrochen.',
        ACTION_CONFIRM: "Möchten Sie trotzdem fortfahren?",
        ABOUT_TO_FACTORYRESET: 'Der Werksreset steht bevor ...',
        CONFIRM_GENERATE: 'Erzeugen ',
        CONFIRM_REPORT: ' Bericht',
        CONFIRM_GENERATE_REPORT: 'Bericht erstellen',
        DO_GENERATE_REPORT: 'Erzeugen Sie einen zusammenfassenden Bericht mit den wichtigsten Parametern für das Gerät. Der Bericht kann im Widget "Geräteberichts-Liste" auf der Protokollseite gefunden werden.',
        SELECT_GENERATE_REPORT: 'Erzeugen Sie einen zusammenfassenden Bericht mit den wichtigsten Parametern für die ausgewählten Geräte. Der Bericht kann im Widget "Geräteberichts-Liste" auf der Protokollseite gefunden werden.',
        SUCCESSGENE: 'Bericht erfolgreich erstellt!',
        FAILGENE: 'Berichtserstellung fehlgeschlagen!',
        CONFIRM_DELETE: 'Löschen bestätigen',
        DO_DELETE: 'Möchten Sie ausgewählte Geräte löschen?',
        CONFIRM_APPLY: 'Anwendung bestätigen',
        DO_APPLY_CONFIGRUATION: 'Möchten Sie diese Konfiguration anwenden?',
        IMPORT_JSON_FILE_MESSAGE: 'Bitte importieren Sie die Konfigurations-JSON-Datei.',
        APPLY_SUCC: 'Anwendung erfolgreich!',
        APPLY_FAIL: 'Anwendung fehlgeschlagen!',
        PLESESELECT: 'Bitte wählen Sie ein Gerät aus',
        CONFIRMReset: 'Reset bestätigen',
        SUREReset: 'Sind Sie sicher, dass Sie zurücksetzen möchten?',
        RESETPERSONALTHEME: 'Sind Sie sicher, dass Sie das persönliche Theme für alle Seiten zurücksetzen möchten?',
        RESETCURRENTPERSONALTHEME: 'Sind Sie sicher, dass Sie das persönliche Design der aktuellen Seite zurücksetzen möchten?',
        RESETALL: 'Alle Seiten zurücksetzen',
        RESETCURRENTPAGE: 'Die aktuelle Seite zurücksetzen',
        RESETSuccess: 'Zurücksetzen des Widget-Layouts erfolgreich',
        CONFIRMSave: 'Speichern bestätigen',
        SURESave: 'Sind Sie sicher, dass Sie die aktuelle Ausgabe speichern möchten?',
        SAVESuccess: 'Speichern des Widget-Layouts erfolgreich',
        DODELETE: 'Möchten Sie löschen ',
        DELETESUCCESS: 'Löschen erfolgreich',
        DELETEFAIL: 'Löschen fehlgeschlagen!',
        CONFIRMBAN: 'Sperren bestätigen',
        BANSUCCESS: 'Sperren erfolgreich!',
        BANFAIL: 'Sperren fehlgeschlagen!',
        DOBAN: 'Möchten Sie sperren ',
        BANSELECT: 'Möchten Sie ausgewählte Geräte sperren?',
        CONFIRMRegister: 'Registrierung bestätigen',
        ONLYVALID: 'Überprüfen, ob legitime Geräte automatisch beibehalten werden',
        SUCCESSRegister: 'Gerät erfolgreich registriert!',
        FAILRegister: 'Registrierung des Geräts fehlgeschlagen!',
        DORegister: 'Möchten Sie registrieren ',
        FAIL: 'Fehlgeschlagen!',
        CONFIRMOPER: 'Löschen des Betriebsprotokolls bestätigen',
        OPERSELECT: 'Möchten Sie ausgewählte Betriebsprotokolle löschen?',
        CONNECTIVITYTESTSELECT: 'Möchten Sie ausgewählte Konnektivitätstestprotokolle löschen?',
        CONNECTIVITYTEAllCONFIRM: 'Bestätigen Sie die Bereinigung des Konnektivitätstestverlaufs?',
        CONNECTIVITYTEAll: 'Möchten Sie den Konnektivitätstestverlauf bereinigen?',
        SPEEDTESTSTSELECT: 'Möchten Sie den ausgewählten Geschwindigkeitstestverlauf löschen?',
        SPEEDTESTAllCONFIRM: 'Geschwindigkeitstestverlauf wirklich bereinigen?',
        SPEEDTESTAll: 'Möchten Sie den Geschwindigkeitstestverlauf bereinigen?',
        PLESEOPER: 'Bitte wählen Sie abgeschlossene Betriebsprotokolle aus',
        DOTAG: 'Möchten Sie das Tag löschen ',
        TAGSUCC: 'Tags erfolgreich gelöscht!',
        TAGFAIL: 'Tags konnten nicht gelöscht werden!',
        ADDTAGSUCC: 'Tags erfolgreich hinzugefügt!',
        ADDTAGFAIL: 'Tags konnten nicht hinzugefügt werden!',
        UPDAGEDEVICE: 'Gerät aktualisieren!',
        CONFIRMCANCEL: 'Abbruch der Operation bestätigen',
        DOCANCEL: 'Möchten Sie abbrechen ',
        CANCELSUCC: 'Abbruch erfolgreich!',
        CANCELFAIL: 'Abbruch fehlgeschlagen!',
        SELECTCANCEL: 'Möchten Sie ausgewählte ausstehende Betriebsprotokolle abbrechen?',
        PLEASECANCEL: 'Bitte wählen Sie ausstehende Betriebsprotokolle aus',
        CONFIRMREPORT: 'Löschen des Berichts bestätigen',
        SELECTREPORT: 'Möchten Sie ausgewählte Berichtsprotokolle löschen?',
        PLEASEREPORT: 'Bitte wählen Sie das Berichtsprotokoll aus',
        CONFIRMSESSION: 'Bestätigen Sie das Löschen des Sitzungsprotokolls',
        SELECTSESSION: 'Möchten Sie ausgewählte Sitzungsprotokolle löschen?',
        PLEASESESSION: 'Bitte wählen Sie das Sitzungsprotokoll aus',
        CONFIRMACK: 'Bestätigen Sie die Bestätigung des Alarms?',
        SELECTACK: 'Möchten Sie alle ausgewählten Alarme bestätigen?',
        DOACK: 'Möchten Sie den Alarm bestätigen ',
        CONFIRMSAVELOCA: 'Bestätigen Sie das Speichern des Orts',
        DOSAVELOCA: 'Möchten Sie den Ort speichern?',
        CONFIRMRESETLOCA: 'Bestätigen Sie das Zurücksetzen des Orts',
        DORESETLOCA: 'Möchten Sie den Ort zurücksetzen?',
        SAVESUCC: 'Speichern erfolgreich!',
        SAVEFAIL: 'Speichern fehlgeschlagen!',
        RESETSUCC: "Zurücksetzen erfolgreich!",
        RESETFAIL: "Zurücksetzen fehlgeschlagen!",
        ACTIVESUCC: 'Aktivieren des Geräts erfolgreich!',
        ACTIVEFAIL: 'Aktivieren des Geräts fehlgeschlagen!',
        BANDEVSUCC: 'Sperren des Geräts erfolgreich!',
        BANDEVFAIL: 'Sperren des Geräts fehlgeschlagen!',
        WAITOTHER: 'Warten auf die andere Partei, um an der Sitzung teilzunehmen ...',
        ACSTROUBL: 'ACS nimmt an der Fehlerbehebungssitzung teil',
        WAITCPE: 'Warten auf CPE, um an der Fehlerbehebungssitzung teilzunehmen',
        ACSCONNECT: 'ACS stellt eine Verbindung zur Fehlerbehebungssitzung her',
        ACSSETTING: 'Warten auf die ACS-Einrichtung zur Fehlerbehebungssitzung',
        CPEJOIN: 'CPE nimmt an der Fehlerbehebungssitzung teil',
        SESSESTABLISH: 'Sitzung wurde hergestellt',
        CONFIRMTROUB: 'Bestätigen Sie das Trennen der Fehlerbehebungssitzung',
        DODISCONNECT: 'Möchten Sie die Sitzung trennen ',
        CONFIRMUSER: 'Benutzerentfernung bestätigen?',
        DOUSER: 'Möchten Sie den Benutzer löschen',
        SUCCESS: 'Erfolg!',
        ERROR: 'Fehler!',
        CONFLOGENTRY: 'Bestätigen Sie das Entfernen des Protokolleintrags',
        UPLOAD_CONNECTIVITYTEST: 'Konnektivitätstest hochladen, bitte warten ...',
        DOWNLOAD_CONNECTIVITYTEST: ' Konnektivitätstest herunterladen, bitte warten ...',
        COMPLETE_CONNECTIVITYTEST: ' Konnektivitätstest abgeschlossen !',
        CONNECTIVITYTEST_URL: 'Testdatei-URL',
        UPLOAD_SPEEDTEST: 'Upload-Geschwindigkeitstest, bitte warten...',
        DOWNLOAD_SPEEDTEST: 'Download-Geschwindigkeitstest, bitte warten...',
        COMPLETE_SPEEDTEST: 'Geschwindigkeitstest abgeschlossen!',
        SPEEDTEST_URL: 'Testdatei-URL',
        REQUESTUPDATEFAIL: 'Aktualisierungsanforderung fehlgeschlagen',
        CONFVERSION_NOTCHANGED: "Die Konfigurationsversion wurde nicht geändert.",
      },
      OPERATION_ACTION: {
        SELECT_OPERATION: 'Wählen Sie Profile aus',
        SELECT_WORKFLOW: 'Workflow auswählen',
        SELECT_Action_OR_WORKFLOW: "Wählen Sie Profile oder Workflow aus",
        ADD_OPERATION: 'Zu Profilen hinzufügen',
        OPERATION_COUNT: 'Anzahl',
        EDIT_OPERATION: 'Operation bearbeiten',
        EDIT_CONFIGURATION: 'Konfiguration bearbeiten',
        OPERATION_DETAILS: 'Operation Details',
        SETUP_OPERATION: 'Details zu Setup-Profilen',
        ADD_OPERATION_TO: 'Vorgang zu Profilen hinzufügen',
        OPERATION_ACTIONS: 'Betriebsprofile',
        ENTER_OPERATION_ACTIONS: 'Geben Sie Ihre Betriebsprofile ein.',
        OPERATION_TYPE: 'Operationstyp',
        SELECT_OPERATIONTYPE: 'Wählen Sie den Operationstyp',
        EDIT_ACTIONS: 'Bearbeiten Sie Betriebsprofile',
        MANUAL_OPERATION: 'Manuelle Konfigurationsdatei',
        MANUAL_WORKFLOW: 'MANUELLER Workflow',
        COMPLETION_RATE: 'Abschlussrate'
      },
      AMP: 'AMP',
      GO: 'Go',
      COLLAPSE: 'Einklappen',
      COLLAPSEALL: 'Alle einklappen',
      EXPAND: 'Erweitern',
      EXPANDALL: 'Alle erweitern',
      DISCOVER: 'Entdecken',
      DISCOVERDATAMODEL: 'Entdecken: Gerät bitten, ausgewählte Parameter zu aktualisieren',
      PATH: 'Pfad',
      REQUIRED: 'Erforderlich',
      OPTIONALPARAM: 'Es gibt keine optionalen Parameter',
      SENDRESP: 'Antwort senden',
      ACCESSLIST: 'Zugriffsliste',
      SELECTACCESSLIST: 'Zugriffsliste auswählen',
      REPORTEDVALUE: 'gemeldeter Wert',
      INPUTLIST: 'Eingabeliste',
      ADDEDITTAG: 'Tags hinzufügen/bearbeiten',
      ADDTAGS: 'Tags hinzufügen',
      ADDTAG: 'Tag hinzufügen',
      INPUTTAGNAME: 'Tag-Namen eingeben',
      TAGSLIST: 'Tags Liste',
      EXISTTAG: 'Tag mit gleichem Namen bereits vorhanden!',
      ADDEDITLABEL: 'Label hinzufügen / bearbeiten',
      INPUTLABELNAME: 'Label-Namen eingeben',
      LOCATION: 'Standort',
      HEALTHYSTATUS: 'Gesundheitsstatus',
      INFORMHISTORY: 'Informationsverlauf',
      SESSIONLOGINTERVAL: 'Sitzungsprotokoll-Intervall',
      SESSIONLOGINTERVALDESCRIPTION: "Historisches Diagramm des regelmäßigen Berichtintervalls des Geräts an AMP über die letzten 24 Stunden.",
      TIMEINTERVAL_BETWEEN_CONSECUTIVE_INFORMS: "Zeitintervalle zwischen aufeinanderfolgenden Informs",
      LOCATIONDESCRIPTION: "Geräteposition auf der Karte anzeigen.",
      ONLINERATE: 'Online-Rate',
      ONLINERATEDESCRIPTION: 'Das Pentagon-Radar-Diagramm zeigt den Gesundheitsstatus des Geräts an.',
      RESET: 'Zurücksetzen',
      EDITCOMPONENT: 'Widget hinzufügen',
      BULKLOGLIST: 'Bulk-Protokollliste',
      EDITFILE: 'Datei bearbeiten',
      FILTERNODE: 'Knoten filtern',
      SELECTACTION: 'Aktion für ausgewählte Geräte',
      PRODUCTINFO: 'Produktinfo',
      CONNHISTORY: 'Verlauf der Verbindungen',
      LAST24: '(Letzte 24 Stunden)',
      WIFIUSAGE: 'Nutzung des WiFi-Kanals',
      WIFIANALYZER: 'WiFi-Analyzer',
      WIFIANALYZERDESCRIPTION: 'WiFi-RSSI-Diagramm von Nachbar-APs aus verfügbaren Kanälen/Bändern auf diesem Gerät.',
      CELLSTATUS: 'Status der kleinen Zelle',
      CELLSTATUSDESCRIPTION: "Status der kleinen Zelle, wie den Status des Funkdienstes, PC, gNB ID, MCC, MNC und TAC.",
      CELLHANDOVERSTATUS: 'Status der kleinen Zellübergabe',
      CELLHANDOVERSTATUSDESCRIPTION: 'Status der kleinen Zellübergabe.',
      COVERMAP: 'Abdeckungsmap',
      COVERMAPDESCRIPTION: 'Zeigt die Standorte und die Funkabdeckung des Geräts in der Gruppe an.',
      TOTALALARMSDESCRIPTION: "Gesamtzahl der vom Gerät gemeldeten Alarme.",
      ALARMSDESCRIPTION: "Gesamtzahl der Alarme mit unterschiedlicher Schwere, wie kritisch, major, minor und Warnung.",
      CREATEMAP: 'Erstellen',
      SAVEMAP: 'Speichern',
      SELECTIMAGE: 'Bild auswählen',
      IMAGEPRE: 'Bildvorschau',
      MAPX: 'Kartendimensionen (X)',
      MAPY: 'Kartendimensionen (Y)',
      EDITMAP: 'Bearbeiten',
      M: 'm',
      ENGMODE: 'Ingenieurmodus',
      CLIMODE: 'Cli-Modus',
      CONFIG: 'Konfiguration',
      PROVISIONING: 'Provisionierung',
      DEVICEALARM24HRS: 'Alarm innerhalb von 24 Stunden',
      PMALARM24HRS: 'Anomalien im PM-Daten innerhalb der letzten 24 Stunden erkannt.',
      PMSTATUS_GOOD: 'Keine abnormalen PM-Daten innerhalb der letzten 24 Stunden.',
      PMSTATUS_BAD: 'PM-Daten weisen innerhalb der letzten 24 Stunden Anomalien auf.',
      PMSTATUS_NORMAL: 'Keine PM-Daten innerhalb der letzten 24 Stunden verfügbar.',
      PMSTATUS_NOPERMISSION: 'Der Zugriff auf PM-Daten ist eingeschränkt.',
      ADDTOGROUP: 'In eine Gruppe beitreten',
      ADDDEVTOGROUP: 'Gerät zur Gruppe hinzufügen',
      SELECTDEVICE: 'Ausgewähltes Gerät',
      RESTARTNOW: 'Jetzt neu starten, damit die Änderungen wirksam werden.',
      TAGSDESCRIPTION: 'Benutzerdefinierte Tags auf dem Gerät für eine einfachere Geräteverwaltung.',
      BATCHSAVE: 'Batch speichern',
      CPUUSAGEDESCRIPTION: "CPU-Nutzungsprozentsatz des Geräts.",
      MEMORYUSAGEDESCRIPTION: "Speichernutzung des Geräts in Prozent.",
      CPUUSAGECHART_DESCRIPTION: "Historisches Diagramm des CPU-Nutzungsprozentsatzes des Geräts.",
      MEMORYUSAGECHART_DESCRIPTION: "Historisches Diagramm des Speicherverbrauchs des Geräts.",
      KPIDESCRIPTION: "Historisches Diagramm der KPI der kleinen Zelle, wie RRC, UE-Kontext und Durchsatz.",
      PMPARAMDESCRIPTION: "KPI der kleinen Zelle, wie RRC, UE-Kontext und Durchsatz.",
      ALARM_CURRENTNUMBERS: 'Gesamtzahl der vom Gerät gemeldeten Alarme.',
      PRODUCTMODEL: 'Produktmodell',
      PRODUCTMODELDESCRIPTION: 'Gibt an, zu welchem Produkt das aktuelle Gerät gehört.',
      DEVICEOFFLINETIPS: 'Gerät ist jetzt offline, AMP behält die zuletzt vom Gerät gemeldeten Informationen.',
      DEVICENOTREGISTERTIPS: 'Das Gerät ist ein neu hinzugefügtes Gerät und war noch nie online/provisioniert.',
      ONLINESTATUS: 'Online-Status',
      ONLINESTATUSDESCRIPTION: "Historisches Diagramm des Online-Status des Geräts.",
      REGISTERSINGLEDEVICE: "Einzelnes Gerät registrieren",
      REGISTERBATCHDEVICE: "Stapelgerät registrieren",
      DOWNLOADSESSIONGCSV: 'CSV herunterladen (Alle Felder)',
      DOWNLOADSESSIONGJSON: 'JSON herunterladen (Vollständiger Inhalt)',
      DOWNLOADLATESTSESSIONGJSON: 'Laden Sie die neuesten Sitzungsprotokolle herunter',
      TOTALCLIENTHISTORY: 'WiFi assoziierte Clients',
      TOTALCLIENTHISTORY_DESCRIPTION: 'Historisches Diagramm einer Übersicht über die Anzahl der mit WiFi verbundenen Clients dieses Geräts.',
      CLIENTSPERSSID: 'WiFi-Kunden pro SSID',
      CLIENTSPERSSID_DESCRIPTION: 'Historisches Diagramm einer Übersicht über die Anzahl der nach SSID kategorisierten WiFi-assoziierten Clients dieses Geräts.',
      CLIENTSPERRADIO: 'WiFi-Kunden pro Radio',
      CLIENTSPERRADIO_DESCRIPTION: 'Historisches Diagramm einer Übersicht über die Anzahl der nach Radio/Band kategorisierten WiFi-assoziierten Clients dieses Geräts.',
      TRAFFICPERSSID: 'WiFi-Traffic pro SSID',
      TRAFFICPERSSID_DESCRIPTION: 'Historisches Diagramm einer Übersicht über den nach SSID kategorisierten WiFi-Verkehr dieses Geräts.',
      TRAFFICPERRADIO: 'WiFi-Traffic pro Radio',
      TRAFFICPERRADIO_DESCRIPTION: 'Historisches Diagramm einer Übersicht über den nach Radio/Band kategorisierten Verkehr dieses Geräts.',
      VIEWCHART: 'Diagramm anzeigen',
      LOGDETAIL: 'Protokolldetail',
      TXPOWER: 'Sendeleistung',
      BEACONPERIOD: 'Beacon-Periode',
      SSIDSWITHCLIENTS: 'WiFi-SSID-Verteilung',
      SSIDSWITHCLIENTS_DESCRIPTION: 'SSID-Verteilung von verbundenen WiFi-Clients im Netzwerk dieses Geräts.',
      RSSIDISTRIBUTION: 'WiFi RSSI-Verteilung',
      RSSIDISTRIBUTION_DESCRIPTION: 'RSSI-Verteilung von verbundenen WiFi-Clients auf diesem Gerät.',
      WIFIRADIOTHROUGHPUT: 'WiFi-Radio-Datennutzung',
      WIFIRADIOTHROUGHPUT_DESCRIPTION: "Um das aktuelle Protokoll und die Datennutzung des Radios von allen verfügbaren WiFi-Bändern/Schnittstellen anzuzeigen.",
      OPERATINGCHANNELBANDWIDTH: 'Bandbreite',
      BYTESSENT: 'Gesendet',
      BYTESRECEIVED: 'Empfangen',
      PACKETSSENT: 'Gesendete Pakete',
      PACKETSRECEIVED: 'Empfangene Pakete',
      ERRORSSENT: 'Gesendete Fehlerpakete',
      ERRORSRECEIVED: 'Empfangene Fehlerpakete',
      SSIDLIST: 'WiFi SSID-Liste',
      SSIDLIST_DESCRIPTION: "Aktueller WLAN-Status bezüglich Aktivierung, WMM, Bandbreite und Datennutzung auf voreingestellten SSIDs dieses Geräts.",
      MAXCLIENTS: 'Maximale Clients',
      WMM: 'WMM',
      UTILIZATION_GREEN: 'Grün',
      UTILIZATION_YELLOW: 'Gelb',
      UTILIZATION_RED: 'Rot',
      UTILIZATION_GREEN_DESCRIPTION: 'Auslastung <= 30%',
      UTILIZATION_YELLOW_DESCRIPTION: '30% < Auslastung <= 60%',
      UTILIZATION_RED_DESCRIPTION: 'Auslastung > 60%',
      MCS: 'MCS',
      STATUS: 'Status',
      INACTIVE: 'Inaktiv',
      DISABLE: 'Deaktivieren',
      WHITELISTED: 'Auf der weißen Liste',
      REFURBISHMENT: 'Sanierung',
      NETWORKTOPOLOGY: "Netzwerktopologie",
      NETWORKTOPOLOGY_DESCRIPTION: "Baumansicht zur Anzeige aller zugehörigen Knoteninformationen.",
      core_5g: {
        ue_info: 'UE-Info',
        ue_info_DESCRIPTION: 'Die UE-Info bietet detaillierte Informationen über die an das 5G-Kernnetzwerk angeschlossenen Benutzergeräte (UE).',
      },
      healthyStatus: {
        alarm_description: 'Für jede kritische Alarmmeldung, die innerhalb von 24 Stunden auftritt, wird eine Abzug von 10 Punkten angewendet, und für jede wichtige Alarmmeldung wird ein Abzug von 5 Punkten angewendet, und so weiter.',
        session_log_rate_description: 'Das Verhältnis des normalen Betriebszyklus des Geräts zu den Berichten, die in den letzten 24 Stunden an AMP gesendet wurden.',
        online_rate_description: 'Die Online-Rate des Geräts in den letzten 24 Stunden gibt den Prozentsatz der Zeit an, in der das Gerät betriebsbereit und verbunden war.',
        service_quality_description: "Das WiFi-Gerät zieht für jeden derzeit verbundenen schlechten Client 5 Punkte ab, während die Small Cell die durchschnittliche RRC-Rate über 24 Stunden misst, um die Dienstqualität zu bewerten.",
        service_active_description: 'Für jeden Geräte-Neustart (Bootstrap) in den letzten 24 Stunden wird ein Abzug von 20 Punkten angewendet.'
      },
      cableMedemInfo: {
        cable_medem_info: 'DOCSIS-Status',
        cable_medem_info_description: 'Anzeige von Kabelmodeminformationen vom Kabelzugangsnetz'
      },
      ONTMediaInfo: {
        ont_media_info: 'ONT-Medienstatus',
        ont_media_info_description: 'Oktische Medieninformationen von ONT-Geräten anzeigen'
      },
      batteryStatus: {
        battery_information: 'Batteriestatus',
        battery_description: 'Batterieinformationen wie Status, Temperatur und Niveau',
      },
      WIFIAPINFO: 'WiFi AP Informationen',
      WIFIAPINFO_DESCRIPTION: '',
      WIFIAPINFO_APCONFIGVERSION: 'AP Konfiguration',
      APCONFIG: 'AP Konfiguration',
      APNCONFIG: 'APN Konfiguration',
      CONFIGVER: "Konfigurationsversion",
      WIFIAPINFO_APNAME: 'AP Name',
      WIFIAPINFO_APROLE: 'AP Rolle',
      WIFIAPINFO_APNCONFIGVERSION: 'APN Konfiguration',
      EDITAPNAME: 'AP Name bearbeiten',
      WIFIAPINFODESCRIPTION: 'WiFi AP Informationen, einschließlich AP Rolle, AP Name, AP Konfigurationsversion und APN Konfigurationsversion.',
      FIVEGLICENSE: 'Modulare Software-Lizenzen',
      FIVEGLICENSEDESCRIPTION: "Lizenzinformationen der kleinen Zelle, wie Ablaufdatum und unterstützte Elemente.",
      OPERATION_LOCATION: 'Profil Standort',
      OPERATION_SETUPLOCATION: 'Profil Standort Einrichten',
      SEARCH_KEYWORD: "Suchschlüsselwort",
      SERVICE_STATUS: "Dienststatus",
      NCI: 'NCI',
      GNB_ID: 'GNB ID',
      GNB_ID_LENGTH: 'GNB ID Length',
      MCC: 'MCC',
      MNC: 'MNC',
      AMF_IP_CONTROL_PLANE: 'AMF IP-Control-Plane',
      CELL_ID: 'Cell ID',
      TAC: 'TAC',
      NSSAI: 'NSSAI',
      PCI: 'PCI',
      PARAMETER: 'Parameter',
      REPORTINTERVAL: 'Berichtsintervall',
      TIMEREFERENCE: 'Zeitreferenz',
      NUMBEROFRETAINEDFAILEDREPROTS: 'Anzahl der beibehaltenen fehlgeschlagenen Berichte',
      ENCODINGTYPE: "Kodierungstyp",
      REQUESTURIPARAMETER: "URI-Parameter anfordern",
      NOTIFTYPE: 'NotificationType',
      REFERENCELIST: 'Referenzliste',
      RECIPIENT: 'Empfänger',
      COMPLETED_DESCRIPTION: "Gerät hat die von AMP zugewiesenen Operationen abgeschlossen.",
      FAIL_DESCRIPTION: "Alle vom Gerät ausgeführten Operationen sind fehlgeschlagen.",
      PARTIAL_DESCRIPTION: "Teilweise vom Gerät ausgeführte Operationen sind fehlgeschlagen.",
      CANCEL_DESCRIPTION: "Von AMP zugewiesene Operationen wurden vor der Ausführung abgebrochen.",
      PENDING_DESCRIPTION: "Warten darauf, dass das Gerät die von AMP zugewiesenen Operationen empfängt.",
      INPROCESS_DESCRIPTION: "Warten darauf, dass das Gerät das Ergebnis der ausgeführten Operationen an AMP meldet.",
      LOADMAPFAIL: 'Das Laden der Kartenkacheln ist fehlgeschlagen. Bitte versuchen Sie es später noch einmal.',
      MISMATCHPROFILE: 'Die Parameter "conGlobalProfile" und "conDeviceSpecificProfile" stimmen nicht mit der Konfiguration überein.',
      ENERGYSAVING: 'Energieverwaltung',
      STARTTIME_MUST_BE_EARLIER: "Die Startzeit muss vor der Endzeit liegen",
      STARTDATE_MUST_BE_EARLIER: "Das Startdatum darf nicht nach dem Enddatum liegen.",
    },
    GROUPS: {
      WIFICLIENT: 'WiFi-Kunden',
      TITLE: 'Gruppen',
      TOTAL: 'Gesamtanzahl der Gruppen',
      LIST: 'Gruppenliste',
      LISTDESCRIPTION: 'Eine separate Einheit, die Geräte innerhalb von AMP enthält, die spezifische Funktionen wie Batch-Betrieb und Überwachung des Netzwerkstatus ausführt.',
      ADDGROUP: 'Gruppe hinzufügen',
      EDITGROUP: 'Gruppe bearbeiten',
      NAME: 'Gruppenname',
      ACCEPT: 'Akzeptieren',
      MEMBERSTITLE: 'Mitglieder',
      MEMBERS: 'Geräte',
      PENDINGLOGS: 'Liste der ausstehenden Protokolle',
      PENDINGLOGSDESCRIPTION: 'Liste ausstehender Betriebsvorgänge, die auf den Empfang von Geräten warten.',
      OPERATIONLOGS: 'Liste der Betriebsprotokolle',
      OPERATIONLOGSDESCRIPTION: 'Liste von Betriebsvorgängen, die von AMP zugewiesen wurden und die von den Geräten in der Gruppe ausgeführt wurden, zusammen mit den gemeldeten Ergebnissen.',
      IMPORTTYPE: 'Importtyp auswählen',
      SELECTGROUP: 'Gruppennamen auswählen',
      CREATE: 'Erstellen',
      ADDDEVICE: 'Gerät hinzufügen',
      INPUTTYPE: 'Eingabetyp auswählen',
      INPUTVALUE: 'Eingabewert',
      GROUPUPDATE: 'Gruppenupdate',
      FILTERING: 'Beseitigung illegaler Geräte',
      ADDTOGROUP: 'Zu Gerätegruppe hinzufügen',
      SELECTGROUPTYPE: 'Gruppentyp auswählen',
      SEARCH: 'Suchen',
      PENDINGOPER: 'Ausstehender Betrieb',
      PARAMNOTIFICATION: 'Parameterbenachrichtigung',
      SELECTPARAMNOTIFICATION: 'Parameterbenachrichtigung auswählen',
      NONEXISTINGMEN: 'Nicht vorhandene Geräte automatisch filtern',
      BYINPUT: 'Nach Eingabe',
      BYIMPORTFILE: 'Durch Importdatei',
      ADDMEMBER: 'Gerät hinzufügen',
      FILTERMEMBERIN: 'Filterzustand im Produkt',
      ENTERKEYWORDS: 'Stichwörter eingeben',
      BYPRODUCT: 'Nach Produkt',
      CONFIRM: 'Bestätigen',
      DEVICECOUNT: 'Geräte',
      ONLINERATE: 'Online-Rate',
      SERVICEAVAILABILITY: 'Dienstverfügbarkeit',
      CREATEBY: 'Erstellt von',
      CREATETIME: 'Erstellungszeit',
      ALARMCOUNT: 'Alarme',
      ALARMCOUNTDESCRIPTION: 'Gesamtzahl der Alarme mit unterschiedlichen Schweregraden wie Kritisch, Major, Minor und Warnung.',
      DRAGANDDROP: 'Ziehen Sie ausgewählte Operationen per Drag & Drop, um die Ausführungsreihenfolge der Operationen festzulegen',
      GROUPACT: 'Gruppenaktion',
      ADDMEMBY: 'Gerät durch Import hinzufügen',
      ADDMEMINPUT: 'Gerät manuell hinzufügen',
      NETWORKTOPOLOGY: 'Netzwerktopologie',
      CANTDOOPERATION: 'In der Gruppe sind keine Geräte vorhanden',
      ASSIGNOPERATION: 'Operation ausführen',
      REASSIGNOPERATION: "Operation wiederholen",
      DIFFRENTPROTOCOLNOTALLOWED: 'Das Hinzufügen von zwei oder mehr Geräten mit unterschiedlichen Protokollen zu einer Gruppe ist nicht erlaubt',
      CONFIRMADDTOGROUPMEMBER: 'Hinzufügen zur Gruppe bestätigen',
      ONLYVALID: 'Es werden nur legale Geräte hinzugefügt',
      DEVICEERRCODE1: 'Das Produkt enthält dieses Gerät nicht',
      DEVICEERRCODE2: 'Das Gerät existiert bereits in der Gruppe',
      DEVICEERRCODE3: 'Das Gerät ist illegal',
      UE: 'UE',
      UEDESCRIPTION: 'Gesamtzahl der UEs, die mit dem in dieser Gruppe enthaltenen Small Cell verbunden sind.',
      ONLINEDEVICESTITLE: 'Online-Geräte',
      ONLINEDEVICESDESCRIPTION: 'Gesamtzahl der Online-Geräte, die in der Gruppe enthalten sind.',
      WIFICLIENTDESCRIPTION: 'Gesamtzahl der WiFi-Clients, die in der Gruppe enthalten sind.',
      ALARMSDESCRIPTION: 'Gesamtzahl der von den Geräten in der Gruppe gemeldeten Alarme.',
      KPIDESCRIPTION: 'Historisches Diagramm der KPIs des Small Cells wie RRC, UE-Kontext und Durchsatz, die in dieser Gruppe enthalten sind.',
      DEVICESLISTDESCRIPTION: 'Liste aller Geräte, die in dieser Gruppe enthalten sind.',
      GROUPSDELETED: 'Die ausgewählten Gruppen wurden erfolgreich gelöscht.',
      RFMAP_MAX_WARNING: 'Die Abdeckungskarte unterstützt nur bis zu 50 Geräte.',
      SSIDLIST: 'WiFi SSID-Liste',
      SSIDLIST_DESCRIPTION: 'Liste aller SSIDs, die in dieser Gruppe enthalten sind.',
      SSID: 'SSID',
      SECURITY: 'Sicherheit',
      CAPTIVEPORTAL: 'Captive Portal',
      MACACL: 'MAC-ACL',
      ATF: 'ATF aktivieren',
      ATFPERCENTAGE: 'ATF',
      BEAMFORMING: 'Beamforming',
      MAXCLIENTS: 'Maximale Clients',
      WMM: 'WMM',
      BAND: 'Band',
      BANDWIDTHCONTROL: 'Bandbreitenkontrolle',
      DOWNLOAD: 'Gesendet',
      UPLOAD: 'Empfangen',
      FOREIGNAPSLIST: 'Liste der fremden APs',
      FOREIGNAPSLIST_DESCRIPTION: 'WiFi AP-Liste, die nicht in dieser Gruppe enthalten ist.',
      VLAN: 'VLAN',
      BSSID: 'BSSID',
      CHANNEL: 'Kanal',
      RSSI: 'RSSI',
      NEARBYAPS: 'Nahegelegene APs',
      TIME: 'Zeit',
      SSIDSWITHCLIENTS: 'Top 6 WiFi SSIDs mit den meisten Clients',
      SSIDSWITHCLIENTS_DESCRIPTION: 'Die WiFi SSID in dieser Gruppe mit den Top 6 der meisten Client-Verbindungen.',
      APSWITHCLIENTS: 'Top 6 WiFi APs mit den meisten Clients',
      APSWITHCLIENTS_DESCRIPTION: 'Die Top 6 der meistverbundenen WiFi-APs nach Clients.',
      APSWITHTRAFFIC: 'Top 6 WiFi APs mit dem höchsten Traffic',
      APSWITHTRAFFIC_DESCRIPTION: 'Die gesamten numerischen Werte der von den WiFi APs gesendeten und empfangenen Daten, wobei die sechs APs mit den höchsten Werten berücksichtigt werden.',
      CLIENTSPERSSID: "WiFi Clients pro SSID",
      CLIENTSPERSSID_DESCRIPTION: "Historisches Diagramm mit einer Übersicht über die Anzahl der Clientverbindungen nach WiFi SSID für Geräte in der Gruppe.",
      CLIENTSPERRADIO: "WiFi Clients pro Radio",
      CLIENTSPERRADIO_DESCRIPTION: "Historisches Diagramm mit einer Übersicht über die Anzahl der Clientverbindungen nach Radiofrequenz für Geräte in der Gruppe.",
      TRAFFICPERSSID: "WiFi Verkehr pro SSID",
      TRAFFICPERSSID_DESCRIPTION: "Historisches Diagramm mit einer Übersicht über den Verkehr nach SSID für Geräte in der Gruppe.",
      TRAFFICPERRADIO: "WiFi Verkehr pro Radio",
      TRAFFICPERRADIO_DESCRIPTION: "Historisches Diagramm mit einer Übersicht über den Verkehr nach Radiofrequenz für Geräte in der Gruppe.",
      BYTESRECEIVED: 'Empfangen',
      BYTESSENT: 'Gesendet',
      ENABLE: 'Aktivieren',
      DISABLE: 'Deaktivieren',
      TAGSGROUPDESCRIPTION: 'Benutzerdefinierte Tags in der Gruppe zur einfacheren Geräteverwaltung.',
      COUNTRY: 'Land',
      SELECTCOUNTRY: 'Land auswählen',
      STREETADDRESS: 'Straßenadresse',
      CITY: 'Stadt',
      STATE: 'Bundesland',
      ZIPCODE: 'Postleitzahl',
      TAGS: 'Tags',
      INPUTTAGNAME: 'Tag-Namen eingeben',
      ROAD: 'Straße',
      HOUSE: 'Gebäudenummer',
      GROUPDETAILS: 'Gruppendetails',
      GROUPLOCATION: 'Gruppenstandort',
      GROUPLOCATIONDESCRIPTION: 'Standort der Gruppe auf der Karte anzeigen.',
      EDITLOCATION: 'Standort Bearbeiten',
      INPUTLAT: 'Breitengrad Eingeben',
      INPUTLNG: 'Längengrad Eingeben',
      RESET: 'Zurücksetzen',
      LATLNG: 'Breiten- und Längengrad',
      LAT: 'Breitengrad',
      LNG: 'Längengrad',
      LOCATION: 'Standort',
      VIEWLOCATION: 'Gruppenstandort anzeigen',
      VIEWCOVERAGEMAP: 'Abdeckungsbereichskarte anzeigen',
      VIEWLOCATIONDESCRIB: 'Wenn das Gruppenstandort-Widget geöffnet ist, können Benutzer durch Klicken auf das Symbol in der Spalte den Standort und die Informationen der Gruppe anzeigen.',
      VIEWCOVERAGEMAPDESCRIB: 'Wenn das Abdeckungsbereichskarten-Widget geöffnet ist, können Benutzer durch Klicken auf das Symbol in der Spalte die Verteilung und Informationen der in der Gruppe enthaltenen Geräte anzeigen.',
      WIDGETNAME: 'Widget-Name, Klasse oder Unterklasse',
      COVERMAP: 'Abdeckungsmappe',
      COVERMAPDESCRIPTION: 'Zeigt die Standorte und die Funkabdeckung aller Geräte an, die zur Gerätegruppe gehören.',
      TOTALALARMSDESCRIPTION: 'Gesamtzahl der von den Geräten in der Gruppe gemeldeten Alarme.',
      ALARMMGMT: 'Alarmverwaltung',
      ALARMMGMTDESCRIPTION: 'Liste aller von den Geräten in der Gruppe gemeldeten Alarme, einschließlich gelöschter und nicht gelöschter Alarme mit Schweregrad, Ereigniszeit und wahrscheinlicher Ursache.',
      NETWORK: 'Netzwerk'
    },
    PRODUCTS: {
      LIST: 'Produktliste',
      LISTDESCRIPTION: 'Eine separate Einheit, die Geräte innerhalb von AMP enthält und spezifische Funktionen wie Zugriffsliste, standardmäßige Bereitstellungsparameter und das Management von Benutzerkonten umfasst. Es ist erforderlich für die Registrierung und das Onboarding von Geräten.',
      NETWORKRADIOACCESSLIST: 'Liste der Funkzugangsnetze',
      NETWORKRADIOACCESSLISTDESCRIPTION: 'Eine separate Einheit zur Verwaltung spezifischer Geräte wie Small Cells. Eine Gruppe mit demselben Namen wird auch für Netzwerkmanagementzwecke erstellt.',
      NETWORKWIFIAPLIST: 'Liste der WiFi-AP-Netzwerke',
      NETWORKWIFIAPLISTDESCRIPTION: 'Eine separate Einheit zur Verwaltung spezifischer Geräte wie WiFi-APs. Eine Gruppe mit demselben Namen wird auch für Netzwerkmanagementzwecke erstellt.',
      NETWORKWIFIMESHLIST: 'Liste der WiFi-Mesh-Netzwerke',
      NETWORKWIFIMESHLISTDESCRIPTION: 'Eine separate Einheit zur Verwaltung spezifischer Geräte wie WiFi-Mesh-APs. Eine Gruppe mit demselben Namen wird auch für Netzwerkmanagementzwecke erstellt.',
      ACTION: 'Produktaktion',
      ADD: 'Produkt hinzufügen',
      ADDRADIOACCESSNETWORK: 'Add Radio Access Network',
      ADDWIFIAPNETWORK: 'Add WiFi AP Network',
      ADDWIFIMESHNETWORK: 'Add WiFi Mesh Network',
      PERMISSION: 'Berechtigung',
      DEVICEPERMISSION: 'Geräteberechtigung',
      ADDPERMISSION: 'Zulassungsliste',
      PERMITTEDTYPE: 'Zutrittskontrolle',
      PROVISIONINGTYPE: 'Art der Bereitstellung ',
      SELECTTYPE: 'Zutrittskontrolle auswählen',
      ALLOWALL: 'Alle zulassen',
      WHITELIST: 'Zulassungsliste',
      CREATEWHITELIST: 'Zulassungsliste erstellen',
      LABEL: 'Label-Name',
      LABELREQUIRED: 'Dieser Label-Name ist erforderlich!',
      NAMEREQUIRED: 'Dieser Name ist erforderlich!',
      PRODUCTTYPE: 'Produkttyp',
      SELECTPRODUCTTYPE: 'Produkttyp auswählen',
      PRODUCTPICTURE: 'Produktbild',
      PRODUCTNAME: 'Produktname',
      ISPRODUCTNAME: 'Dieser Produktname ist erforderlich!',
      ISPRODUCTCLASS: 'Diese Produktklasse ist erforderlich!',
      TARGETPRODUCT: 'Zielproduktname',
      OBJECTNAME: 'Objektname',
      ENTEROBJECTNAME: 'Objektnamen eingeben',
      PRODUCTDETAILS: 'Produktdetails',
      DETAILS: 'Details',
      CHOOSEFILE: 'Datei auswählen',
      PERMITEDDEVICE: 'Zulässige Geräte',
      DEVICELIMITS: 'Gerätelimits',
      UPDATEDTIME: 'Aktualisierungszeit',
      EDITPRODUCT: 'Produkt bearbeiten',
      EDITRAN: 'Funkzugangsnetzwerk bearbeiten',
      EDITAPN: 'WiFi AP Netzwerk bearbeiten',
      EDITMESH: 'WiFi Mesh Netzwerk bearbeiten',
      PRODUCTMODEL: 'Produktmodell',
      PRODUCTMODELDESCRIPTION: 'Produktmodell, Beschreibung und Bild',
      BYDEFAULT: 'Standardbild auswählen',
      BYUPLOAD: 'Bild hochladen',
      DATACOLLECT: 'Daten sammeln',
      DATACOLLECTDESCRIPTION: 'Schalten Sie dies ein, um mit der Datensammlung zu beginnen. Mit den damit verbundenen KPI-Charts des Geräts kann auf der Seite "Geräteinformationen" ein Widget erstellt werden.',
      DATAEXPORTDESCRIPTION: "Schalten Sie diese Option ein, um Leistungsmetriken an ein Drittanbieter-Telemetriesystem zu exportieren.",
      PRODUCTNOTEXIST: 'Das aktuelle Produkt existiert nicht',
      DEVICETEXIST: 'Das Gerät existiert bereits',
      DEVICEERRCODE1: 'Item contain illegal string',
      DEVICEERRCODE2: 'DelItem doesn not exist',
      DEVICEERRCODE3: "Das Gerät existiert bereits in Produkt:",
      LABELORPATHEXISTS: "Beschriftungsname oder Parameterpfad existiert bereits",
      PARSINGFAILED: "Analyse der Datei fehlgeschlagen.",
      FILETYPEERROR: "Nur CSV-Dateien können hochgeladen werden",
      EXPORTINITDEFAULTTOJSON: "JSON herunterladen (Standardwert zur Bereitstellung)",
      EXPORTINITDEFAULTTOCSV: "CSV herunterladen (Beispiel für die Batch-Registrierung)",
      EDITPROVISIONINGDEFAULTVALUE: "Standardwert für Provisioning bearbeiten",
      TAGS: 'Tags',
      LOCATION: 'Standort',
      REGISTERDEVICE: 'Gerät registrieren',
      NAME: 'Name',
      NAMETOOLTIP: 'Vom Benutzer definierter Produktname eingeben',
      NETWORKNAME: 'Vom Benutzer definierter Netzwerkname eingeben',
      PICTURE: 'Bild',
      UEFORPRODUCT: 'UE-Anzahl pro Produkt',
      OUI: 'Die korrekte OUI gemäß dem Gerät eingeben',
      DESCRIPTION: 'Beschreibung',
      NETWORKDESCRIPTION: 'Merkmale oder proprietäre Attribute der Geräte im Netzwerk eingeben',
      PRODUCTDESCRIPTION: 'Merkmale oder proprietäre Attribute der Geräte im Produkt eingeben',
      PRODUCTCLASS: 'Die korrekte Produktklasse gemäß dem Gerät eingeben',
      PROCPELIMIT: 'Kapazität des Produkts eingeben',
      NETCPELIMIT: 'Kapazität des Netzwerks eingeben',
      OUIDESCRIPTION: 'OUI muss korrekt sein, damit AMP das zugreifende Gerät verifizieren kann',
      ALLOWALLDESCRIPTION: 'OUI und Produktklasse des Geräts sollten verifiziert werden',
      PROALLOWLISTDESCRIPTION: 'OUI, Produktklasse und Seriennummer des Geräts sollten verifiziert werden, nach Erstellung des Produkts muss das Gerät mit der Seriennummer registriert werden',
      NETALLOWLISTDESCRIPTION: 'OUI, Produktklasse und Seriennummer des Geräts sollten verifiziert werden, nach Erstellung des Netzwerks muss das Gerät mit der Seriennummer registriert werden',
      PRODUCTCLASSDESCRIPTION: 'Produktklasse muss korrekt sein, damit AMP das zugreifende Gerät verifizieren kann, z.B.: Femtocell_5G_SA, EAP, etc.',
      PRODEVICELIMITDESCRIPTION: 'Kapazität der Geräte im Produkt',
      NETDEVICELIMITDESCRIPTION: 'Kapazität der Geräte im Netzwerk',
      PROVISIONINGTYPEDESCRIPTION: 'Provisioning-Typ muss korrekt sein, damit AMP das zugreifende Gerät verifizieren kann'
    },
    ALARMS: {
      TOTAL: 'Gesamtanzahl der Alarme',
      ALARMCOUNT: 'Anzahl der kritischen/Wesentlich/Unerheblich/Warnung Alarme',
      CRITICAL: 'Kritisch',
      MAJOR: 'Wesentlich',
      WARNING: 'Warnung',
      INDETERMINATE: 'Unbestimmt',
      MINOR: 'Unerheblich',
      CURRENTNUMBERS: 'Aktuelle Alarmnummern',
      SYSTEM: 'Systemereignisse',
      LIST: 'Liste der Systemereignisse',
      DEVICE: 'Gerätealarme',
      DEVICELIST: 'Liste der Gerätealarme',
      ACKALARM: 'Alarm bestätigen',
      ERRORLIST: 'Gerätfehlerliste',
      DEVICEEVENTTRACKING: 'Geräteereignisverfolgung',
      DEVICEEVENTTRACKINGDESCRIPTION: 'Liste signifikanter Ereignisse, die von AMP im Zusammenhang mit Geräten verfolgt werden, wie Neustart, Offline und Zurücksetzen.',
      NOTIFICATION: 'Benachrichtigung',
      NOTIFICATIONLIST: 'Liste der benachrichtigungen',
      NOTIFICATIONLISTDESCRIPTION: 'Liste von Regeln, die von Benutzern erstellt wurden, um Benachrichtigungen über SMTP oder SNMP-Trap unter bestimmten Bedingungen basierend auf Anforderungen zu erhalten.',
      FORK: 'Gabeln',
      FORKNOTIFICATION: 'Gabeln der benachrichtigungen',
      CLONE: 'Klonen',
      CLONENOTIFICATION: 'Klonen der benachrichtigungen',
      EDITNOTIFICATION: 'Benachrichtigung bearbeiten',
      SETUPDETAILS: 'Details (Setup-Daten)',
      ENTERDETAILS: 'Geben Sie Ihre Details ein',
      GENERIC_TRAP_TYPE: "Generischer Falle-Typ",
      SPECIFIC_TRAP_OID: "Spezifische Falle OID",
      VARIABLE_BINDINGS: 'Variablenbindungen (optional)',
      ALIASVALUE: 'Aliaswert',
      OlDNAME: 'Alt/Name',
      VARIABLE_BINDINGS_DESCRIPTION: 'Die Werte der Varbinds sind mit der in der Stufe konfigurierten Aliasliste verknüpft.',
      TARGET: 'Ziel',
      EDITTARGET: 'Ziel bearbeiten',
      SELECTTARGET: 'Ziel auswählen',
      ENTERTARGET: 'Geben Sie Ihr Ziel ein.',
      TARGETREQUIRED: 'Dieser Dateiname des Ziels ist erforderlich!',
      TARGETTYPE: 'Zieltyp',
      TARGETDEVICESN: 'Zielgerät SN',
      ISTARGETDEVICESN: 'Diese Zielgerät SN ist erforderlich!',
      SCHEDULE: 'Zeitplan',
      EDITSCHEDULE: 'Zeitplan bearbeiten',
      ENTERSCHEDULE: 'Geben Sie Ihren Zeitplan ein.',
      SCHEDULEDOWNLOAD: 'Zeitplandownloaddetails',
      STARTDATE: 'Startdatum',
      ENDDATE: 'Enddatum',
      STARTTIME: 'Startzeit',
      ENDTIME: 'Endzeit',
      ENTEROPERATION: 'Geben Sie Ihre Operation ein',
      TRIGGER: 'Auslösertyp',
      SELECTTRIGGER: 'Auslösertyp auswählen',
      INFORMPARAM: 'Informationsparameter',
      CONDITION: 'Bedingung',
      SELECTCONDITION: 'Bedingung auswählen',
      BUILDCONDITION: 'Bedingung erstellen',
      PARAMCONDITION: 'Parameterbedingung',
      SELECTPARAMCONDITION: 'Parameterbedingung auswählen',
      ATTACHED: 'Angehängte Nachrichten',
      ADDITIONALPARAMINFO: 'Zusätzliche Parameterinformationen',
      ADDITIONALPARAMINFO_DESCRIPTION: 'Detailliertere Informationen, die einen spezifischen Parameterwert in der Benachrichtigungsnachricht enthalten.',
      NODE: 'Knoten',
      ENTERNODE: 'Knoten eingeben',
      SELECTNODE: 'Knoten auswählen',
      VIEWNODE: 'NetConf-Knoten anzeigen',
      REFERNODE: 'Knoten verweisen',
      REFERNODEREQUIRED: 'Dieser Knoten verweisen ist erforderlich!',
      PARENTNODE: 'Übergeordneter Knoten',
      SELECTPARENTNODE: 'Übergeordneten Knoten auswählen',
      CHILDNODE: 'Untergeordneter Knoten',
      ADDCHILDNODE: 'Untergeordneten Knoten hinzufügen',
      SELECTCHILDNODE: 'Untergeordneten Knoten auswählen',
      CHILDCONTENT: 'Inhalt des untergeordneten Knotens',
      CONTENT: 'Inhalt',
      ENTERCONTENT: 'Inhalt eingeben',
      CONFIG: 'Konfigurieren',
      NAMESPACE: 'Namensraum',
      ENTERNAMESPACE: 'Namensraum eingeben',
      ALIAS: 'Alias',
      ENTERALIAS: 'Alias eingeben',
      ADDATTACHED: 'Angehängte Nachricht hinzufügen',
      ADDDEVICEFALUT: 'Gerätefehlerparameter hinzufügen',
      SELECTDEVICEFAULTNAME: 'Gerätefehlername auswählen',
      BUILD: 'Operation erstellen (optional)',
      BUILDREQUIRED: 'Betrieb',
      PROGRESS: 'Fortschritt',
      ACTIONS: 'Aktionen',
      REPEAT: 'Wiederholen',
      REPEATTYPE: 'Wiederholungstyp',
      UPLOADNOTIFI: 'Alarmbenachrichtigung hochladen',
      DROPFILE: 'Dateien hier ablegen oder klicken',
      UPLOADFORMATSARESUPPORTED: 'Unterstützt nur das Hochladen im Format .tar .tar.gz .tgz .zip .gzip.',
      UPLOADALL: 'Alle hochladen',
      UPLOAURL: 'Geräteprotokoll-Upload-URL',
      BANDWIDTH: 'Upload-Bandbreite',
      QUEUEPROGRESS: 'Warteschlangenfortschritt',
      PARAMLIST: 'Parameterliste',
      SELECT: 'Auswählen',
      ENTERSELECT: 'Wählen Sie (XPath-Ausdruck)',
      SOURCE: 'Quelle',
      SELECTSOURCE: 'Quelle auswählen',
      FILTERSTATE: 'Filterstatus',
      SELECTFILTERSTATE: 'Filterstatus auswählen',
      FILTERTYPE: 'Filtertyp',
      SELECTFILTERTYPE: 'Filtertyp auswählen',
      REMOVEALL: 'Alle entfernen',
      REMOVE: 'Entfernen',
      UPDATEUSER: 'Benutzer aktualisieren',
      UPDATEDBY: 'Aktualisiert von',
      LASTACTIVE: 'Letzte Aktivität',
      UPDATELOG: 'Aktualisierungsprotokoll',
      NOTIF: 'Benachrichtigung',
      ONLYONCE: 'Nur einmal',
      ALLSTATE: 'Alle Zustände',
      ALLSEVERITY: 'Alle Schweregrade',
      ALLGROUPS: 'Alle Gruppen',
      SEVERITY: 'Schweregrad',
      STATE: 'Zustand',
      CLEAREDTIME: 'Gelöschte Zeit',
      CLEARED: 'Freigegeben',
      NOTCLEARED: 'Nicht freigegeben',
      CLEAREDALARM: 'Gelöscht',
      UNCLEAREDALARM: 'Nicht gelöscht',
      CHANGEDALARM: 'Geändert',
      NEWALARM: 'Neuer Alarm',
      NOTIFICATIONTYPE: "Benachrichtigungstyp",
      PROBABLECAUSE: "Wahrscheinliche Ursache",
      SPECIFICPROBLEM: "Spezifisches Problem",
      ADDITIONALTEXT: "Zusätzlicher Text",
      ADDITIONALINFORMATION: "Zusätzliche Informationen",
      ALARMID: 'Alarm ID',
      EVENTTYPE: 'Ereignistyp',
      EVENTTIME: 'Ereigniszeit',
      ACKUSER: 'Benutzerbestätigung',
      ACKTIME: 'Bestätigungszeit',
      ERRORCODE: 'Fehler',
      DEVICEFAULTPARAM: 'Gerätefehlerparameter',
      ATTACHEDMES: 'Angehängte Nachricht',
      BYTAG: 'nach Tag',
      RECEIVERLIST: 'Empfängerliste',
      RECEIVERCCLIST: 'Empfänger CC-Liste',
      RECEIVETAGSTOOLTIP: 'Empfänger mit bestimmten Tags hinzufügen',
      RECEIVERCCTAGSTOOLTIP: 'CC mit spezifischen Tags hinzufügen',
      EMAILSUBJECT: 'E-Mail-Betreff',
      EMAILCONTENT: 'E-Mail-Inhalt',
      WITHACTIVEHOURS: 'Mit aktiven Stunden',
      PRIPHONENUM: 'Primärer Telefonnummer',
      SECPHONENUM: 'Sekundäre Telefonnummer',
      TEXTMESSAGE: 'Textnachricht',
      ACK: 'Bestätigen',
      EDIT_STAGE_NOTIFICATIONS: 'Benachrichtigung mit Stufe / Benachrichtigungen erstellen',
      TOTALALARMSDESCRIPTION: 'Gesamtzahl der vom System gemeldeten Alarme.',
      ALARMMGMT: 'Alarmverwaltung',
      ALARMMGMTDESCRIPTION: 'Liste aller vom System gemeldeten Alarme, einschließlich gelöschter und nicht gelöschter Alarme mit Schweregrad, Ereigniszeit und wahrscheinlicher Ursache.',
      ALARMCOUNTDESCRIPTION: 'Gesamtzahl der Alarme mit unterschiedlichen Schweregraden wie Kritisch, Major, Minor und Warnung.',
      TARGETDEVICETAG: 'Zielgerät-Tag',
    },
    PROVISIONING: {
      COLLAPSIBLE: 'Bereitstellung',
      WORKSFLOW: 'Workflows',
      WORKSFLOWLIST: 'Workflow-Liste',
      WORKSFLOWLISTDESCRIPTION: 'Liste von Regeln, die von Benutzern erstellt wurden, um Operationen an spezifischen Geräten unter bestimmten Bedingungen basierend auf Anforderungen durchzuführen.',
      CONFIGURATIONS: 'Konfigurationen',
      CONFIGURATIONLIST: 'Konfigurationsliste',
      CONFIGURATIONLISTDESCRIPTION: 'Liste von Regeln, die automatisch oder von Benutzern erstellt wurden, um Bereitstellungskonfigurationen an Geräten unter bestimmten Bedingungen basierend auf Anforderungen durchzuführen.',
      POLICYLIST: 'Energiepolitik',
      POLICYLISTDESCRIPTION: 'Listet die automatisch erstellten Energiesparregeln auf. Diese Regeln nehmen Konfigurationseinstellungen auf Geräten vor, wenn der Energiesparmodus aktiviert ist.',
      CLONEPOLICY: 'Klonrichtlinie',
      POLICYS: 'Richtlinien',
      FROMWORKSFLOW: 'Von Workflow',
      FROM: 'Von',
      VALIDFROM: 'Gültig ab',
      CLONEWORKSFLOW: 'Workflow klonen',
      CLONECONFIGURATION: 'Klon-Konfiguration',
      EDITWORKSFLOW: 'Workflow bearbeiten',
      FORKWORKSFLOW: 'Workflow verzweigen',
      UPLOADWORKSFLOW: 'Workflow hochladen',
      UPLOADQUEUE: 'Upload-Warteschlange',
      OPERATIONS: 'Operationen',
      PROFILES: 'Profiles',
      ACTIONS: 'Aktionen',
      CLONEOPERATIONS: 'Profile klonen',
      FORKOPERATIONS: 'Gabelprofile',
      EDITOPERATIONS: 'Operationen bearbeiten',
      OPERATIONLIST: 'Profilliste',
      OPERATIONLISTDESCRIPTION: 'Liste von Regeln, die von Benutzern erstellt wurden, um Operationen an spezifischen Geräten unter bestimmten Bedingungen basierend auf Anforderungen durchzuführen.',
      DUOPERATION: 'DU-Operation hinzufügen',
      PARAPATH: 'Parameterpfad',
      ENTERPARAPATH: 'Parameterpfad eingeben',
      ISPARAPATH: 'Parameterpfad ist erforderlich.',
      NEXTLEVEL: 'Nächstes Level',
      PRODUCT: 'Produkt',
      SCRIPTS: 'Skripte',
      SCRIPTLIST: 'Skriptliste',
      EDITSCRIPT: 'Skript bearbeiten',
      SCRIPTNAME: 'Skriptname',
      FILES: 'Dateien',
      FILELIST: 'Dateiliste',
      FILELISTDESCRIPTION: 'Eine von Benutzern erstellte Liste, die erforderliche Informationen enthält, wenn ein Gerät Datei-Uploads oder -Downloads durchführt, wie Dateityp, URL und Authentifizierung.',
      FILETYPE: 'Dateityp',
      SELECTFILETYPE: 'Dateityp auswählen',
      ENTERFILETYPE: 'Dateityp eingeben',
      ISFILETYPE: 'Dieser Dateityp ist erforderlich!',
      ISURL: 'Diese URL ist erforderlich!',
      DELAYSECONDS: 'Verzögerungssekunden',
      TARGETNAME: 'Ziel-Dateiname',
      ENTERTARGETNAME: 'Ziel-Dateiname eingeben',
      FILESIZE: 'Dateigröße',
      DESCRIPTION: 'Beschreibung',
      SUBSCRIBE: 'Abonnieren',
      SUBSCRIBETOPIC: 'Abonnementthema',
      SELECTTOPIC: 'Abonnementthema auswählen',
      VENDORFILE: 'Herstellerspezifische Dateien',
      VENDORFILEDESCRIPTION: 'Eine von Benutzern erstellte Liste, die erforderliche vendor-spezifische Informationen enthält, wenn ein Gerät Datei-Uploads oder -Downloads durchführt, wie Dateityp, URL und Authentifizierung.',
      ADDVENDORFILE: 'Herstellerspezifische Dateien hinzufügen',
      EDITVENDORFILE: 'Herstellerspezifische Dateien bearbeiten',
      LATESTFIRMWARE: 'Neueste Firmware',
      AVAILABLEFILES: 'Verfügbare Dateien',
      SETUPDETAILS: 'Einrichtungsdetails',
      CODEDISTRIBUTION: 'Bereitstellungscodeverteilung',
      OPERATENAME: 'Operate Name',
      ENTEROPERATENAME: 'Operate Name eingeben',
      ADDINFORM: 'Informationsparameter hinzufügen',
      PUBLICTOPIC: 'Veröffentlichen Thema',
      SELECTPUBLICTOPIC: 'Veröffentlichen Thema auswählen',
      ISDATAMODEL: 'Dieses Datenmodell ist erforderlich!',
      CPELIMIT: 'CPE-Grenzen',
      ISCPELIMIT: 'Diese CPE-Grenzen sind erforderlich!',
      SUMMARYACTION: 'Zusammenfassungsaktion',
      ADDSUMMARYREPORT: 'Zusammenfassungsbericht hinzufügen',
      SUMMARYREPORT: 'Zusammenfassungsbericht',
      SUMMARYREPORTSETTING: 'Einstellungen für Zusammenfassungsbericht',
      PLEASESELECTTEMPLATE: 'Bitte wählen Sie zuerst eine Vorlage aus, bevor Sie fortfahren',
      PLEASEFILLPARAMS: 'Bitte füllen Sie den Etikettennamen und den Parameterpfad aus, bevor Sie fortfahren',
      INFORMLIST: 'Informationsparameterliste',
      SELECTTRIGGERREQ: 'Auslöser auswählen (erforderlich)',
      DEVICEPARAMLIST: 'Geräteparameterliste',
      ADDSTAGE: 'Stufe',
      ENTERSTAGENAME: 'Stufennamen eingeben',
      SELECTFILE: 'Datei auswählen',
      SUCCESSURL: 'Erfolgs-URL',
      FAILURL: 'Fehler-URL',
      NOTIFYTYPE: 'Benachrichtigungstyp',
      SELECTNOTIFYTYPE: 'Benachrichtigungstyp auswählen',
      NOTIFYPARAMS: 'Benachrichtigungsparameter',
      SHOWDETAILS: 'Details anzeigen',
      NOTIFYTYPEREQU: 'Benachrichtigungstyp auswählen (erforderlich)',
      EDITNOTIFYPARA: 'Benachrichtigungsparameter bearbeiten',
      OBJECTPATH: 'Objektpfad',
      ENTEROBJECTPATH: 'Objektpfad eingeben',
      ALLOWPAR: 'Teilweise zulassen',
      ADDCREATEOBJ: 'Objekt erstellen hinzufügen',
      ISOBJECTNAME: 'Objektname ist erforderlich!',
      FILETARGET: 'Dateiziel',
      SELECTSN: 'SN auswählen',
      GENERSUMMARYREPORT: 'Zusammenfassungsbericht generieren',
      SCRIPT: 'Skript',
      SELECTSCRIPT: 'Skript auswählen',
      ACTIONSLIST: 'Aktionen Liste',
      PARAMTYPE: 'Parameter Typ',
      ADDITIONALCON: 'Auslösebedingungen',
      ADDCONDITION: 'Zusätzliche Bedingung hinzufügen',
      EDITCONDITION: 'Zusätzliche Bedingung bearbeiten',
      DEVICEPARAMTRIGGER: 'Geräteparameter Auslöser',
      INFORM: 'Informieren',
      DURATION: 'Dauer',
      FIELD: 'Feld',
      TRIGGEREVENTS: 'Auslösende Ereignisse',
      TRIGGERTYPE: 'Auslösertyp',
      INFORMEVENT: 'Informationsereignis',
      SELECTINFORMEVENT: 'Informationsereignis auswählen',
      EVENTNAME: 'Ereignisname',
      ENTEREVENTNAME: 'Ereignisname eingeben',
      PARAMETERSKEY: 'Parameter ParametersKey',
      ENTERPARAMETERSKEY: 'Parameter ParametersKey eingeben',
      PARAMETERSVALUE: 'Parameter ParametersValue',
      ENTERPARAMETERSVALUE: 'Parameter ParametersValue eingeben',
      PROTOCOLVER: 'Protokollversionen',
      SOURCEURL: 'Quell-URL',
      TARGETURL: 'Ziel-URL',
      SESSIONID: 'Sitzungs-ID',
      OPERATEMODE: 'Betriebsmodus auswählen',
      SELECTOPERATEMODE: 'Betriebsmodus auswählen',
      INPUTURLFORMATE: 'Unterstützte Formate: http / https / ftp / ftps / sftp',
      HISTORY: 'Geschichte',
      WORKFLOWHISTORY: 'Workflow-Verlauf',
      CONFIGURATIONHISTORY: 'Konfigurationsverlauf',
      POLICYHISTORY: 'Richtlinienverlauf',
      TRIGGERTIME: "Auslösezeit",
      TARGETPRODUCT: 'Zielprodukt',
      TARGETGROUP: 'Zielgruppe',
      TARGETSOFTWAREVERSION: 'Ziel-Softwareversion',
      TARGETSN: 'Seriennummer des Zielgeräts',
      TARGETSV: 'Softwareversion des Zielgeräts',
      SUPPORTEDPRODUCT: 'Unterstütztes Produkt',
      MANAGEDEDPRODUCT: 'Verwaltetes Produkt',
      ALWAYSACTIVE: "Immer aktiv",
      SELECTACTIVEDATERANGE: "Aktiven Datums- und Zeitbereich auswählen",
      DAYOFWEEK: 'Mit Wochentag',
      WITHONLYONCE: 'Mit nur einmal',
      ACTIVEDATERANGE: 'Aktiver Datumsbereich',
      ACTIVETIMERANGE: 'Aktiver Zeitbereich in Tag',
      EVERYDAY: 'Täglich',
      SUNDAY: 'Sonntag',
      MONDAY: 'Montag',
      TUESDAY: 'Dienstag',
      WEDNESDAY: 'Mittwoch',
      THURSDAY: 'Donnerstag',
      FRIDAY: 'Freitag',
      SATURDAY: 'Samstag',
      EXECUTIONSTATUS: 'Ausführungsstatus',
      EXECUTIONTIME: 'Ausführungszeit',
      EDITACTIONS: 'Aktionen bearbeiten',
      DOWNLOADASMULTI: 'Als mehrere Dateien herunterladen',
      DOWNLOADASONE: 'Als eine Datei herunterladen',
      LASTEXECUTIONTIME: 'Letzte Ausführungszeit',
      SEARCHSN: 'Suche SN',
      ACTIVATE: 'aktivieren',
      DEACTIVATE: 'deaktivieren',
      LOADING: 'Laden',
      STATETYPE: 'Zustandstyp',
      STAGE: 'Stufe',
      EDIT_STAGE_OPERATIONS_DESCRIPTION: 'Workflow mit Stufe / Operationen erstellen',
      EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION: 'Konfiguration mit Stufe / Operationen erstellen',
      EDIT_POLICY_STAGE_OPERATIONS_DESCRIPTION: 'Build-Richtlinie mit Stage/Operations',
      TRIGGERCONDITIONS: 'Auslösebedingungen',
      BUILD: 'Erstellen',
      SETUP_SCHEDULE_DESCRIPTION: 'Aktiven Zeitbereich einrichten',
      INVALID_VALUE_MESSAGE: 'Es gibt ungültige Werte, bitte überprüfen Sie das Formular.',
      RESET_TOOLTIP: 'Auf Standard zurücksetzen',
      RESET_CONFIRM: 'Möchten Sie alle anfänglichen Konfigurationswerte zurücksetzen?',
      COUNT: 'Ausführungshäufigkeit',
      COMPLETEDCOUNT: 'Abgeschlossene',
      PARTIALFAILEDCOUNT: 'Teilweiser Ausfall',
      CANCELEDCOUNT: 'Abbrechen',
      INPROGRESS: 'Bearbeitung im Gange',
      FAILCOUNT: 'Fehlgeschlagen',
      ADDTAGFAIL: 'Tags hinzufügen fehlgeschlagen!',
      ADDTAGSUCC: 'Tags erfolgreich hinzugefügt!',
      DELTAGFAIL: 'Tags löschen fehlgeschlagen!',
      DELTAGSUCC: 'Tags erfolgreich gelöscht!',
      STEPAFTERSUCCESS: "Beenden Sie nachfolgende Aufgabe bei Fehlern",
      WORKFLOWOPERATIONLOG: 'Workflow-Betriebsprotokolle',
      OPERATIONLOGS: 'Betriebsprotokolle'
    },
    USERS: {
      ACCOUNT: 'Konten',
      ONLINEUSERS: 'Online-Benutzer',
      ONLINEUSERSDESCRIPTION: 'Die Gesamtzahl der Benutzer, die derzeit in Benutzung sind oder sich innerhalb einer halben Stunde angemeldet haben',
      PROFILE: 'Profil',
      PROFILEDESCRIPTION: 'Detaillierte Informationen zum aktuell angemeldeten Benutzer.',
      STATUS: 'Status',
      ALLSTATUS: 'All Status',
      ALLTYPE: 'Alle Typen',
      ROLE: 'Rolle',
      ROLELIST: 'Rollenliste',
      ROLELISTDESCRIPTION: 'Liste von Rollen, die zur Steuerung der Benutzerautorisierung verwendet werden.',
      CANNOTFINDROLE: "Rollenberechtigungsliste kann nicht gefunden werden",
      CHANGE: 'Ändern',
      ACCOUNTLIST: 'Kontoliste',
      ACCOUNTLISTDESCRIPTION: 'Liste von Konten, die der aktuell angemeldete Benutzer verwalten kann.',
      EDITUSER: 'Benutzer bearbeiten',
      EXPIRATION: 'Ablaufdatum',
      DEPLOYEXPIRATION: 'Ablaufdatum bereitgestellt',
      CONTROLLIST: 'Zugriffssteuerungsliste',
      ALLEDITABLE: 'Alle bearbeitbar',
      EDITABLE: 'Bearbeitbar',
      ALLREADONLY: 'Alle nur lesbar',
      READONLY: 'Nur lesbar',
      ALLDISABLED: 'Alle Behinderten',
      DISABLED: 'Behindert',
      SUBMIT: 'Senden',
      AMPNODE: 'Und stoppeln!',
      SUPERADMINPASSWORD: 'Code des supermasters.',
      ACTIVITIES: 'Konto-Protokolle',
      ACTIVITIESLIST: 'Kontoaktivitäten Liste',
      ACTIVITIESLISTDESCRIPTION: 'Protokoll aller Anfrageereignisse für Konten, die der aktuell angemeldete Benutzer verwalten kann.',
      DATERANGE: 'Datumsbereich auswählen',
      BEGINTIMEDATERANGE: 'Wähle den Anfangsdatum',
      ENDTIMEDATERANGE: 'Wähle das Enddatum',
      DASHPERMISSION: 'Dashboard-Berechtigung',
      CONFIRMPASSWORD: 'Passwort bestätigen',
      NOTMATCH: 'Passwort und Passwortbestätigung stimmen nicht überein.',
      NOTMATCH2: 'Neues Passwort und Passwortbestätigung stimmen nicht überein.',
      ISPASSWORD: 'Ein Passwort ist erforderlich.',
      ISUSERNAME: 'Der Benutzername ist erforderlich.',
      ADDUSER: 'Benutzer hinzufügen',
      USERROLE: 'Benutzerrolle',
      CONFIRMPSW: 'Passwörter müssen 8-128 Zeichen lang sein und mindestens folgendes enthalten: Buchstaben, Zahlen und Symbole.',
      SPECIALSYMBOLS: 'Es sind nur 1-32 Zeichen aus Buchstaben, Zahlen und Sonderzeichen (@! #? $ / \ _ -.) erlaubt.',
      SPECIALSYMBOLS_NO_DASH: 'Es sind nur 1-128 Zeichen aus Buchstaben, Zahlen und Sonderzeichen (@! #? $ / \ _ .) erlaubt.',
      ADDNEWUSER: 'Neuen Benutzer hinzufügen',
      USERACTION: 'Benutzeraktion',
      LANGUAGE: 'Sprache',
      AUTHORITYLIST: 'Autoritätsliste der Widget-Klasse',
      CHANGEPASSWORD: 'Passwort ändern',
      APIDOCUMENT: 'API-Dokument',
      USERMANUAL: 'Ein bedienungsanleitung.',
      OLDPASSWORD: 'Altes Passwort',
      NEWPASSWORD: 'Neues Passwort',
      PREVIOUSTIME: 'Letzter Anmeldezeit',
      PREVIOUSLOCATION: 'Letzter Anmeldeort',
      LASTLOGINLOCATION: 'Letzter Anmeldeort',
      CURRENTTIME: 'Neuester Anmeldezeit',
      CURRENTLOCATION: 'Neuester Anmeldeort',
      EDITROLE: 'Rolle bearbeiten',
      ADDNEWROLE: 'Neue Rolle hinzufügen',
      AUTHORITY: 'Rollenvorlage',
      EMAIL: 'E-mail',
      ACTIONTYPE: 'Aktionstyp',
      EMAILERROR: 'Das Format der E-mail ist falsch',
      ISMAIL: 'E-Mail ist erforderlich.',
      TAGHASCREATED: 'Dieses Tag wurde von einem anderen Benutzer erstellt',
      DEVICEADMIN_TITLE: 'Geräteverwaltung',
      DEVICEADMIN_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von gerätebezogenen Widgets, die in der Geräteklasse/Geräteverwaltung Unterklasse klassifiziert sind. Enthaltene Widgets: Geräteliste, Live-Update, Neustart, Firmware-Upgrade.',
      PRODUCTADMIN_TITLE: 'Produktverwaltung',
      PRODUCTADMIN_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von produktspezifischen Widgets, die in der Geräteklasse/Produktverwaltung Unterklasse klassifiziert sind. Enthaltene Widgets: Registrierte Geräte, Produktliste.',
      GROUPADMIN_TITLE: 'Gruppenverwaltung',
      GROUPADMIN_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Gruppenbezogenen Widgets, die in der Geräteklasse/Gruppenverwaltung Unterklasse klassifiziert sind. Enthaltene Widgets: Gruppenliste, Gerät zur Gruppe hinzufügen, Operation hinzufügen.',
      GENERALDATA_TITLE: 'Allgemeine Daten',
      GENERALDATA_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von allgemeinen gerätespezifischen Daten-Widgets, die in der Geräteklasse/Allgemeine Daten Unterklasse klassifiziert sind. Enthaltene Widgets: Online-Geräte, Gruppen, Produktmodell, Allgemeine Informationen, KPI.',
      ALARMMANAGEMENT_TITLE: 'Alarmverwaltung',
      ALARMMANAGEMENT_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Alarmverwaltungs-Widgets in einem spezifischen Gerät, das in der Geräteklasse/Alarmverwaltung Unterklasse klassifiziert ist. Enthaltenes Widget: Alarmverwaltung.',
      REMOTETROUBLESHOOTING_TITLE: 'Fernwartung',
      REMOTETROUBLESHOOTING_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von erweiterten Fernwartungs-Widgets, die in der Geräteklasse/Fernwartung Unterklasse klassifiziert sind. Enthaltene Widgets: Terminal, Befehls-XML.',
      DATAMODEL_TITLE: 'Datenmodell',
      DATAMODEL_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von datenmodellspezifischen Widgets, die in der Geräteklasse/Datenmodell Unterklasse klassifiziert sind. Enthaltenes Widget: Datennode, Parameterdaten.',
      NETWORKLOCATION_TITLE: 'Netzwerkstandort',
      NETWORKLOCATION_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von netzwerkspezifischen Standort-Widgets, die in der Geräteklasse/Netzwerkstandort Unterklasse klassifiziert sind. Enthaltene Widgets: Standort, Netzwerktopologie, Abdeckungskarte.',
      LOGCOLLECTION_TITLE: 'Protokollsammlung',
      LOGCOLLECTION_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Protokollsammlungs-Widgets, die in der Geräteklasse/Protokollsammlung Unterklasse klassifiziert sind. Enthaltene Widgets: Sitzungsprotokollliste, Bericht generieren, Betriebsprotokollliste.',
      STATISTICALANALYSIS_TITLE: 'Statistische Analyse',
      STATISTICALANALYSIS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von statistischen Analyse-Widgets, die in der Geräteklasse/Statistische Analyse Unterklasse klassifiziert sind.',
      WIFISPECIFIC_TITLE: 'WiFi-spezifisch',
      WIFISPECIFIC_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von WiFi-spezifischen Widgets, die in der Geräteklasse/WiFi-spezifisch Unterklasse klassifiziert sind. Enthaltene Widgets: WiFi-Clients, WiFi-Radio-Status, WiFi-Analyzer, WiFi-Nachbarliste.',
      CELLULARSPECIFIC_TITLE: 'Mobilfunk-spezifisch',
      CELLULARSPECIFIC_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Mobilfunk-spezifischen Widgets, die in der Geräteklasse/Mobilfunk-spezifisch Unterklasse klassifiziert sind. Enthaltene Widgets: UE, Zellstatus, Antennenstrahl, Nachbar-/PLMN-Liste.',
      PMKPICOUNTER_TITLE: 'PM KPI-Zähler',
      PMKPICOUNTER_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von PM KPI Counter Widgets, die in der Unterklasse Device Class/PM KPI Counter klassifiziert sind. Enthaltene Widgets:PM Chart,PM Parameter.',
      FAPSPECIFIC_TITLE: 'FAP-spezifisch',
      FAPSPECIFIC_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von FAP-spezifischen Widgets, die in der Geräteklasse/FAP-spezifisch Unterklasse klassifiziert sind.',
      APPSPECIFIC_TITLE: 'App-spezifisch',
      APPSPECIFIC_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von app-spezifischen Widgets, die in der Geräteklasse/App-spezifisch Unterklasse klassifiziert sind. Enthaltene Widgets: Anwendungsliste, Dienstanbieter, Status der Geräte-Apps.',
      YANGMODULE_TITLE: 'Yang-Modul',
      YANGMODULE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Yang-Modul-Widgets, die in der Geräteklasse/Yang-Modul Unterklasse klassifiziert sind.',
      POWERSAVING_TITLE: 'Energieeinsparung',
      POWERSAVING_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Energieeinsparung Widgets, die in der Unterklasse Device Class/Energieeinsparung klassifiziert sind. Enthaltene Widgets:Energiemanagement.',
      DOCSIS_TITLE: 'Docsis spezifisch',
      DOCSIS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Docsis spezifisch Widgets, die in der Unterklasse Device Class/Docsis spezifisch klassifiziert sind. Enthaltene Widgets:Docsis-Status.',
      DEVICEALARM_TITLE: 'Gerätealarm',
      DEVICEALARM_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Gerätealarm-Widgets, die in der Alarmklasse/Gerätealarm Unterklasse klassifiziert sind. Enthaltene Widgets: Gesamtalarme, Alarmverwaltung (Herunterladen/Acknowledgement), Geräteereignisverfolgung.',
      NOTIFICATIONMANAGMENT_TITLE: 'Benachrichtigungsverwaltung',
      NOTIFICATIONMANAGMENT_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Benachrichtigungsverwaltungs-Widgets, die in der Alarmklasse/Benachrichtigungsverwaltung Unterklasse klassifiziert sind. Enthaltenes Widget: Benachrichtigungsliste',
      WORKFLOWSETUP_TITLE: 'Workflow/Konfiguration einrichten',
      WORKFLOWSETUP_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Workflow/Konfiguration einrichten Widgets, die in der Bereitstellungsklasse/Workflow/Konfiguration einrichten Unterklasse klassifiziert sind. Enthaltene Widgets: Workflow/Konfigurationsliste, Workflow/Konfigurationsverlauf.',
      OPERATIONSETUP_TITLE: 'Betriebskonfiguration',
      OPERATIONSETUP_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Betriebskonfigurations-Widgets, die in der Bereitstellungsklasse/Betriebskonfiguration Unterklasse klassifiziert sind. Enthaltenes Widget: Aktionsliste.',
      POLICYSETUP_TITLE: 'Richtlinieneinrichtung',
      POLICYSETUP_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Richtlinieneinrichtung Widgets, die in der Unterklasse Bereitstellungsklasse/Richtlinieneinrichtung klassifiziert sind. Enthaltene Widgets:Energiepolitik.',
      SCRIPTSETUP_TITLE: 'Skriptkonfiguration',
      SCRIPTSETUP_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Skriptkonfigurations-Widgets.',
      FILESETUP_TITLE: 'Dateikonfiguration',
      FILESETUP_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Dateikonfigurations-Widgets, die in der Bereitstellungsklasse/Dateikonfiguration Unterklasse klassifiziert sind. Enthaltene Widgets: Dateiliste, Herstellerspezifische Dateien.',
      ACCOUNTADMIN_TITLE: 'Konto Verwaltung',
      ACCOUNTADMIN_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Kontoverwaltungs-Widgets, die in der Benutzerklasse/Konto Verwaltung Unterklasse klassifiziert sind. Enthaltene Widgets: Profil, Kontoliste.',
      ACCOUNTLOG_TITLE: 'Konto Protokoll',
      ACCOUNTLOG_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Kontoprotokoll-Widgets, die in der Benutzerklasse/Konto Protokoll Unterklasse klassifiziert sind. Enthaltenes Widget: Aktivitätenliste.',
      ACCOUNTROLE_TITLE: 'Konto Rolle',
      ACCOUNTROLE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Konto Rollen-Widgets, die in der Benutzerklasse/Konto Rolle Unterklasse klassifiziert sind. Enthaltenes Widget: Rollenliste.',
      DEVICESTATISTICS_TITLE: 'Geräte Statistiken',
      DEVICESTATISTICS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Geräte-Statistik-Widgets, die in der Analyseklasse/Geräte Statistiken Unterklasse klassifiziert sind. Enthaltene Widgets: Online-Geräte, Neue Geräte, Ereigniscodes.',
      SYSTEMSTATISTICS_TITLE: 'System Statistiken',
      SYSTEMSTATISTICS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von System-Statistik-Widgets, die in der Analyseklasse/System Statistiken Unterklasse klassifiziert sind. Enthaltene Widgets: DB-Status, Dauer der Gerätesitzungen, Gerätesitzungsraten, Freier Speicher.',
      PROVISIONINGSTATISTICS_TITLE: 'Bereitstellungs Statistiken',
      PROVISIONINGSTATISTICS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Bereitstellungs-Statistik-Widgets, die in der Analyseklasse/Bereitstellungs Statistiken Unterklasse klassifiziert sind. Enthaltene Widgets: Bereitstellungscode, Softwareversion, SIM-Status.',
      PMSTATISTICS_TITLE: 'PM Statistiken',
      PMSTATISTICS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von PM-Statistik-Widgets, die in der Analyseklasse/PM Statistiken Unterklasse klassifiziert sind. Enthaltene Widgets: PM, PM-Status, Leistungsbericht.',
      SERVERSETTING_TITLE: 'Server Einstellung',
      SERVERSETTING_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Server-Einstellungs-Widgets, die in der Systemklasse/Server Einstellung Unterklasse klassifiziert sind. Enthaltene Widgets: Allgemein, Live-Update, CWMP, Netconf, Leistungsdienst.',
      SERVERPREFERENCE_TITLE: 'Server Einstellungen',
      SERVERPREFERENCE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Server-Einstellungen-Widgets, die in der Systemklasse/Server Einstellungen Unterklasse klassifiziert sind. Enthaltene Widgets: SMTP-Benachrichtigungen, SNMP-Trap-Benachrichtigungen, Berichte, Statistiken, Protokolle.',
      SERVERLICENSE_TITLE: 'Server Lizenz',
      SERVERLICENSE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Server-Lizenz-Widgets, die in der Systemklasse/Server Lizenz Unterklasse klassifiziert sind. Enthaltenes Widget: Lizenz.',
      SERVERREPORT_TITLE: 'Server Bericht',
      SERVERREPORT_DESCRIPTION: 'Berechtigung zum Generieren/Lesen von Server-Bericht-Widgets, die in der Add-On-Klasse/Bericht Export Unterklasse klassifiziert sind. Enthaltene Widgets: Zusammenfassungsbericht generieren.',
      SYSTEMEVENTS_TITLE: 'Systemereignisse',
      SYSTEMEVENTS_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Systemereignis-Widgets, die in der Systemklasse/Systemereignisse Unterklasse klassifiziert sind. Enthaltenes Widget: Systemereignisse, Registrierungsprotokoll.',
      SYSTEMNODES_TITLE: 'Systemknoten',
      SYSTEMNODES_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Systemknoten-Widgets, die in der Systemklasse/Systemknoten Unterklasse klassifiziert sind. Enthaltenes Widget: Knoten.',
      PERSONALTHEME_TITLE: 'Persönliches Thema',
      PERSONALTHEME_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von persönlichen Themen-Widgets, die in der Add-On-Klasse/Persönliches Thema Unterklasse klassifiziert sind.',
      REPORTEXPORT_TITLE: 'Bericht Export',
      REPORTEXPORT_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Bericht Export-Widgets, die in der Add-On-Klasse/Bericht Export Unterklasse klassifiziert sind. Enthaltenes Widget: Zusammenfassungsbericht generieren.',
      SYSTEMINFORMATION_TITLE: 'Systeminformationen',
      SYSTEMINFORMATION_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von AMP-Systeminformations-Widgets, die in der Systemklasse/Systeminformationen Unterklasse klassifiziert sind. Enthaltenes Widget: Systeminformationen.',
      FURBISHMENTSTATISTICS_TITLE: 'Sanierungsstatistiken',
      GENERAL_TITLE: 'Allgemein',
      GENERAL_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen allgemeiner Widgets, die in die 5G Core-Klasse/allgemeine Unterklasse eingestuft sind.',
      UE_TITLE: 'UE',
      UE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von UE-Widgets, die in die 5G Core-Klasse/UE-Unterklasse eingestuft sind.',
      CELL_TITLE: 'Zelle',
      CELL_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Zell-Widgets, die in die 5G Core-Klasse/Zellenunterklasse eingestuft sind.',
      ALARM_TITLE: 'Alarm',
      ALARM_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von Alarm-Widgets, die in die 5G Core-Klasse/Alarm-Unterklasse eingestuft sind.',
      SYSTEM_TITLE: "System",
      SYSTEM_DESCRIPTION: "Berechtigung zum Bearbeiten/Lesen von Aktionen und Widgets im Zusammenhang mit dem 5GC-System.",
      TOTALREQUESTS_TITLE: 'Gesamtanfragen',
      TOTALREQUESTS_DESCRIPTION: 'Anzahl der Gesamtanfragen in den letzten 24 Stunden',
      EXTERNAL_DEVICE_TITLE: 'Gerät',
      EXTERNAL_DEVICE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von NBI-Berechtigungen für externe Klasse/Geräte-Unterklasse.',
      CBSD_DEVICE_TITLE: 'Gerät',
      CBSD_DEVICE_DESCRIPTION: 'Berechtigung zum Bearbeiten/Lesen von NBI-Berechtigungen für CBSD-Klasse/Geräte-Unterklasse.',
      REQUESTSHISTORY_TITLE: "Anfrageverlauf",
      REQUESTSHISTORY_DESCRIPTION: "Historische Grafik der Gesamtanfragen der letzten 24 Stunden",
      IPREQUESTDISTRIBUTION: "IP Request Distribution Map",
      IPREQUESTDISTRIBUTION_DESCRIPTION: "Die 5 IP-Adressen mit den meisten Anfragen in den letzten 24 Stunden",
    },
    ANALYSIS: {
      COLLAPSIBLE: 'Analyse',
      SYSTEM: 'Systemstatistiken',
      SESSIONDURATION: 'Dauer der Gerätesitzungen',
      SESSIONRATE: 'Satzrate der Gerätesitzungen',
      LATENCY: 'Anforderungs-Latenz des Geräts',
      REQUESTRATE: 'Anforderungsrate des Geräts',
      PARSING: 'Anforderungsanalyse des Geräts',
      MEMORY: 'Speichernutzung',
      SPACEUSAGE: 'Speicherplatznutzung',
      CPUUTILIZE: 'CPU-Nutzung',
      MEMORYUSAGECHART: 'Diagramm der Speichernutzung',
      FREEMEMORY: 'Freier Speicher',
      CPUUSAGE: 'CPU-Nutzung',
      CPUUSAGECHART: 'Diagramm der CPU-Nutzung',
      FREEDISK: 'Freier Speicherplatz',
      DEVICE: 'Gerätestatistiken',
      PM: 'PM-Statistiken',
      TOTALDEVICE: 'Gesamtzahl der Geräte',
      NEWDEVICE: 'Neue Geräte',
      SESSIONS: 'Sitzungen',
      EVENTCODE: 'Ereigniscode',
      MEMORYUTILIZATION: 'Speicherverbrauch',
      DISKUTILIZATION: 'Festplattennutzung',
      MEMORYUTILIZATIONDESCRIPTION: 'Historisches Diagramm der Speichernutzung.',
      DISKUTILIZATIONDESCRIPTION: 'Historisches Diagramm der Festplattennutzung.',
      SESSIONDURATIONDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Sitzungsdauer für alle Geräte in regelmäßigen Abständen. Sitzungsdauer: die gesamte Zeit, die in einer Sitzung zwischen dem Gerät und dem AMP verbracht wird.',
      SESSIONRATEDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Sitzungsrate für alle Geräte in regelmäßigen Abständen. Sitzungsrate: die Anzahl der von dem Gerät an AMP pro Sekunde initiierten Sitzungen.',
      LATENCYDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Anforderungs-Latenz für alle Geräte in regelmäßigen Abständen. Anforderungs-Latenz: die gesamte Zeit, die für eine Anfrage zwischen dem Gerät und dem AMP benötigt wird.',
      REQUESTRATEDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Anforderungsrate für alle Geräte in regelmäßigen Abständen. Anforderungsrate: die Anzahl der vom Gerät an AMP pro Sekunde initiierten Anfragen.',
      PARSINGDESCRIPTION: 'Historisches Diagramm der durchschnittlichen Anforderungsanalyse für alle Geräte in regelmäßigen Abständen. Anforderungsanalyse: die gesamte Zeit, die AMP benötigt, um eine vom Gerät initiierte Anfrage zu analysieren.',
      MEMORYDESCRIPTION: 'Historisches Diagramm des freien Speichers.',
      CPUUSAGEDESCRIPTION: 'Historisches Diagramm der CPU-Nutzung.',
      FREEDISKDESCRIPTION: 'Historisches Diagramm des freien Speicherplatzes.',
      TOTALDEVICEDESCRIPTION: 'Historisches Diagramm der Gesamtzahl der Geräte in regelmäßigen Abständen.',
      ONLINEDEVICEDESCRIPTION: 'Historisches Diagramm der Gesamtzahl der Online-Geräte in regelmäßigen Abständen.',
      NEWDEVICEDESCRIPTION: 'Historisches Diagramm der Gesamtzahl der neu registrierten Geräte in regelmäßigen Abständen.',
      SESSIONSDESCRIPTION: 'Historisches Diagramm der Gesamtzahl der Sitzungen für jedes Produkt in regelmäßigen Abständen.',
      EVENTCODEDESCRIPTION: 'Historisches Diagramm der Gesamtzahl der Ereigniscodes für jedes Produkt in regelmäßigen Abständen.',
      PROVISIONING: 'Provisionierungsstatistiken',
      PMSTATISTICS: 'PM-Statistiken',
      STATUSFORDEVICES: 'Status für Online-Geräte',
      RATE: 'Rate',
      NUMBER: 'Anzahl',
      VERSIONDISTRIBUTION: 'Verteilung der Softwareversionen',
      CODEDISTRIBUTION: 'Verteilung der Provisionierungscodes',
      XMPPSTATUS: 'XMPP-Status',
      XMPPSTATUS_DESCRIPTION: 'Status des XMPP-Dienstes.',
      IMSSTATUS: 'IMS-Registrierungsstatus',
      SIMSTATUS: 'SIM-Status',
      IPSECSTATUS: 'IPSec-Tunnelstatus',
      STATUSFORDEVICE: 'Status für die Gesamtheit der Geräte',
      DBSTATUS: 'DB-Status',
      DBSTATUS_DESCRIPTION: 'Status des Datenbankdienstes.',
      SELECTDURATION: 'Dauer auswählen',
      PMSTATUS: 'PM-Status',
      PMSTATUS_DESCRIPTION: 'Status des PM-Dienstes.',
      REFURBISHMENETHISTORY: 'Überholungs-Historie'
    },
    SYSTEM: {
      COLLAPSIBLE: 'System',
      EXTERNALSERVICE: 'Externer Dienst',
      PERFORMANCESERVICE: 'Leistungsdienst',
      PERFORMANCESERVICEDESCRIPTION: 'Einstellungen im Zusammenhang mit PM, wie die Standard-URL für das Hochladen von KPI-Dateien durch Geräte.',
      PREFERENCE: 'Präferenz',
      DBSERVER: 'DB-Server',
      SOURCES: 'Datenquellen',
      SNMPTRAP: 'SNMP-Trap-Benachrichtigungen',
      SNMPTRAP_DESCRIPTION: 'Konfiguration der Benachrichtigung über SNMP.',
      SMTP: 'SMTP-Benachrichtigungen',
      SMTP_DESCRIPTION: 'Konfiguration der Benachrichtigung über SMTP.',
      SMS: 'SMS-Benachrichtigungen',
      STATISTICSPRE: 'Statistiken',
      STATISTICSPRE_DESCRIPTION: 'Einstellung zur Rotation der Datensammlung in AMP.',
      LOGPRE: 'Protokolle',
      LOGPRE_DESCRIPTION: 'Einstellung zur Rotation der Protokolle für Gerätesitzungen und -operationen.',
      FAULT: 'Alarme',
      FAULT_DESCRIPTION: 'Einstellung zur Präferenz von Alarmen, wie automatische Bestätigung und Alarmrotation.',
      REPORTS: 'Berichte',
      REPORTS_DESCRIPTION: 'Einstellung zur Rotation des Gerätesyntheseberichts.',
      SYSREPORT: 'Serverberichtsliste',
      GENERATESYSREPORT: 'Serverbericht generieren',
      CONFIRMDELETE: 'Löschen aller Serverberichte bestätigen',
      DOCONFIRMDELETE: 'Möchten Sie alle Serverberichte löschen?',
      CONFIRMDELETESELECT: 'Löschen der ausgewählten Serverberichte bestätigen',
      DOCONFIRMDELETESELECT: 'Möchten Sie die ausgewählten Serverberichte löschen?',
      DOWNLOADCSV: 'CSV herunterladen',
      DOWNLOADTXT: 'TXT herunterladen',
      DOWNLOADXLSX: 'XLSX herunterladen',
      LICENSE: 'Lizenz',
      LICENSE_DESCRIPTION: 'Informationen zur AMP-Lizenz, wie Status, Typ, unterstütztes Protokoll und Ablauf. Ermöglicht dem Benutzer, die Lizenz zu aktualisieren, um das Ablaufdatum zu verlängern oder mehr Funktionen in AMP zu aktivieren.',
      LICENSETYPE: 'Ausgabe',
      LICENSESTATE: 'Lizenzstatus',
      KEY_DESCRIPTION: "Stellen Sie die Funktionalität zur Aktivierung von AMP nach Eingabe eines Schlüssels zusammen mit schlüsselbezogenen Informationen bereit.",
      CPENUM: 'Gerätekapazität',
      SESSIONNUM: 'Sitzungsnummer',
      REMAININGTIME: 'Verbleibende Zeit',
      VALIDTO: 'Gültig bis',
      DISPLAY: 'Anzeige',
      GENERAL: 'Allgemein',
      GENERAL_DESCRIPTION: 'Allgemeine Systemeinstellungen, wie Sitzungsprotokollierungsstufe, Abmeldezeitlimit und Serverbericht.',
      CWMP: 'CWMP',
      CWMP_DESCRIPTION: 'Einstellungen im Zusammenhang mit dem CWMP-Protokoll, wie Sitzungszeitüberschreitung und Online-Kriterien.',
      NETCONF: 'Netconf',
      NETCONF_DESCRIPTION: 'Einstellungen im Zusammenhang mit dem NETCONF-Protokoll, wie Wiederholungsintervall und Halteintervall.',
      USP: 'USP',
      USP_DESCRIPTION: 'Einstellungen im Zusammenhang mit dem USP-Protokoll, wie über WebSocket oder MQTT.',
      LIVEUPDATE: 'Live-Update',
      LIVEUPDATE_DESCRIPTION: 'Funktionen zur sofortigen Initiierung der Kommunikation zwischen AMP und einem bestimmten Gerät.',
      MQTT: 'System-MQTT',
      FILES: 'Dateien',
      FILES_DESCRIPTION: 'Standarddateieinstellungen des Systems, wie FOTA und URL sowie Authentifizierung von Gerätesitzungen.',
      TELEMETRY: 'System-Telemetrie',
      SUMMARYREPORT: 'Zusammenfassungsbericht',
      SUMMARYREPORT_DESCRIPTION: 'Kartenbezogene Einstellungen, wie Auswahl der Kartendatenanbieter und -quellen.',
      EVENTS: 'Ereignisse',
      SYSEVENTS: 'Systemereignisse',
      SYSEVENTS_DESCRIPTION: 'Liste aller AMP-bezogenen Ereignisse mit detaillierten Informationen wie Schweregrad, spezifisches Problem, wahrscheinliche Ursache und Ereigniszeit.',
      REGISTRATIONLOG: 'Registrierungsprotokoll',
      REGISTRATIONLOG_DESCRIPTION: 'USP MQTT-Registerprotokoll',
      NODES: 'Knoten',
      DEL_TITLE_NODES: 'Möchten Sie den Knoten löschen',
      NODES_DESCRIPTION: 'Liste der AMP-Knoteninformationen wie Knotenname, IP-Adresse, AMP-Version und Betriebszeit.',
      ENTERLICENSEKEY: 'Lizenzschlüssel eingeben',
      LICENSEKEY: 'Lizenzschlüssel',
      KEY: 'Schlüssel',
      UNIQUEID: 'Eindeutige Kennung',
      KEYVALIDITY: 'Schlüsselvalidität',
      EDITURL: 'URL bearbeiten',
      VERIFYSMTP: 'E-Mail-Versand testen',
      VERIFYSNMP: 'SNMP-Versand testen',
      SERVICESTATUSTRACKING: 'Überwachung des Dienststatus',
      KPIFACTORS: 'KPI-Faktoren',
      KPIFACTORSTRACKING: 'KPI-Faktoren zur Überwachung',
      VERIFYXMPP: 'XMPP testen',
      MAP_DESCRIPTION: 'Karte',
      PROCESS: 'Prozess',
      NODEID: 'Knoten-ID',
      SEVERITY: 'Schweregrad',
      LOCATIONMAP: 'Standortkarte',
      LOCATIONMAP_DESCRIPTION: 'Kartenbezogene Einstellungen, wie Auswahl der Kartendatenanbieter und -quellen.',
      FIVECORESERVICE: '5G Core-Dienst',
      FIVECORESERVICE_DESCRIPTION: 'Einstellungen im Zusammenhang mit dem 5G Core-Dienst, wie 5G Core-Anbieter und Server-URL.',
      ENERGYMANAGEMENT: "Energiemanagement",
      ENERGY_DESCRIPTION: "Einstellungen zur Energieverwaltung, wie Leistungsgrenze und Ruheintervall.",
      NIDS: 'NIDS',
      NIDS_DESCRIPTION: 'Netzwerk-Überwachungssystem zur Erkennung von Eindringlingen.',
      PROMETHEUS: 'Metrikensammlung - Prometheus',
      PROMETHEUS_DESCRIPTION: 'Einstellungen im Zusammenhang mit der Datensammlung über Prometheus.',
      PROMETHEUS_PARAMETER_DESCRIPTION: {
        PULL_PATH: 'URLs, von denen Metriken abgerufen werden sollen.',
        USERNAME: 'Benutzername im Authorization-Header für jede Prometheus-Metrik-Pull-Anforderung.',
        PASSWORD: 'Passwort im Authorization-Header für jede Prometheus-Metrik-Pull-Anforderung.',
      },
      KAFKA: 'Metrikensammlung - Kafka',
      KAFKA_DESCRIPTION: 'Einstellungen im Zusammenhang mit der Datensammlung über Kafka.',
      KAFKA_PARAMETER_DESCRIPTION: {
        BROKERS: 'URLs von Kafka-Brokern.',
        TOPIC: 'Kafka-Thema für Produzentennachrichten.',
        ROUTING_KEY: 'Mechanismus zur Nachrichtenweiterleitung.',
        ACCESS_TOKEN: 'Token zur Authentifizierung.'
      },
      NODE_DESCRIPTION: {
        UPGRADE: 'Upgrade.',
        ADDRESS: 'Die adresse.',
        HASHKEY: 'Hash-Schlüssel',
        TYPE: 'Typen?',
        USERNAME: 'benutzername',
        PASSWORD: 'kennwort',
        TARGEVERSION: 'Versionen der kreise',
        COMPOENT: 'Komponente',
        RUNTOOL: 'Ausführungstool',
        UPGRADESUCC: 'Upgrade erfolgreich.',
        UPGRADEFAIL: 'Scheitern beim upgrade!',
        WAITFORUPDATE: 'Herunterladen der Upgrade-Dateien',
        STARTUPDATING: 'Upgrade starten...',
        BEINGUPDATED: 'Upgrade abgeschlossen und warte auf Neustart',
        SERVERRESTART: 'Der Server wird neu gestartet ...',
        TIMEOUT: 'Upgrade-Zeitüberschreitung!',
        ACSNODEREBOOT: 'Und ein einladen der amp-knoten kann zu datenverlust führen.。',
        ACSNODESHUTDOWN: 'Das Herunterfahren dieses AMP-Knotens kann zu Datenverlust führen.',
      },
      SETTING_DESCRIPTION: {
        GENERAL: {
          SESSIONTIMEOUT: 'Die Einstellung ist die konfigurierbare Timeout-Zeit für die CWMP-Sitzung.',
          TRANSACTIONTIMEOUT: 'Die Transaktionszeitüberschreitung ist die Zeitüberschreitung für Anfrage und Antwort.',
          REFRESHINTERVAL: 'Netconf-Wiederholungsintervall',
          KEEPALIVEINTERVAL: 'Netconf-Keepalive-Intervall',
          SESSIONLOGLEVEL: 'Diese Einstellung beeinflusst die detaillierte Ansicht des Gerätesitzungsprotokolls: Wenn ORIGINAL ausgewählt ist, wird die Sitzung als SOAP-Umschläge angezeigt; Wenn RPC ausgewählt ist, als analysierte SOAP-Nachrichten.',
          DEVICE: 'Die Einstellungen zum Ein- und Ausschalten des Sendens von Gerätedaten. Alle folgenden Parameter werden für die Initialisierung des Geräts beim Bootstrap-Vorgang verwendet.',
          DEVICE_XMPP_CONNECTION_1: 'Die Einstellungen zum Ein- und Ausschalten des Sendens von Device.XMPP.Connection.1 für die Bootstrap-Methoden.',
          DEVICE_MQTT_CLIENT_1: 'Die Einstellungen zum Ein- und Ausschalten des Sendens von Device.MQTT.Client.1 für die Bootstrap-Methoden.',
          DEIVCE_DEVICEINFO_XVENDOR_HOLD: 'Die Einstellungen zum Ein- und Ausschalten des Sendens von Device.DeviceInfo.X_VENDOR.HOID für die Bootstrap-Methoden.',
          DEVICE_MANAGEMENTSERVER_PERIODICINFORMTIME: 'Die Einstellungen zum Ein- und Ausschalten des Sendens von Device.ManagementServer.PeriodicInformTime für die Bootstrap-Methoden.',
          TYPEOFINFORMRECEIVEDWITHIN: 'Die Einstellungen werden verwendet, um festzustellen, ob das Gerät online ist.',
          INTERVALOFINFORMRECEIVEDWITHIN: 'Die Einstellungen konfigurieren den Zeitraum für die Abbruchbedingung.',
          SERVERREPORTENABLED: 'Die Einstellungen zum Ein- und Ausschalten der Serverberichterstellung: Wenn eingeschaltet, wird der Serverbericht von der ACS erstellt. Wenn ausgeschaltet, wird der Serverbericht nicht von der ACS erstellt.',
          SERVERREPORTEMAILNOTIFICATION: 'Funktionalität zum einschalten/abschalten Von systemen.',
          SERVERREPORTPERIOD: 'Die Einstellung konfiguriert die Häufigkeit (in Tagen) der Serverberichterstellung.',
          SERVERREPORTCONTENT: 'Und das system meldet sich freiwillig, und mit einem apparat Von amp-godb, XMPP, MQTT und PM erfassen informationen.',
          PRIORITY: 'Definiert die Priorität der IP-Erkennungsquellen des CPE: RemoteAddress - IP wird aus dem angegebenen Datenknoten abgerufen. Eq.Device.DeviceInfo.X_Vendor_GlobalIPAddress; X-Forwarded-For - IP wird aus dem X-Forwarded-For-HTTP-Header abgerufen; Custom - IP wird aus dem benutzerdefinierten HTTP-Header abgerufen, dessen Name in der [Benutzerdefinierten Überschrift] Einstellung angegeben ist.',
          CUSTOMHEADER: 'Name des benutzerdefinierten HTTP-Headers, der die tatsächliche IP-Adresse des CPE enthält.',
          IDLETIMEOUT: 'Ablaufzeit der Systemsitzung',
          SWAGGER_ENABLED: 'Restful-API-Benutzeroberfläche aktivieren/deaktivieren.',
          CLIENT_URL: 'Die URL, mit der sich die CPE mithilfe des CPE-WAN-Managementprotokolls mit dem ACS verbindet.'
        },
        CONNECTIONREQUEST: {
          USERNAME: 'Konfigurierbarer Benutzername für den CR-Mechanismus.',
          PASSWORD: 'Konfigurierbares Passwort für den CR-Mechanismus.',
          RETRYTIMEOUT: 'Konfigurierbares Timeout für den CR.',
          NUMBEROFRETRY: 'Die maximale Anzahl der CR-Versuche.',
          TYPE: 'Der Typ für den CR.',
          // XMPPADDRESS:'',
          XMPPDOMAIN: 'Die Einstellung ist der konfigurierbare Domänenname, der bei der automatischen JID-Erstellung verwendet wird.',
          XMPPPORT: 'Der Port des XMPP-Servers. Standardwert für den ejabberd-Server angegeben.',
          XMPPACSUSERNAME: 'Konfigurierbarer ACS-Benutzername für das XMPP CR-Aufrufen.',
          XMPPACSPASSWORD: 'Konfigurierbares ACS-Passwort für die XMPP CR-Aufrufe.',
          XMPPADMINPORT: 'Der Port des XMPP-Servers für den Serveradministrator. Standardwert für den ejabberd-Server angegeben.',
          XMPPADMINUSERNAME: 'Die Einstellung ist der konfigurierbare XMPP-Administrator-Benutzername, der bei der automatischen XMPP-Benutzerregistrierung verwendet wird.',
          XMPPADMINPASSWORD: 'Die Einstellung ist das konfigurierbare XMPP-Administrator-Passwort, das bei der automatischen XMPP-Benutzerregistrierung verwendet wird.',
          XMPPRESOURCE: 'Konfigurierbarer Ressourcenwert für das XMPP CR.',
          XMPPUSETLS: 'Die Einstellungen zum Ein- und Ausschalten der TLS-Verwendung für das XMPP CR: Wenn eingeschaltet, ist TLS aktiviert. Wenn ausgeschaltet, ist TLS deaktiviert.'
        },
        USP: {
          BINDING: 'Der art der verbindung, WebSocket Oder MQTT.',
          ADDRESS: 'MTP Server-Domäne',
          PORT: 'MTP Verbindungsport',
          APIKEY: "In MQTT wird ein API-Schlüssel verwendet, um den Status des Servers abzufragen.",
          USERNAME: 'Der Benutzername, der vom Broker benötigt wird, falls vorhanden.',
          PASSWORD: 'Das Passwort, das vom Broker benötigt wird, falls vorhanden.',
          USE_TLS: 'MTP über TLS',
          EXADDRESS: 'Die Adresse oder Domäne, unter der das Gerät eine Verbindung zum WebSocket-Dienst/MQTT-Dienst/CWMP-Dienst/XMPP-Dienst herstellen kann.',
          EXPORT: 'Der Port, an dem das Gerät eine Verbindung zum WebSocket-Dienst/mqtt-Dienst herstellen kann.',
          USETLS: 'Ist das Gerät über TLS mit dem WebSocket-Dienst/MQTT-Dienst/CWMP-Dienst/XMPP-Dienst verbunden?.',
          EXURL: 'Die URL, unter der das Gerät eine Verbindung zum CWMP-Dienst herstellen kann.',
        },
        FILES: {
          DOWNLOAD: {
            LATESTFIRMWARE: 'Die Einstellung ermöglicht dem Benutzer, die neueste Firmware-Version für CSR-Benutzer festzulegen.',
            FIRMWARESERVERURL: 'Der Pfad zum Dateiserver, der von APs zum Herunterladen einer Firmware verwendet werden soll.',
            FIRMWARESERVERUSERNAME: 'Benutzername zur Authentifizierung am Dateiserver.',
            FIRMWARESERVERPASSWORD: 'Passwort zur Authentifizierung am Dateiserver.',
          },
          UPLOAD: {
            FILETYPE: 'Dateityp für den Geräte-Upload',
            INSTANCEPATH: 'Der Pfad im Datenmodell für Benutzer kann Protokolldatei hochladen.',
            LOGUPLOADURL: 'Die URL, unter der CSR-Benutzer AP-Protokolldateien hochladen können. Diese Funktion erfordert eine zusätzliche Serverkonfiguration.',
            USERNAME: 'Konfigurierbarer Benutzername für den Datei-Upload.',
            PASSWORD: 'Konfigurierbares Passwort für den Datei-Upload.'
          },
          CONF_DOWNLOAD: {
            DEFAULT_FILE: 'Die ausgewählte Standard-Konfigurationsdatei aus Dateien (Typ: 3 Vendor Configuration File).',
            DEFAULT_FILE_URL: 'Die URL der Standard-Konfigurationsdatei.',
            FILETYPE: 'Downloadtyp der Gerätekonfigurationsdatei.',
            CONFURL: 'Die URL, von der das Gerät die Konfigurationsdatei von AMP herunterladen kann.',
            USERNAME: 'Konfigurierbarer Benutzername für den Gerätedownload.',
            PASSWORD: 'Konfigurierbares Passwort für den Gerätedownload.'
          },
        },
        TELEMETRY: {
          TELEMETRYSERVERREDIRECTION: 'Die Einstellungen zum Ein- oder Ausschalten der Schaltfläche für den Link zu Websites von Drittanbietern auf der Geräteinformationsseite. Wenn sie aktiviert ist, wird die Schaltfläche für den Link zu Websites von Drittanbietern angezeigt. Wenn sie deaktiviert ist, wird die Schaltfläche für den Link zu Websites von Drittanbietern nicht angezeigt.',
          VENDOR: 'Lieferant, der bestimmte Technologien, Dienste oder Produkte bereitstellt, darunter Druid, DNMM, HP und Open 5GC.',
          TYPE: 'Open-Source-Netzwerk-Intrusion-Detection-Systemtyp, Optionen umfassen Suricata',
          SERVERURL: 'Die URL der Website von Drittanbietern.',
          SERVERUSERNAME: 'Konfigurierbarer Benutzername für die Anmeldung auf der Website von Drittanbietern.',
          SERVERPASSWORD: 'Konfigurierbares Passwort für die Anmeldung auf der Website von Drittanbietern.',
          KPIFACTORS: 'KPI-Faktoren können den Status von PM-Parametern basierend auf benutzerdefinierten Regeln überwachen.',
          UEINTERVAL: 'konfigurierbares UE-Timer-Intervall.',
          CELLINTERVAL: 'konfigurierbares Cell-Timer-Intervall.',
          ALARMINTERVAL: 'konfigurierbares Alarm-Timer-Intervall.',
          COMMONINTERVAL: 'konfigurierbares Common Timer-Intervall.',
          APIURL: 'Die Nordgrenz-Schnittstellenadresse des Performance Service.',
          APIUSERNAME: 'Der Benutzername für die Identität der Nordgrenz-Schnittstelle des Performance Service.',
          APIPASSWORD: 'Das Passwort für die Identität der Nordgrenz-Schnittstelle des Performance Service.'
        },
        MAP: {
          TYPE: 'Und der server, und die kappen Von Google maps und der Open street karte. Die standardeinstellung ist google maps.',
          URL: 'Die adresse des servers.',
          APIKEY: 'Danach suchen wir unseren api schlüssel.',
        }
      },
      PREFERENCE_DESCRIPTION: {
        SMTP: {
          MAIL_HEALTHY: 'System-Health-Monitoring wie CPU-Auslastung, Festplattennutzung, Abstürze und Lizenz.',
          MAIL_FORM: 'E-Mail-Sender-Benutzername',
          MAIL_HOST: 'Die SMTP-Serveradresse, die zur Mailbox gehört.',
          MAIL_USERNAME: 'E-Mail-Sender-Adresse',
          MAIL_PASSWORD: 'E-Mail-Sender-Passwort',
          MAIL_PORT: 'E-Mail-Sender-Port',
          MAIL_TO: 'Empfängeradresse',
          MAIL_SMTP_AUTH: 'SMTP-Protokollbezogene Konfiguration, ob eine Authentifizierung erforderlich ist.',
          MAIL_SMTP_SECURITY: 'SMTP-Protokollbezogene Konfiguration.',
          MAIL_TRANSPORT_PROTOCOL: 'Derzeit nicht verwendet',
        },
        SNMPTRAP: {
          SNMPTRAP_TARGET: 'Zieladresse für SNMP-Traps',
          SNMPTRAP_PORT: 'UDP-Port zum Senden von Anfragen, Standardwert ist 161.',
          SNMPTRAP_RETRIES: 'Anzahl der Wiederholungen für den erneuten Versand einer Anfrage, Standardwert ist 1.',
          SNMPTRAP_TIMEOUT: 'Anzahl der Millisekunden, die gewartet werden, bis eine Antwort erfolgt, bevor ein erneuter Versuch oder ein Fehlschlag unternommen wird, Standardwert ist 5000.',
          SNMPTRAP_TRANSPORT: 'Geben Sie das Transportmittel an, das verwendet werden soll, kann entweder udp4 oder udp6 sein, Standardwert ist udp4.',
          SNMPTRAP_TRAPPORT: 'UDP-Port zum Senden von Traps und Informationsnachrichten, Standardwert ist 162.',
          SNMPTRAP_VERSION: 'Entweder snmp.Version1 oder snmp.Version2c',
          SNMPTRAP_BACKOFF: 'Der Faktor, um den die Zeitüberschreitung für jeden erneuten Versuch erhöht wird, Standardwert ist 1 für keine Erhöhung.',
          SNMPTRAP_COMMUNITY: 'Zur Sicherstellung der Kommunikationssicherheit und Authentifizierung verwendet.',
        },
        REPORTS: {
          DEVICE_REPORT_CLEANUP_ENABLE: 'Wenn "An" aktiviert ist, schaltet diese Einstellung die automatische Bereinigung des Speichers für Geräteberichte ein. Wenn "Aus" aktiviert ist, ist die automatische Bereinigung des Speichers für Geräteberichte deaktiviert.',
          DEVICE_REPORT_RETENTION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Einträge im Speicher für Geräteberichte aufbewahrt werden.',
          SERVER_REPORT_CLEANUPZZ_ENABLE: 'Wenn "An" aktiviert ist, schaltet diese Einstellung die automatische Bereinigung des Speichers für Serverberichte ein. Wenn "Aus" aktiviert ist, ist die automatische Bereinigung des Speichers für Serverberichte deaktiviert.',
          SERVER_REPORT_RETNETION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Einträge im Speicher für Serverberichte aufbewahrt werden.'
        },
        STATISTICS: {
          CPU_COLLECTION_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz die Erfassung von CPU-Metriken. Wenn deaktiviert, erlaubt die Lizenz die Erfassung von CPU-Metriken nicht.',
          DISK_COLLECTION_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz das Sammeln von Datenträgermetriken. Wenn deaktiviert, erlaubt die Lizenz das Sammeln von Datenträgermetriken nicht.',
          MEMORY_COLLECTION_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz das Sammeln von Speichermetriken. Wenn deaktiviert, erlaubt die Lizenz das Sammeln von Speichermetriken nicht.',
          REPORT_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz das Reporting von statistischen Indikatoren. Wenn deaktiviert, erlaubt die Lizenz keine Meldung statistischer Indikatoren.',
          REPORT_PERIOD: 'Die Einstellung legt die Häufigkeit fest, mit der Metriken gesammelt werden sollen.',
          PM_KPI_COLLECTION_RETENTION: 'Die Einstellung gibt die Anzahl der Tage an, für die PM-KPI-DB-Daten aufbewahrt werden sollen.',
          PM_KPI_FILE_RETENTION: 'Die Einstellung gibt die Anzahl der Tage an, die PM-KPI-Dateien aufbewahrt werden sollen.'
        },
        LOGKONFIG: {
          DEVICE_GROUP_OPERATION_LOG_CLEANUP_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz die automatische Bereinigung von Gerätegruppenoperationen. Wenn deaktiviert, erlaubt die Lizenz keine automatische Bereinigung von Gerätegruppenoperationen.',
          DEVICE_GROUP_OPERATION_LOG_RETENTION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Daten zu Gruppenoperationen in Protokollen aufbewahrt werden sollen. ',
          DEVICE_OPERATION_LOG_CLEANUP_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz die automatische Bereinigung von Geräteoperationen. Wenn deaktiviert, erlaubt die Lizenz keine automatische Bereinigung der Gerätebetriebsdaten.',
          DEVICE_OPERATION_LOG_RETENTION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Daten zu Geräteoperationen in Protokollen aufbewahrt werden sollen.',
          SESSION_LOG_CLEANUP_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz die automatische Bereinigung des Ereignisprotokolls. Wenn deaktiviert, erlaubt die Lizenz keine automatische Bereinigung des Ereignisprotokolls.',
          SESSION_LOG_RETENTION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Sitzungsaufzeichnungen in Protokollen aufbewahrt werden sollen.',
          STATISTICS_LOG_CLEANUP_ENABLE: 'Wenn aktiviert, erlaubt die Lizenz die automatische statistische Protokollbereinigung. Wenn deaktiviert, erlaubt die Lizenz keine automatische statistische Protokollbereinigung.',
          STATISTICS_LOG_RENTENTION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Statistikprotokolle aufbewahrt werden sollen.'
        },
        FAULTMANAGEMENT: {
          EVENT_ALARM_ACK_ENABLE: 'Wenn "An" aktiviert ist, schaltet diese Einstellung die automatische Bestätigung von Ereignissen ein. Wenn "Aus" aktiviert ist, ist eine manuelle Bestätigung erforderlich.',
          EVENT_ALARM_CLEANUP_ENABLE: 'Wenn "An" aktiviert ist, schaltet diese Einstellung die automatische Bereinigung von Ereignisprotokollen ein. Wenn "Aus" aktiviert ist, ist die automatische Bereinigung von Ereignisprotokollen deaktiviert.',
          EVENT_ALARM_EMAIL_ENABLE: 'Wenn "An" aktiviert ist, schaltet diese Einstellung die automatische E-Mail-Benachrichtigung bei Ereignissen ein. Wenn "Aus" aktiviert ist, erfolgt keine E-Mail-Benachrichtigung über Alarme.',
          EVENT_ALARM_RETENTION_PERIOD: 'Die Einstellung legt die Anzahl der Tage fest, an denen Ereignisprotokolle aufbewahrt werden sollen.'
        }
      }
    },
    COMMON: {
      DEVICES: 'Geräte',
      DEVICE: 'Gerät',
      CLIENTS: 'Clients',
      CLIENT: 'Client',
      USERS: 'Benutzer',
      ALARMS: 'Alarme',
      TOTALALARMS: 'Gesamtalarme',
      HISTORYALARMS: 'Historische Alarme',
      CRITICALALARMS: 'Kritische Alarme',
      MAJORALARMS: 'Hauptalarme',
      WARNINGALARMS: 'Warnungs-Alarme',
      MINORALARMS: 'Minore Alarme',
      PRODUCTS: 'Produkte',
      PRODUCTSDISTRIBUTION: 'Produktaufteilung',
      REGISTERDEVICECOUNT: "Registrierte Geräteanzahl",
      REGISTERDEVICECOUNTDISTRIBUTION: "Verteilungsdiagramm der Anzahl der Geräte pro Produkt",
      ONLINEDEVICE: 'Online-Geräte',
      HISTORYONLINEDEVICE: "Verlauf der Online-Geräte",
      APPLY: 'Anwenden',
      DELETE: 'Löschen',
      DELETEALL: 'Alle löschen',
      CANCEL: 'Abbrechen',
      OK: 'OK',
      CLOSE: 'Schließen',
      ADD: 'Hinzufügen',
      EDIT: 'Bearbeiten',
      Fail: 'Fehlgeschlagen',
      SERIAL_NUMBER: 'Seriennummer',
      PRODUCT_CLASS: 'Produktklasse',
      ACTION: 'Profile',
      NEW: 'Neu',
      SELECTACTION: 'Aktion auswählen',
      IMPORT: 'Importieren',
      DOWNLOAD: 'Herunterladen',
      DOWNLOADLOG: 'Logbücher runterladen',
      SAVE: 'Speichern',
      DONTSAVE: 'Ne pas sauvegarder',
      UPLOAD: 'Hochladen',
      NAME: 'Name',
      ENTERNAME: 'Name eingeben',
      VERSION: 'Version',
      PRIORITY: 'Priorität',
      ENTERVERSION: 'Version eingeben',
      SOFTVERSION: 'Softwareversion',
      TYPE: 'Typ',
      SELECTTYPE: 'Typ auswählen',
      PREVIOUS: 'Vorherige',
      NEXT: 'Nächste',
      USERNAME: 'Benutzername',
      PASSWORD: 'Passwort',
      USERNAME1: 'Benutzername',
      PASSWORD1: 'Benutzerpasswort',
      ENTERUSERNAME: 'Benutzernamen eingeben',
      ENTERPASSWORD: 'Passwort eingeben',
      UPDATE: 'Aktualisieren',
      UNINSTALL: 'Deinstallieren',
      PARAMETERS: 'Parameter',
      PARAMNAME: 'Parameterpfad',
      ENTERPARAMNAME: 'Geben Sie den Parameterpfad ein',
      PARAMTYPE: 'Parametertyp',
      SELECTPARAMTYPE: 'Parametertyp auswählen',
      PARAMVALUE: 'Parameterwert',
      ENTERPARAMVALUE: 'Parameterwert eingeben',
      ADDPARAM: 'Parameter hinzufügen',
      EXECUTE: 'Ausführen',
      SIZE: 'Größe',
      CANCELALL: 'Alle abbrechen',
      FIELDREQUIRED: 'Dieses Feld ist erforderlich!',
      DETAILS: 'Details',
      SELECTPRODUCTNAME: 'Produktname auswählen',
      SELECTPRODUCT: 'Produkt auswählen',
      AND: 'Und',
      EDITPARAM: 'Parameter bearbeiten',
      VALUE: 'Wert',
      EXPANDCOLLROW: 'Zeile erweitern/zusammenklappen',
      PORT: 'Port',
      HOST: 'Host',
      THECUSTOMIZE: 'Themenanpassung',
      CUSTOMIZEREALTIME: 'Anpassen und in Echtzeit anzeigen',
      SKIN: 'Design',
      LIGHT: 'Hell',
      BORDERED: 'Gerahmt',
      DARK: 'Dunkel',
      RED: "Rot",
      BLUE: "Blau",
      SEMIDARK: 'Halbdunkel',
      ROUTETRA: 'Routenübergang',
      FADEINLEFT: 'Von links einblenden',
      ZOOMIN: 'Hineinzoomen',
      FADEIN: 'Einblenden',
      NONE: 'Keine',
      MENULAYOUT: 'Menülayout',
      VERTICAL: 'Vertikal',
      HORIZONTAL: 'Horizontal',
      MENUCOLL: 'Menü eingeklappt',
      MENUHIDDEN: 'Menü versteckt',
      NAVBARCOLOR: 'Farbe der Navigationsleiste',
      NAVBARTYPE: 'Typ der Navigationsleiste',
      MENYTYPE: 'Menütyp',
      FLOATING: 'Schwebend',
      STICKY: 'Sticky',
      STATIC: 'Statisch',
      FOOTERTYPE: 'Fußzeilentyp',
      WIDGETS: 'Widgets anpassen',
      EDITMODE: 'Widget-Bearbeitungsmodus',
      CUSWIDGETS: 'Widgets anpassen',
      LOGOUT: 'Abmelden',
      RECENTNOTIF: 'Aktuelle Benachrichtigungen',
      NOTIFICATIONS: 'Benachrichtigungen',
      READMORE: 'Mehr lesen',
      TOTAL: 'Gesamt',
      SELECTED: 'ausgewählt',
      CREATED: 'Erstellt am',
      SELECTCOLUMN: 'Spalte auswählen',
      ACTIVE: 'Aktiv',
      ALLOW: 'Zulassen',
      YES: 'Ja',
      CLIENTLIST: 'Liste der Clients',
      WIFICLIENTLIST: 'Liste der WLAN-Clients',
      WIFICLIENTLIST_DESCRIPTION: 'Liste aller in dieser Gruppe enthaltenen WiFi-Clients.',
      WIFICLIENTLISTDESCRIPTION: 'Aktueller WiFi-Status von verbundenen Clients auf verfügbaren Funkgeräten/Bändern dieses Geräts.',
      ONLINE: 'Online',
      OFFLINE: 'Offline',
      EXPORT: 'Exportieren',
      MQTT: 'MQTT',
      CWMP: 'CWMP',
      NETCONF: 'NETCONF',
      CURRENTNODE: 'Aktueller Knoten',
      CHILDNODE: 'Untergeordneter Knoten',
      EDITSTAGE: 'Stadiennamen bearbeiten',
      STAGENAME: 'Stadienname',
      ENTERSTAGENAME: 'Stadiennamen eingeben',
      OPERATIONNAME: 'Operationsname',
      ADDOPERATION: 'Operation hinzufügen',
      ALLPROTOCOL: 'Alle Protokolle',
      ALLPRODUCTS: 'Alle Produkte',
      ALLEVENT: 'Alle Ereignisse',
      USER: 'Benutzer',
      ALLFILETYPES: 'Alle Dateitypen',
      ALLTARGETTYPES: 'Alle Zieltypen',
      TRANSMISSTIONTYPE: 'Übertragungsart',
      SELECTTRANSMISSTIONTYPE: 'Getriebetyp auswählen',
      REMOVEFROMGROUP: 'Aus Gruppe entfernen',
      SHUTDOWN: 'Herunterfahren',
      NOPERMISSION: 'Der aktuelle Benutzer hat keine Berechtigung, den Inhalt dieser Seite zu lesen.',
      SEPARATED_BY_SEMICOLONS: 'Durch Semikolons getrennt',
      SEPARATED_BY_COMMAS: 'Durch Kommas getrennt',
      MAIL_SEPARATED_BY_SEMICOLONS: 'Durch Semikolons getrennt (<EMAIL>;<EMAIL>;)',
      SN_SEPARATED_BY_COMMAS: 'Durch Kommas getrennt (sn1,sn2)',
      WIDGETNAME: 'Geben Sie den Widget-Namen, die Klasse oder Unterklasse ein.',
      APNAMEEDITSUCC: 'AP Name erfolgreich bearbeitet!',
      APNAMEEDITFAIL: 'Fehler beim Bearbeiten des AP Namens.',
      LOCATE: 'Lokalisieren',
      RELOAD: "Lade sie hoch.",
      DATA: "Daten",
      STATE: "Zustand",
      REGISTER: 'Registrieren',
      GROUP: 'Gruppe',
      SELECTCHARTTYPE: 'Diagrammtyp auswählen',
      OPEN_MAXIMIZE: "Maximieren öffnen",
      SELECTORENTER: 'Bitte wählen oder geben Sie Ihre Option ein',
      INVALIDFILETYPE: 'Ungültiger Dateityp. Bitte wählen Sie einen der folgenden Typen: ',
    },
    CONFIRM: {
      CONF: 'Bestätigen Sie die ',
      REMOVAL: ' Entfernung?',
      REBOOT: ' Neustart?',
      SHUTDOWN: ' Herunterfahren?',
      ADDFAIL: 'Hinzufügen fehlgeschlagen!',
      NAMEEXIST: 'Name existiert bereits!',
      ALARMNOTIF: 'Alarmbenachrichtigungen erfolgreich aktualisiert',
      CONFREMGROUP: 'Gruppenentfernung bestätigen?',
      CONFGROUP: 'Gruppenentfernung bestätigen?',
      CONFGROUPS: 'Bestätigen Sie die Entfernung von Gruppen?',
      DOGROUP: 'Möchten Sie ',
      FROMGROUP: ' aus der Gruppe entfernen?',
      IMPORTSUCCESS: "Erfolgreich importiert!",
      IMPORTFAIL: 'Import fehlgeschlagen!',
      FILEEMPTY: 'Die Datei ist leer',
      NOTSUPPORT: 'Dieses Dateiformat wird nicht unterstützt',
      DODELETEGROUP: 'Möchten Sie die Gruppe löschen ',
      DODELETEGROUPS: 'Möchten Sie diese Gruppen löschen?',
      GROUPOPER: 'Gerätegruppenoperationen!',
      WORKFLOWOPER: 'Geräte-Workflow-Aktionen!',
      WORKFLOWOPERATION: 'Bedienung des Geräte-Workflows',
      WORKFLOWDOREMOVEALL: 'Möchten Sie alle Einträge aus dem Gruppenbetriebsprotokoll entfernen?',
      WORKFLOWCLEANSUCC: 'Das Protokoll der Gerätegruppenoperationen wurde erfolgreich bereinigt',
      WORKFLOWNOTCLEAN: 'Die Bereinigung der Gerätegruppenoperationen wurde nicht durchgeführt',
      SETGROUPOPERSUCC: 'Gruppenleiterin ausgeführt!',
      RENAMESUCC: 'Umbenennen erfolgreich',
      CONFNIT: 'Bestätigen Sie den Download von Alarmbenachrichtigungen?',
      DODOWNLOADNIT: 'Möchten Sie die Alarmbenachrichtigungen herunterladen ',
      ALARMNIT: 'Benachrichtigungen',
      DOWNLOADSUCC: 'Erfolgreich heruntergeladen!',
      PLESELECT: 'Bitte wählen Sie zuerst Alarmbenachrichtigungen aus!',
      CONFDOWNLOADNIT: 'Bestätigen Sie den Download der ausgewählten Alarmbenachrichtigungen?',
      DODOWNLOADSELECT: 'Möchten Sie die ausgewählten Alarmbenachrichtigungen herunterladen?',
      DOWNLOADSELECT: 'Die ausgewählten Alarmbenachrichtigungen wurden erfolgreich heruntergeladen!',
      DOWANT: 'Möchten Sie ',
      THEALARMNIT: ' die Alarmbenachrichtigung',
      SUCC: ' erfolgreich!',
      CONFDELETENIT: 'Bestätigen Sie das Löschen von Alarmbenachrichtigungen?',
      DODELETENIT: 'Möchten Sie die Alarmbenachrichtigung löschen',
      NITDELSUCC: 'Alarmbenachrichtigung erfolgreich gelöscht',
      NITID: 'Alarmbenachrichtigung (ID: ',
      WANDEL: ') wurde gelöscht!',
      NITDELETEFAIL: 'Löschen der Alarmbenachrichtigung fehlgeschlagen',
      NOTDEL: ') wurde nicht gelöscht!',
      SELECTFIRST: 'Bitte wählen Sie zuerst Alarmbenachrichtigungen aus!',
      STATEITEMS: 'Die ausgewählten Alarmbenachrichtigungen enthalten einen oder mehrere aktive Zustandselemente!',
      CONFSELECTNIT: 'Bestätigen Sie das Löschen der ausgewählten Alarmbenachrichtigungen?',
      DOSELECTNIT: 'Möchten Sie die ausgewählten Alarmbenachrichtigungen löschen?',
      SELECTNITSUCC: 'Die ausgewählten Alarmbenachrichtigungen wurden erfolgreich gelöscht!',
      GROUPOPERATION: 'Gerätegruppenoperation ',
      CANCELSUCC: ' wurde erfolgreich abgebrochen',
      REMOVEDLOG: ' wurde aus dem Protokoll entfernt',
      CONFCLEANUP: 'Bestätigen Sie die Protokollbereinigung',
      DOREMOVEALL: 'Möchten Sie alle Einträge aus dem Gruppenbetriebsprotokoll entfernen?',
      GROUPCLEANSUCC: 'Das Protokoll der Gerätegruppenoperationen wurde erfolgreich bereinigt',
      GROUPNOTCLEAN: 'Die Bereinigung der Gerätegruppenoperationen wurde nicht durchgeführt',
      CONFPRODUCTREM: 'Bestätigen Sie die Entfernung des Produkts?',
      DOPRODUCT: 'Möchten Sie das Produkt löschen ',
      CONFRANREM: 'Bestätigen Sie die Entfernung des Radiozugangsnetzwerks?',
      CONFAPNREM: 'Bestätigen Sie die Entfernung des WiFi AP Netzwerks?',
      CONFMESHREM: 'Bestätigen Sie die Entfernung des WiFi Mesh Netzwerks?',
      DORAN: 'Möchten Sie das Radiozugangsnetzwerk löschen ',
      DOAP: 'Möchten Sie das WiFi AP Netzwerk löschen ',
      DOMESH: 'Möchten Sie das WiFi Mesh Netzwerk löschen ',
      CONFPRODUCTBAN: 'Produktsperrung bestätigen',
      CONFRANBAN: 'Bestätigen Sie das Verbot des Funkzugangsnetzes',
      CONFAPNBAN: 'Bestätigen Sie das Verbot des WiFi-AP-Netzwerks',
      CONFMESHNBAN: 'Bestätigen Sie das Verbot des WiFi-Mesh-Netzwerks',
      PRODUCTACCESS: '? Geräte dieses Produkts können nicht mehr auf den Server zugreifen.',
      PRODUCTSACCESS: '? Geräte dieser Produkte können nicht mehr auf den Server zugreifen.',
      RANSACCESS: '? Geräte dieses Funkzugangsnetzes können nicht auf den Server zugreifen.',
      APSACCESS: '? Geräte dieses WiFi-AP-Netzwerks können nicht auf den Server zugreifen.',
      MESHSACCESS: '? Geräte dieses WiFi-Mesh-Netzwerks können nicht auf den Server zugreifen.',
      CONFFILE: 'Bestätigen Sie die Dateientfernung?',
      DELFILESUCC: 'Datei erfolgreich gelöscht',
      CONFSCRIPT: 'Bestätigen Sie das Entfernen des Skripts?',
      DOSCRIPT: 'Möchten Sie das Skript löschen ',

      CONFFLOW: 'Bestätigen Sie den Download des Workflows?',
      WORKFLOW: 'Workflow',
      DOWORKFLOW: 'Möchten Sie den Workflow herunterladen ',
      CONFSELECT: 'Bestätigen Sie den Download der ausgewählten Workflows?',
      DOSELECTFLOW: 'Möchten Sie die ausgewählten Workflows als mehrere Dateien herunterladen?',
      DOSELECTFLOWASONE: 'Möchten Sie die ausgewählten Workflows als eine Datei herunterladen?',
      DOWNSELECTFLOW: 'Die ausgewählten Workflows wurden erfolgreich heruntergeladen!',
      DOWNSELECTFILESUCCESS: 'Laden Sie die ausgewählten Dateien erfolgreich herunter!',
      DELSELECTFLOW: 'Die ausgewählten Workflows wurden erfolgreich gelöscht!',
      PLEASEFLOWS: 'Bitte wählen Sie zuerst Workflows aus!',
      THEFLOW: ' den Workflow ',
      CONFDELFLOW: 'Bestätigen Sie das Löschen des Workflows?',
      DODELFLOW: 'Möchten Sie den Workflow löschen',
      DELSUCC: 'Workflow erfolgreich gelöscht',
      FLOWID: 'Workflow (ID: ',
      FLOWDELFAIL: 'Löschen des Workflows fehlgeschlagen',
      FLOWITEM: 'Die ausgewählten Workflows enthalten einen oder mehrere aktive Zustandselemente',
      CONFSELECTFLOW: 'Bestätigen Sie das Löschen der ausgewählten Workflows?',
      DODELSELECTFLOW: 'Möchten Sie die ausgewählten Workflows löschen?',

      CONFCONFIGURATION: 'Bestätigen Sie den Download des Konfigurationen?',
      CONFIGURATION: 'Konfiguration',
      DOWORKCONFIGURATION: 'Möchten Sie den Konfiguration herunterladen ',
      CONFSELECTCONFIGURATION: 'Bestätigen Sie den Download der ausgewählten Konfigurationen?',
      DOSELECTCONFIGURATION: 'Möchten Sie die ausgewählten Konfigurationen als mehrere Dateien herunterladen?',
      DOSELECTCONFIGURATIONASONE: 'Möchten Sie die ausgewählten Konfigurationen als eine Datei herunterladen?',
      DOWNSELECTCONFIGURATION: 'Die ausgewählten Konfigurationen wurden erfolgreich heruntergeladen!',
      DELSELECTCONFIGURATION: 'Die ausgewählten Konfigurationen wurden erfolgreich gelöscht!',
      PLEASECONFIGURATIONS: 'Bitte wählen Sie zuerst Konfigurationen aus!',
      THECONFIGURATION: ' den Konfiguration ',
      CONFDELCONFIGURATION: 'Bestätigen Sie das Löschen des Konfigurationen?',
      DODELCONFIGURATION: 'Möchten Sie den Konfiguration löschen',
      DELCONFIGURATIONSUCC: 'Konfiguration erfolgreich gelöscht',
      CONFIGURATIONID: 'Konfiguration (ID: ',
      CONFIGURATIONDELFAIL: 'Löschen des Konfigurationen fehlgeschlagen',
      CONFIGURATIONITEM: 'Die ausgewählten Konfigurationen enthalten einen oder mehrere aktive Zustandselemente',
      CONFDELSELECTCONFIGURATION: 'Bestätigen Sie das Löschen der ausgewählten Konfigurationen?',
      DODELSELECTCONFIGURATION: 'Möchten Sie die ausgewählten Konfigurationen löschen?',

      CONFPOLICY: 'Download-Richtlinie bestätigen?',
      DOWORKPOLICY: 'Möchten Sie die Richtlinie herunterladen?',
      POLICYID: 'Richtlinie (ID:',
      POLICYFLOWSUCC: 'Richtlinie erfolgreich aktualisiert',
      POLICY: 'Politik',
      POLICYCLONESUCCESS: 'Richtlinie erfolgreich geklont!',
      CONFDELPOLICY: 'Löschen der Richtlinie bestätigen?',
      DODELPOLICY: 'Möchten Sie die Richtlinie löschen?',
      DELPOLICYSUCC: 'Richtlinie erfolgreich gelöscht',
      POLICYDELFAIL: 'Löschen der Richtlinie fehlgeschlagen',
      PLEASEPOLICYS: 'Bitte zuerst Richtlinien auswählen!',
      DOSELECTPOLICY: 'Möchten Sie die ausgewählten Richtlinien als mehrere Dateien herunterladen?',
      DOSELECTPOLICYSONE: 'Möchten Sie die ausgewählten Richtlinien als eine Datei herunterladen?',
      CONFSELEPOLICY: 'Auswahl der Richtlinien zum Herunterladen bestätigen?',
      DOWNSELECTPOLICY: 'Die ausgewählten Richtlinien wurden erfolgreich heruntergeladen!',
      POLICYITEM: 'Die ausgewählten Richtlinien enthalten einen oder mehrere aktive Zustandsartikel',
      CONFDELSELECTPOLICY: 'Löschen der ausgewählten Richtlinien bestätigen?',
      DODELSELECTPOLICY: 'Möchten Sie die ausgewählten Richtlinien löschen?',
      DELSELECTPOLICY: 'Die ausgewählten Richtlinien wurden erfolgreich gelöscht!',

      CONPROFILE: 'Konfigurationsdatei herunterladen?',
      PROFILE: 'Konfigurationsdatei',
      DOPROFILE: 'Soll die Konfigurationsdatei heruntergeladen werden',
      CONOSELECT: 'Download der ausgewählten Profildatei bestätigen？',
      DOSELECTPROFILE: 'Möchten Sie das ausgewählte Arbeitsprofil in mehrere Dateien herunterladen?',
      DOSELECTPROFILEASONE: 'Möchten Sie das ausgewählte Arbeitsprofil als Datei herunterladen?',
      DOWNSELECTPROFILE: 'Die ausgewählten Profile wurden erfolgreich heruntergeladen!',

      PROVISIONINGFILE: "Dateidownload bestätigen?",
      PROVISIONINGCONFILE: "Download der Datei bestätigen?",
      PROVISIONINGSELECT: "Download der ausgewählten Datei bestätigen?",
      PROVISIONINGDOSELECTFILE: "Möchten Sie die ausgewählte Datei als mehrere Dateien herunterladen?",
      PROVISIONINGDOSELECTFILEONE: "Möchten Sie die ausgewählten Dateien als eine Datei herunterladen?",
      PROVISIONINGCONOSELECT: "Sind Sie sicher, dass Sie die ausgewählte Datei herunterladen möchten?",

      ENDGREATER: 'Das Enddatum sollte größer als das aktuelle Datum sein!',
      STARTHAVEVALUE: 'Das Startdatum oder das Enddatum sollte einen Wert haben!',
      ENDGREATERSTART: 'Das Enddatum sollte größer als das Startdatum sein!',
      STARTENDALUE: 'Die Startzeit oder die Endzeit sollte einen Wert haben!',
      EXACTLYEQUAL: 'Die Zeit kann nicht genau gleich sein!',
      CONFSTAGE: 'Bestätigen Sie die Entfernung der Stufe?',
      BADREQ: 'Schlechte Anfrage',
      PARAMNEED: 'Parameter müssen vor dem Speichern ausgefüllt werden',
      FLOWSUCC: 'Workflow erfolgreich aktualisiert',
      CONFIGURATIONSUCC: 'Konfiguration erfolgreich aktualisiert',
      WASUPDATE: ') wurde aktualisiert!',
      CROSSCLICK: 'Klicken Sie auf das Kreuz',
      FLOWADDSUCC: 'Workflow erfolgreich hinzugefügt',
      WASADD: ') wurde hinzugefügt!',
      FORMFAIL: 'Formularvalidierung fehlgeschlagen!',
      CONFOPER: 'Bestätigen Sie die Entfernung der Operation?',
      VERSIONERROR: 'Versionsfehler!',
      ONLY16: 'Es sind nur 1-16 Zeichen aus Buchstaben, Zahlen und Sonderzeichen ( _ - .) erlaubt.',
      FORKSUCC: 'Workflow erfolgreich verzweigt!',
      WASFORK: ' Das macht es unsicher. Neue priorität:',
      FLOWSPACE: 'Workflow ',
      OPERFORKSUCC: 'Operation erfolgreich verzweigt!',
      OPERFLOWSPACE: 'Operation ',
      OPERATION: ' Operation',
      CONFDELOPER: 'Löschen der einrichtungsdatei bestätigt?',
      DODELOPER: 'Soll die konfigurationsdatei gelöscht werden',
      DELSUCCESS: 'Erfolgreich gelöscht!',
      PLEOPERFIRST: 'Bitte wählen Sie zuerst Operationen aus!',
      CONFSELECTOPER: 'Bestätigen Sie, dass die ausgewählten Profile gelöscht werden?',
      DOSELOPER: 'Möchten Sie die ausgewählten Profile löschen?',
      DELOPERSUCC: 'Löschen Sie die ausgewählten Profile erfolgreich!',
      CONFACTIONRE: 'Schneide bestätigen?',
      STAGE: ' Stufe?',
      ACTION: ' Profile?',
      OPERUPDATESUCC: 'Profile erfolgreich aktualisiert',
      OPERADDSUCC: 'Profile erfolgreich hinzugefügt',
      ALARMADDSUCC: 'Alarmbenachrichtigung erfolgreich hinzugefügt',
      CONFLICT: 'Konflikt',
      ALARMNOTNAME: 'Alarmbenachrichtigung mit Namen ',
      ALREADYEXIST: ' existiert bereits',
      CONTAINDATA: ' enthält inkonsistente Daten',
      NAMEERROR: 'Namensfehler!',
      ONLYNAMESYM: 'Nur 1-64 Zeichen von Buchstaben, Zahlen und Sonderzeichen (_-) Leerzeichen) sind zulässig',
      CLONENOTI: 'Alarmbenachrichtigung erfolgreich geklont!',
      WANCLONE: ' wurde geklont. Neue Alarmbenachrichtigung: ',
      CONFIGUPDATESUCC: 'Konfiguration erfolgreich aktualisiert',
      SETOPERSUCC: 'Gerätebetrieb erfolgreich festgelegt!',
      SETOPERFAIL: 'Festlegen des Gerätebetriebs fehlgeschlagen!',
      TASKSUCC: 'Aufgabe erfolgreich hinzugefügt!',
      LABELSUCC: 'Label erfolgreich bearbeitet!',
      LABELFAIL: 'Bearbeiten des Labels fehlgeschlagen!',
      UPDATEDEV: 'Aktualisierung erfolgreich',
      CMDSENQUSUCC: 'Befehl erfolgreich in die Warteschlange gestellt!',
      FORKNOT: 'Alarmbenachrichtigung erfolgreich verzweigt!',
      WANIMPORT: ') wurde nicht importiert.',
      NOTIIMPORTFAIL: 'Import der Alarmbenachrichtigung fehlgeschlagen',
      IMPORTSUCC: 'Import erfolgreich!',
      GROUPCREATESUCC: 'Gerätegruppe erfolgreich erstellt',
      IMPORTTOGROUP: ' Geräte werden in die Gruppe importiert ',
      GNAMEEXIST: 'Dieser Gruppenname existiert bereits',
      SAVESRSSUCC: 'Einstellungen für den Zusammenfassungsbericht erfolgreich gespeichert',
      PLEASECONF: 'Das Thema kann nach der Einstellung nicht mehr geändert werden. Bitte bestätigen Sie',
      ADDPRODSUCC: 'Produkt erfolgreich hinzugefügt.',
      ADDPARAMSUCC: 'Parameter erfolgreich hinzugefügt.',
      UPDATEPRODSUCC: 'Produkt erfolgreich aktualisiert.',
      UPDATERANSUCC: 'Funkzugangsnetzwerk erfolgreich aktualisiert.',
      UPDATEAPNSUCC: 'WiFi AP Netzwerk erfolgreich aktualisiert.',
      UPDATEMESHSUCC: 'WiFi Mesh Netzwerk erfolgreich aktualisiert.',
      UPDATEPARAMSUCC: 'Parameter erfolgreich aktualisiert.',
      PLEASEFILL: 'Bitte füllen Sie zuerst die erforderlichen Felder mit Sternchen aus!',
      UPDATEPERM: 'Aktualisierung des Berechtigungstyps erfolgreich',
      UPDATEPERMDEV: 'Aktualisierung der Geräteberechtigungen erfolgreich',
      ADDSUCC: 'Hinzufügen erfolgreich',
      UPDATESUCC: 'Aktualisierung erfolgreich',
      DONE: 'erledigt',
      WARNING: 'Warnung!',
      PARAMNAMEEXIST: 'Der Parametername existiert bereits!',
      SCRITEMPTY: 'Die Skriptliste ist leer!',
      USPGROUPEMPTY: 'Die Gruppenliste ist leer!',
      GROUPNAMEREQ: 'Der Gruppenname ist erforderlich',
      OPERMODEREQ: 'Der Betriebsmodus ist erforderlich',
      FLOWIMPORTFAIL: 'Import des Workflows fehlgeschlagen',
      SYSSETSUCC: 'Erfolgreich Systemeinstellungen speichern!',
      SYSSETFAIL: 'Speichern der Systemeinstellungen fehlgeschlagen!',
      DEVSETSUCC: 'Geräteeinstellungen erfolgreich speichern!',
      DEVSETFAIL: 'Speichern der Geräteeinstellungen fehlgeschlagen!',
      PROSETSUCC: 'Produkteinstellungen erfolgreich speichern!',
      PROSETFAIL: 'Speichern der Produkteinstellungen fehlgeschlagen!',
      SYSPRESUCC: 'Systempräferenz erfolgreich speichern!',
      SYSPREFAIL: 'Speichern der Systempräferenz fehlgeschlagen!',
      DELETEUSER: 'Benutzer löschen ',
      CHANGEUSER: 'Benutzer ändern ',
      SPFAIL: ' fehlgeschlagen',
      ACTIVESUSS: ' Aktivstatus erfolgreich',
      ACTIVEFAIL: ' Aktivstatus fehlgeschlagen',
      IMAGEOVERSIZE: 'Das Bild ist zu groß.',
      PWDNOTSAME: 'Das Passwort stimmt nicht überein.',
      PWDREQ: 'Das Passwort ist erforderlich.',
      FORMATINCORRECT: 'Das Ablaufdatum hat ein falsches Format.',
      MUSTADMIN: 'Das Produkt des ADMIN/CSR-Benutzers muss "ADMIN" sein.',
      PRODUCTREQ: 'Das Produkt ist erforderlich.',
      SELECTADMIN: 'Nur die ADMIN-Rolle kann "ADMIN" auswählen.',
      ROLEREQ: 'Die Benutzerrolle ist erforderlich.',
      AVATARSUCC: 'Das Avatar wurde erfolgreich aktualisiert.',
      AVATARFAIL: 'Fehler beim Aktualisieren des Avatars.',
      EMAILSUCC: 'Erfolgreich aktualisierte E-Mail',
      EMAILFAIL: 'E-Mail-Aktualisierung fehlgeschlagen.',
      UPPERLIMIT: 'Die Anzahl der hinzufügbaren Geräte hat das obere Limit überschritten!',
      MAXLIMIT: 'Das maximale Gerätelimit beträgt ',
      NOTSAVETITLE: 'Möchten Sie Ihre Änderungen speichern?',
      NOTSAVECONTENT: 'Die von Ihnen vorgenommenen Änderungen werden nicht gespeichert.',
      CONFIRMROLE: 'Zum Löschen der Rolle bestätigen?',
      DOROLE: 'Möchten Sie die Rolle löschen?',
      DOSELECTROLE: 'Möchten Sie die ausgewählte Rolle löschen?',
      DELROLESUCC: 'Rollen erfolgreich gelöscht.',
      ROLEUPSUCC: 'Rolle erfolgreich aktualisiert.',
      ROLEADDSUCC: 'Rolle erfolgreich hinzugefügt.',
      CHANGEWILLBELOSE: "Ihre Änderungen gehen verloren, wenn Sie sie nicht speichern.",
      SYSTEMRETURNLOGIN: "Das System kehrt zur Anmeldeseite zurück",
      SAVEEVENTTIP: 'Bitte wählen Sie „Ereignis informieren“, bevor Sie in dieser Phase speichern!',
      SAVENOTIFYORDEVICEPARAMTIP: 'Bitte fügen Sie vor dem Speichern in dieser Phase einen Benachrichtigungsparameter oder eine Geräteparameterbedingung hinzu!',
      SAVEDEVICEFAULTTIP: 'Bitte fügen Sie die Gerätefehlerparameterbedingung vor dem Speichern in dieser Phase hinzu!',
      ADDMEMBERSUC: 'Gruppenmitglieder erfolgreich hinzugefügt!',
      ADDMEMBERFAIL: 'Das Hinzufügen von Gruppenmitgliedern ist fehlgeschlagen!',
      SMTPHEALTHY: 'E-Mail-Erfolg testen.',
      SNMPHEALTHY: 'Test SNMP Trap Erfolg.',
      XMPPHEALTHY: 'XMPP-Erfolg testen.',
      GENSERVERREPORT: 'Serverbericht erfolgreich generiert.',
      WORKFLOWCLONESUCCESS: 'Workflow erfolgreich geklont!',
      CONFIGURATIONWCLONESUCCESS: 'Konfiguration erfolgreich geklont!',
      HASBEENCLONED: 'wurde geklont!',
      HASBEENFORKED: 'wurde gefaltet!',
      APNAMEEDITFAIL: 'AP Name Bearbeitung fehlgeschlagen.',
      ADDAPNSUCC: 'WiFi-AP-Netzwerk erfolgreich hinzugefügt, und eine Gruppe mit demselben Namen wurde gleichzeitig erstellt.',
      ADDRANSUCC: 'Funkzugangsnetzwerk erfolgreich hinzugefügt, und eine Gruppe mit demselben Namen wurde gleichzeitig erstellt.',
      ADDMESHSUCC: 'WiFi-Mesh-Netzwerk erfolgreich hinzugefügt, und eine Gruppe mit demselben Namen wurde gleichzeitig erstellt.',
      TAGERROR: 'Es sind nur 0 bis 32 Zeichen aus Buchstaben, Zahlen, -, _, . und Leerzeichen erlaubt.'
    },
    PM: {
      PMWORD: 'PM',
      PMPARAM: 'PM-Parameter',
      PMCHART: 'PM-Diagramm',
      PERFORMANCEREPORT: 'Leistungsbericht',
      PMSTATEITEMS: 'Die ausgewählten Metrik-IDs enthalten ein oder mehrere Tracking-Elemente!',
      PERFORMANCEREPORT_DESCRIPTION: "Liste der generierten Server (AMP) Berichte mit detaillierten Informationen wie dem Status von Geräten und Diensten.",
      PMSTATISTICS: 'PM Statistiken',
      SERIALNUMBER: 'Seriennummer',
      TARGETSERIALNUMBER: 'Ziel-Seriennummer',
      TARGETSN: 'Ziel-Seriennummer',
      TARGETGROUP: "Zielgruppe",
      TARGET: "Ziel",
      GROUP: 'Gruppe',
      PARAMNAME: 'Parametername',
      PARAMPATH: 'Parameterpfad',
      CONDITION: 'Bedingung',
      CONDITIONS: 'Bedingungen',
      PARAMVALUE: 'Parameterwert',
      FROM: 'Von',
      TO: 'Bis',
      CREATEDBY: 'Erstellt von',
      BEGINTIME: 'Startzeit',
      ENDTIME: 'Endzeit',
      TIME: 'Zeit',
      UPDATETIME: 'Aktualisierungszeit',
      MODELNAME: 'Modellname',
      PRODUCTCLASS: 'Produktklasse',
      TIMERANGE: 'Zeitbereich',
      OUI: 'OUI',
      METRICRULE: 'Metrikregel',
      ALL: 'Alle',
      CONFIRM_DELETE: 'Löschen bestätigen',
      DO_DELETE: 'Möchten Sie ausgewählte löschen ',
      DODELETE: 'Möchten Sie löschen ',
      DELETESUCCESS: 'Löschen erfolgreich',
      DELETEFAIL: 'Löschen fehlgeschlagen',
      PLESESELECT: 'Bitte auswählen ',
      CONFIRM_REFRESH: 'Bestätigen Sie die erneute Suche',
      DO_REFRESHSELECT: 'Möchten Sie die ausgewählten Elemente erneut durchsuchen',
      DO_REFRESH: 'Möchten Sie erneut suchen',
      REFRESHSUCCESS: 'Suche erfolgreich erneut durchgeführt',
      REFRESHFAIL: 'Suche erneut fehlgeschlagen',
      EXPIREDUPDATESUCCESS: "Alle abgelaufenen Daten wurden erfolgreich aktualisiert.",
      DATAEXPIRED: "Daten abgelaufen",
      ADDCONDITION: 'Bedingung hinzufügen',
      DELETECONDITION: 'Bedingung löschen',
      SEARCH: 'Suchen',
      REFRESH: 'Aktualisieren',
      REFRESHALL: "Alles aktualisieren",
      SEARCHRESULT: 'Suchergebnis',
      VIEWCHART: 'Diagramm anzeigen',
      CHART: 'Diagramm',
      SAVERULE: 'Regelname speichern',
      UPDATERULE: 'Regelname aktualisieren',
      NAME: 'Name',
      DESCRIPTION: 'Beschreibung',
      CLOSE: 'Schließen',
      SAVE: 'Speichern',
      DELETE: 'Löschen',
      DELETEALL: 'Alle löschen',
      GOTDEVICEINFO: 'Zur Geräteinformationsseite wechseln für ',
      VIEWALLCHART: "Bitte wählen Sie Geräte aus, um Leistungsdiagramme anzuzeigen. (max.: 20)",
      DURATION: "Dauer",
      NOTIFICATIONTOOLTIP: 'In Benachrichtigung umwandeln',
      REFRESHRULETOOLTIP: 'Suchregel erneut durchsuchen',
      DEVICECOUNT: 'Geräte',
      FAIL: 'Fehlgeschlagen!',
      REFRESHLOADING: 'Suche erneut lädt...',
      DOWNLOAD: 'Herunterladen',
      CONFIRM_DOWNLOAD: 'Download bestätigen',
      DOWNLOADSUCCESS: 'Download erfolgreich',
      DOWNLOADFAIL: 'Download fehlgeschlagen',
      DO_DOWNLOAD: 'Möchten Sie die ausgewählten Geräte herunterladen?',
      DODOWNLOAD: 'Möchten Sie herunterladen ',
      OPENSEARCHBAR: 'Suchleiste öffnen',
      HIDESEARCHBAR: 'Suchleiste ausblenden',
      LASTMACHINGTIME: 'Letzte Übereinstimmungszeit',
      RESEARCH: 'Neue Suche',
      RESEARCHRULETOOLTIP: 'Neue Suchregel',
      TRACKING: 'Verfolgung',
      RESULT: 'Ergebnis',
      RESEARCHALL: 'Alle erneut durchsuchen',
      RESULTTOOLTIP: 'Anzahl der Geräte, die die Bedingung erfüllen',
      LASTMACHING: "Letzte Übereinstimmung",
    },
    REFURBISHMENT: {
      REFURBISHMENTSTATISTICS: 'Sanierungsstatistiken',
      REFURBISHMENTTIME: 'Sanierungszeit',
      REFURBISHMENTCOUNT: 'Anzahl der Sanierungen',
      INSTALLATIONTIME: 'Installationszeit',
    },
    CARE: {
      TITLE: 'care',
      GENERALINFO: 'General Info',
      GENERALINFODESCRIPTION: 'Allgemeine Informationen zum Gerät.',
      MAP: 'Standort',
      MAPDESCRIPTION: 'Standort des Geräts auf Google Map.',
      WIFICLIENTLIST: 'Liste der WLAN-Clients',
      WIFICLIENTLISTDESCRIPTION: 'WLAN-Clients stellen eine Verbindung zum Gerät her.',
      ERRORSTATUS: 'Fehlerstatus',
      ERRORSTATUSDESCRIPTION: 'Fehlerliste des Geräts inklusive Ereigniscode, Fehlerbeschreibung und Fehlerzeit.',
      ERRORSTCARE: "Besorgen Sie sich das Gerät",
      SELECTDEVICE: "Gerät auswählen",
      SERIALNUMBER: "Seriennummer",
      PRODUCTNAME: "Produktname",
    },
    FIVEGC: {
      CELL_CONNECTED: "Zelle Verbunden",
      CELL_CONNECTED_DESCRIPTION: "Zeigt die Anzahl der verbundenen und funktionierenden Funkgeräte an.",
      CELL_DISCONNECTED: "Zelle Getrennt",
      CELL_DISCONNECTED_DESCRIPTION: "Zeigt die Anzahl der getrennten Funkgeräte an.",
      ACTIVE_UE: 'Aktive UE',
      ACTIVE_UE_DESCRIPTION: 'Anzahl der Zellen mit aktiven UE.',
      NO_ACTIVE_UE: 'Inaktive UE',
      NO_ACTIVE_UE_DESCRIPTION: 'Zeigt die Anzahl der Zellen ohne Aktivität. Dies bedeutet nicht unbedingt, dass keine UE angehängt sind; sie könnten angehängt, aber nicht aktiv sein.',
      CELLS_WITHOUT_ATTACHED_UE: 'Zellen ohne angehängte UE',
      CELLS_WITHOUT_ATTACHED_UE_DESCRIPTION: 'Zeigt die Anzahl der Funkgeräte an, die verbunden sind, aber keine angehängten UE haben.',
      CELLS_WITH_ACTIVE_UE: "Zellen mit Aktiven UEs",
      CELLS_WITH_ACTIVE_UE_DESCRIPTION: "Dieses Widget zeigt eine Reihe von Aktivitätsindikatoren in einem Balkendiagrammformat an, das den Aktivitätsstatus der Zellen in verschiedenen Bereichen anzeigt, einschließlich keine Aktivität, 1 bis 5 aktive UEs, 6 bis 10 aktive UEs, 11 bis 20 aktive UEs und 20 oder mehr aktive UEs.",
      CELLS_LIST: "Zellenliste",
      CELLS_LIST_DESCRIPTION: "Dieses Widget zeigt eine Liste von Zellen an, die mit dem 5G-Kern verbunden sind.",
      ALARM_LIST: "Alarmliste",
      ALARM_LIST_DESCRIPTION: "Die Alarmliste liefert Informationen über die verschiedenen Alarme im System.",
      FIVECORENETWORK: "5G Kernnetzwerk",
      FIVECORENETWORK_DESCRIPTION: "Dieses Widget dient als Link zum 5G-Kern, der in den Systemeinstellungen mit der 5G-Kern-URL konfiguriert ist.",
      CELL_THROUGHPUT: "Zellen-Durchsatz",
      CELL_THROUGHPUT_DESCRIPTION: "Dieses Widget zeigt den aktuellen Download- und Upload-Durchsatz der Zelle an.",
      UE_THROUGHPUT_BY_CELL: "UE-Durchsatz pro Zelle",
      UE_THROUGHPUT_BY_CELL_DESCRIPTION: "UE-Durchsatz pro Zelle zeigt die gesamte Download- und Upload-Durchsatzrate aller mit einer bestimmten Zelle verbundenen UEs an und bietet einen Überblick über die Datenübertragungsleistung.",
      UE_LIST: "UE-Liste",
      UE_LIST_DESCRIPTION: "Die UE-Informationen bieten detaillierte Informationen über das an das 5G-Kernnetzwerk angeschlossene Benutzergerät (UE).",
      UE_5QI_PACKET: "UE 5QI Paket",
      UE_5QI_PACKET_DESCRIPTION: "Dieses Widget zeigt die 5QI-Pakete des UE an, einschließlich verschiedener Verkehrsmesswerte und Verlustquoten für den Uplink und Downlink.",
      ACTIVE: 'Aktiv',
      INACTIVE: 'Inaktiv',
      STATUS: 'Status',
      NAME: 'Name',
      STATE: 'Zustand',
      gNBID: 'gNB ID',
      BAND: 'Band',
      SESSIONS: 'Sitzungen',
      DATA: 'Daten',
      PLMN: 'PLMN',
      TAC: 'TAC',
      IP: 'IP',
      SEVERITY: 'Schweregrad',
      ALARM_ID: 'Alarm ID',
      EVENT_TYPE: 'Ereignistyp',
      EVENT_TIME: 'Ereigniszeit',
      PROBABLE_CAUSE: 'Wahrscheinliche Ursache',
      SPECIFIC_PROBLEM: 'Spezifisches Problem',
      OBJ_CLASS: "Objektklasse",
      ADD_TEXT: "Text hinzufügen",
      THROUGHPUT_HISTORY: "Durchsatzverlauf",
      STATUS_HISTORY: "Statusverlauf",
      HISTORY: 'Geschichte',
      IMSI: 'IMSI',
      PHONENUMBER: "Telefonnummer",
      IMEI: 'IMEI',
      SUB_TYPE: 'Untertyp',
      REG_TYPE: "Registrierungstyp",
      LOCAL_ATTACHMENT: 'Lokale Verbindung',
      LAST_ACTIVITY_TIME: "Letzte Aktivität",
      REGISTRATION_TIME: "Registrierungszeit",
      DEREGISTRATION_TIME: "Abmeldezeit",
      UL_THROUGHPUT: 'UL-Durchsatz',
      DL_THROUGHPUT: 'DL-Durchsatz',
      SUPI: 'SUPI',
      FIVEQI: '5QI',
      UL_INGRESS: 'UL-Eingang',
      UL_EGRESS: 'UL-Ausgang',
      UL_DROPPED: 'UL-Verlust',
      UL_TOTAL_INGRESS: 'Gesamt-UL-Eingang',
      UL_TOTAL_EGRESS: 'Gesamt-UL-Ausgang',
      UL_TOTAL_DROPPED: 'Gesamt-UL-Verlust',
      DL_INGRESS: 'DL-Eingang',
      DL_EGRESS: 'DL-Ausgang',
      DL_DROPPED: 'DL-Verlust',
      DL_TOTAL_INGRESS: 'Gesamt-DL-Eingang',
      DL_TOTAL_EGRESS: 'Gesamt-DL-Ausgang',
      DL_TOTAL_DROPPED: 'Gesamt-DL-Verlust',
      ALARM: 'Alarm',
      ALARM_DESCRIPTION: 'Die neuesten Alarme aus dem 5G-Core-Netzwerk in Echtzeit. Sie umfassen die Alarmstufe, den Zeitstempel und eine kurze Beschreibung, die eine schnelle Identifizierung und Reaktion auf Netzwerkprobleme ermöglichen.',
      ue_activity: 'UE-Aktivität des 5G-Kerns',
      ue_activity_DESCRIPTION: 'Dieses Kuchendiagramm veranschaulicht den Aktivitätsstatus der Benutzerausrüstung (UE).',
      ue_presence: 'UE-Präsenz des 5G-Kerns',
      ue_presence_DESCRIPTION: 'Zeigt die Anzahl der angeschlossenen (verbundenen) und abgetrennten (getrennten) UE an.',
      cells_info: 'Zellen des 5G-Kerns',
      cells_info_DESCRIPTION: 'Die Zellen des 5G-Kerns liefern detaillierte Informationen über die mit dem 5G-Kernnetzwerk verbundenen Zellen.',
      ACTIVITY_DESCRIPTION: {
        ACTIVE: 'Das aktive Segment zeigt die Anzahl der aktiven, angeschlossenen UE.',
        DATA: 'Das Datensegment zeigt die Anzahl der Datensitzungen im System. Ein UE kann mehrere Datensitzungen haben.',
        CALLS: 'Das Anrufsegment zeigt die Anzahl der momentan in einem Anruf befindlichen angeschlossenen UE.',
        INACTIVE: 'Das inaktive Segment zeigt die Anzahl der nicht aktiven, angeschlossenen UEs.'
      },
      PRESENCE_DESCRIPTION: {
        ATTACHED: 'Der Zustand "Angehängt" zeigt an, dass das Benutzergerät erfolgreich mit dem Netzwerk verbunden ist.',
        DETACHED: 'Der Zustand "Abgetrennt" zeigt an, dass das Benutzergerät vom Netzwerk getrennt ist oder noch nicht damit verbunden war.'
      },
      LICENSE_DESCRIPTION: {
        UE: 'Zeigt die Anzahl der in Gebrauch befindlichen UE-Lizenzplätze und die Anzahl der verfügbaren UE-Lizenzplätze.',
        NETWORK: 'Zeigt die Anzahl der PDN-Lizenzplätze und die Anzahl der verfügbaren PDN-Lizenzplätze.',
        CELLS_4G: 'Die Anzahl der in Gebrauch befindlichen 4G-Zellenlizenzplätze und die Gesamtanzahl der verfügbaren 4G-Zellenlizenzplätze.',
        CELLS_5G: 'Die Anzahl der in Gebrauch befindlichen 5G-Zellenlizenzplätze und die Gesamtanzahl der verfügbaren 5G-Zellenlizenzplätze.',
      },
      ACTIVITY_CHART_DESCRIPTION: {
        ACTIVE_20PLUS: "Gibt die Anzahl der Zellen mit 20 oder mehr aktiven Nutzern an.",
        ACTIVE_11To20: "Gibt die Anzahl der Zellen mit 11 bis 20 aktiven Nutzern an.",
        ACTIVE_6To10: "Gibt die Anzahl der Zellen mit 6 bis 10 aktiven Nutzern an.",
        ACTIVE_1To5: "Gibt die Anzahl der Zellen mit 1 bis 5 aktiven Nutzern an.",
        NOACTIVE: "Gibt die Anzahl der Zellen ohne Aktivität an. Dies bedeutet nicht unbedingt, dass keine Nutzer verbunden sind; sie könnten verbunden, aber inaktiv sein."
      },
      ACTION: {
        RESTARTSYSTEM: 'System neu starten',
        DORESTARTSYSTEM: 'Wenn Sie fortfahren, werden ALLE DIENSTE VORÜBERGEHEND VERLOREN GEHEN!',
        RESTARTSYSTEM_SUCESS: 'System erfolgreich neu gestartet!',
        RESTARTSYSTEM_FAIL: 'Systemneustart fehlgeschlagen!',
        BACKUPCONFIGURATION: 'Konfiguration sichern',
        BACKUPCONFIGURATION_SUCESS: 'Konfiguration erfolgreich gesichert!',
        BACKUPCONFIGURATION_FAIL: 'Konfigurationssicherung fehlgeschlagen!',
        RESTORECONFIGURATION: 'Konfiguration wiederherstellen',
        RESTORECONFIGURATION_SUCESS: 'Konfiguration erfolgreich wiederhergestellt!',
        RESTORECONFIGURATION_FAIL: 'Konfigurationswiederherstellung fehlgeschlagen!',
        FACTORYRESET: 'Auf Werkseinstellungen zurücksetzen',
        DOFACTORYRESET: 'Sie sind dabei, Ihre Konfiguration auf die Werkseinstellungen zurückzusetzen!',
        FACTORYRESET_SUCESS: 'Auf Werkseinstellungen erfolgreich zurückgesetzt!',
        FACTORYRESET_FAIL: 'Zurücksetzen auf Werkseinstellungen fehlgeschlagen!',
        REFRESHALL: "Alle aktualisieren",
        REFRESHALL_SUCESS: "Alle erfolgreich aktualisiert!",
        REFRESHALL_FAIL: "Alle Aktualisierungen fehlgeschlagen!",
        SYSTEMMANAGEMENT: "Systemverwaltung",
      },
    },
    POWER: {
      ENERGYSAVING: 'Energieverwaltung',
      STARTTIME_MUST_BE_EARLIER: "Die Startzeit muss vor der Endzeit liegen",
      STARTDATE_MUST_BE_EARLIER: "Das Startdatum darf nicht nach dem Enddatum liegen.",
      POWER_CONSUMPTION_SUMMARY: "Stromverbrauch Übersicht",
      REAL_AVG_ENERGY: "Reale Durchschnittsenergie",
      NORMAL_STATE_ENERGY: "Energie im Normalzustand",
      ENERGY_CONSUMPTION_BY_POLICY: "Energieverbrauch nach Richtlinie",
      POWER_CONSUMPTION: "Stromverbrauch",
      TX_POWER: "Sendeleistung",
      NETWORK_USAGE: "Netzwerknutzung",
      UPLOAD: "Hochladen",
      DOWNLOAD: "Herunterladen",
      UE_COUNT: "Anzahl verbundener Geräte",
      LOCATION: "Standort",
      CURRENT_POLICY: "Aktuelle Richtlinie",
      POLICY_SETTING: "Richtlinieneinstellung",
      ENERGY_POLICY: "Energiepolitik",
      MILD_SLEEP_DESCRIPTION: "Reduziert die Sendeleistung bei voller Betriebsbereitschaft des Geräts.",
      MODERATE_SLEEP_DESCRIPTION: "Reduziert die Leistung und deaktiviert vorübergehend das Funkmodul, um Energie zu sparen.",
      WAKEABLE_DEEPSLEEP_DESCRIPTION: "Fährt das Gerät schrittweise herunter, erlaubt jedoch ein automatisches Aufwachen bei Bedarf.",
      DEEPSLEEP_DESCRIPTION: "Fährt das Gerät vollständig herunter. Ein manuelles Aufwecken ist erforderlich.",
      SCHEDULE_SETTING: "Zeitplaneinstellung",
      POLICY_LIST: "Richtlinienliste",
      DURATION: "Dauer",
      ENERGY: "Stromverbrauch",
      POWER_CONSUMPTION_PER_DEVICE: "Stromverbrauch pro Gerät",
      DL_PRB_LOADING: "DL PRB-Auslastung",
      UL_PRB_LOADING: "UL PRB-Auslastung",
      TOTAL_POWER_CONSUMPTION: "Gesamtstromverbrauch",
      NAME: "Name",
      SCHEDULE: "Zeitplan",
      CONDITION: "Bedingung",
      ACTIVE: "Aktiv",
      NO_POLICIES_FOUND: "Keine Richtlinien gefunden.",
      NO_POLICY_CONFIGURED: "Für dieses Gerät wurde keine Energiesparrichtlinie konfiguriert.",
      ENABLE_TO_ADD_POLICIES: "Sie können Richtlinien hinzufügen, sobald die Energiesparsteuerung aktiviert ist.",
      ENERGY_SAVING_NOT_ENABLED: "Energiesparmodus ist nicht aktiviert.",
      NO_POLICY_SETTINGS_AVAILABLE: "Für dieses Gerät sind keine Richtlinieneinstellungen verfügbar.",
      ENABLE_TO_CONFIGURE_POLICY: "Bitte aktivieren Sie die Energiesparsteuerung, um Richtlinien zu konfigurieren oder anzuzeigen.",
      ENERGY_MODE: "Energiesparmodus",
      TRAFFIC_LOADING: "Datenverkehrslast",
      UE_CONTEXT: "UE-Kontext",
      POLICY_DISABLE_TITLE: "Energiesparmodus deaktivieren?",
      POLICY_DISABLE_TEXT: "Nach der Deaktivierung kehrt das Gerät zu den normalen Energieeinstellungen zurück. Möchten Sie es wirklich deaktivieren?",
    }
  }
}
